# HCI Asyn

## 项目说明
HCI Asyn 是一个基于 Celery 的异步任务处理系统，用于处理 HCI 平台的各种异步任务。

## Celery Worker 启动工具

`agent_runner.py` 是一个用于启动 Celery Worker 的工具，支持多种启动模式。

### 使用方法

1. Agent 模式启动（用于运行 agent 相关任务）：
```bash
python agent_runner.py agent --queue queue_192.168.213.31
```

2. 服务端模式启动（用于运行服务器端任务）：
```bash
python agent_runner.py server
```

3. 自定义任务包启动：
```bash
python agent_runner.py custom --include app.tasks.vm_tasks,agents.tasks
```

### 参数说明

所有模式都支持的通用参数：
- `--concurrency`, `-c`: 并发 worker 数（默认：2）
- `--loglevel`: 日志级别（默认：info）
- `--max-tasks`: 每个子进程最大处理任务数（默认：2）

Agent 模式特有参数：
- `--queue`, `-Q`: 指定队列名称（必需）

自定义模式特有参数：
- `--include`: 指定要加载的任务包，逗号分隔（必需）
- `--queue`, `-Q`: 指定队列名称（可选）

### 示例

1. 启动 agent worker，使用 4 个并发进程：
```bash
python agent_runner.py agent --queue queue_192.168.213.31 -c 4
```

2. 启动服务端 worker，设置日志级别为 debug：
```bash
python agent_runner.py server --loglevel debug
```

3. 启动自定义任务包，指定队列：
```bash
python agent_runner.py custom --include app.tasks.vm_tasks,agents.tasks --queue custom_queue
```

### 注意事项

1. Agent 模式会加载以下任务模块：
   - app.agents.disk_tasks
   - app.agents.network_tasks

2. 服务端模式会加载以下任务模块：
   - app.tasks.disk_tasks
   - app.tasks.cdrom_tasks
   - app.tasks.vm_tasks
   - app.tasks.network_tasks
   - app.tasks.storage_tasks

3. 确保在运行前已正确配置 Redis 或 RabbitMQ 连接信息。

## 环境设置

设置 Python 路径：
```bash
export PYTHONPATH="/root/hci/hci_api:/root/hci/hci_db:/root/hci/hci_asyn"
```

## 其他说明

关于龙芯平台的支持，请参考 `Readme.md` 文件。 