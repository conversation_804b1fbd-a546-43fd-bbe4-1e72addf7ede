#!/bin/bash

# 设置 MySQL 连接参数
# MYSQL_USER="root"
# MYSQL_PASS="thecloud2015.1"
# MYSQL_HOST="**************"

# 检查数据库是否存在
db_exists=$(mysql -u${MYSQL_USER} -p${MYSQL_PASS} -h${MYSQL_HOST} -e "SHOW DATABASES LIKE 'hci_db'" | grep -c 'hci_db')

if [ $db_exists -eq 0 ]; then
    # 数据库不存在，创建数据库
    echo "Creating database hci_db..."
    mysql -u${MYSQL_USER} -p${MYSQL_PASS} -h${MYSQL_HOST} -e "CREATE DATABASE IF NOT EXISTS hci_db"
fi

# 尝试升级，如果失败再尝试版本控制
echo "Attempting upgrade..."
if ! python3 db/manage.py upgrade; then
    echo "Upgrade failed, attempting version control..."
    python3 db/manage.py version_control || true  # 即使版本控制失败也继续
    echo "Attempting upgrade again..."
    python3 db/manage.py upgrade
fi
