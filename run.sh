#!/bin/bash

# 获取当前脚本所在的目录的父目录
PARENT_DIR=$(dirname $(cd "$(dirname "$0")"; pwd))

#启动云平台 定时任务
function run_the_beat() {
	#source /maojj/myvenv/bin/activate
	export PYTHONPATH=$PARENT_DIR/hci_api:$PARENT_DIR/hci_db
	celery -A app beat
}

#启动云平台 worker
function run_the_work() {
	#source /maojj/myvenv/bin/activate
	export PYTHONPATH=$PARENT_DIR/hci_api:$PARENT_DIR/hci_db
	celery -A app worker --loglevel=INFO -Q default
}

#启动云桌面 定时任务
function run_desk_beat() {
	#source /maojj/myvenv/bin/activate
	export PYTHONPATH=$PARENT_DIR/hci_api:$PARENT_DIR/hci_db
	celery -A app beat
}

#启动云桌面 worker
function run_desk_work() {
	#source /maojj/myvenv/bin/activate
	export PYTHONPATH=$PARENT_DIR/hci_api:$PARENT_DIR/hci_db
	celery -A app worker --loglevel=INFO -Q desktop
}

# 启动指定IP队列的worker
function run_ip_queue_work() {
	#source /maojj/myvenv/bin/activate
	export PYTHONPATH=$PARENT_DIR/hci_api:$PARENT_DIR/hci_db
	
	IP_QUEUE="**************"
	BROKER_HOST="${2:-localhost}"
	
	# 设置BROKER URL - 使用Redis
	export CELERY_BROKER_URL="redis://$BROKER_HOST:6379/0"
	
	celery  --broker "$CELERY_BROKER_URL" -A app worker --loglevel=INFO -Q "$IP_QUEUE" 
}

# 支持自定义队列和broker地址的 worker 启动
function run_queue_work() {
	#source /maojj/myvenv/bin/activate
	export PYTHONPATH=$PARENT_DIR/hci_api:$PARENT_DIR/hci_db
	
	QUEUE_NAME="$2"
	BROKER_HOST="${3:-localhost}"
	
	# 设置BROKER URL - 使用Redis
	export CELERY_BROKER_URL="redis://$BROKER_HOST:6379/0"
	
	celery  --broker "$CELERY_BROKER_URL" -A app worker --loglevel=INFO -Q "$QUEUE_NAME" 
}

# 根据参数运行不同的函数
if [ "$1" == "beat" ]; then
  run_the_beat
elif [ "$1" == "worker" ]; then
  run_the_work
elif [ "$1" == "deskbeat" ]; then
  run_desk_beat
elif [ "$1" == "deskworker" ]; then
  run_desk_work
elif [ "$1" == "ipqueue" ]; then
  run_ip_queue_work "$@"
elif [ "$1" == "queue" ] && [ -n "$2" ]; then
  run_queue_work "$@"
else
  echo "用法: $0 [选项] [参数]"
  echo "选项:"
  echo "  beat                  启动云平台定时任务"
  echo "  worker                启动云平台worker (默认队列)"
  echo "  deskbeat              启动云桌面定时任务"
  echo "  deskworker            启动云桌面worker"
  echo "  ipqueue [地址]        使用**************作为队列名称启动worker"
  echo "  queue <队列名> [地址] 指定队列和broker地址启动worker"
  echo ""
  echo "示例:"
  echo "  $0 ipqueue                    使用**************作为队列名称启动worker，使用本地Redis"
  echo "  $0 ipqueue ***************    使用**************作为队列名称启动worker，连接到指定Redis"
  echo "  $0 queue default              使用default队列启动worker，使用本地Redis"
fi







