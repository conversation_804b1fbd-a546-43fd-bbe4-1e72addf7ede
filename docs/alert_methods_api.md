# 告警方式API接口文档

## 概述

告警方式功能支持邮件、钉钉、Webhook、短信、企业微信等多种告警通知方式。所有配置参数都作为独立的数据库字段，前端可以直接修改这些字段来配置告警方式。

## 数据库字段说明

### 基本字段
- `id`: 主键ID
- `name`: 告警方式名称
- `method_type`: 告警方式类型 (email, dingtalk, webhook, sms, wechat)
- `description`: 告警方式描述
- `is_enabled`: 是否启用 (1启用, 0禁用)
- `is_default`: 是否为默认告警方式 (1是, 0否)
- `order_id`: 排序ID
- `status`: 状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 邮件告警字段
- `smtp_from`: SMTP发件人邮箱
- `smtp_smarthost`: SMTP服务器地址:端口
- `smtp_auth_username`: SMTP认证用户名
- `smtp_auth_password`: SMTP认证密码
- `smtp_require_tls`: 是否需要TLS (1需要, 0不需要)
- `email_to`: 邮件接收人，多个用逗号分隔
- `email_subject`: 邮件主题模板
- `email_body`: 邮件内容模板

### 钉钉告警字段
- `dingtalk_webhook_url`: 钉钉机器人Webhook地址
- `dingtalk_secret`: 钉钉机器人加签密钥
- `dingtalk_at_mobiles`: 钉钉@手机号，多个用逗号分隔
- `dingtalk_at_all`: 是否@所有人 (1是, 0否)
- `dingtalk_title`: 钉钉消息标题模板
- `dingtalk_content`: 钉钉消息内容模板

### Webhook告警字段
- `webhook_url`: Webhook地址
- `webhook_method`: HTTP方法 (POST, PUT等)
- `webhook_headers`: HTTP请求头，JSON格式
- `webhook_body`: HTTP请求体模板
- `webhook_timeout`: 超时时间(秒)

### 短信告警字段
- `sms_api_url`: 短信API地址
- `sms_api_key`: 短信API密钥
- `sms_api_secret`: 短信API密钥
- `sms_template_id`: 短信模板ID
- `sms_sign_name`: 短信签名
- `sms_phone_numbers`: 短信接收号码，多个用逗号分隔
- `sms_content`: 短信内容模板

### 企业微信告警字段
- `wechat_webhook_url`: 企业微信机器人Webhook地址
- `wechat_mentioned_list`: 企业微信@用户列表，多个用逗号分隔
- `wechat_mentioned_mobile_list`: 企业微信@手机号列表，多个用逗号分隔
- `wechat_title`: 企业微信消息标题模板
- `wechat_content`: 企业微信消息内容模板

## API接口

### 1. 获取告警方式列表

```http
POST /v1/alertsmethods/all
Content-Type: application/json

{
    "page": 1,
    "pagecount": 10,
    "order_by": "created_at",
    "order_type": "desc",
    "search_str": ""
}
```

**响应示例:**
```json
{
    "total": 5,
    "pages": 1,
    "current_page": 1,
    "data": [
        {
            "id": 1,
            "name": "生产环境邮件告警",
            "method_type": "email",
            "description": "生产环境的邮件告警配置",
            "is_enabled": 1,
            "is_default": 1,
            "smtp_from": "<EMAIL>",
            "smtp_smarthost": "smtp.gmail.com:587",
            "smtp_auth_username": "<EMAIL>",
            "smtp_auth_password": "password",
            "smtp_require_tls": 1,
            "email_to": "<EMAIL>,<EMAIL>",
            "email_subject": "【告警通知】{{.alert_name}}",
            "email_body": "告警详情：{{.description}}",
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        }
    ]
}
```

### 2. 获取告警方式详情

```http
GET /v1/alertsmethods/detail/{method_id}
```

**响应示例:**
```json
{
    "msg": "ok",
    "data": {
        "id": 1,
        "name": "生产环境邮件告警",
        "method_type": "email",
        "smtp_from": "<EMAIL>",
        "smtp_smarthost": "smtp.gmail.com:587",
        // ... 其他字段
    }
}
```

### 3. 创建告警方式

```http
POST /v1/alertsmethods/add
Content-Type: application/json

{
    "name": "生产环境邮件告警",
    "method_type": "email",
    "description": "生产环境的邮件告警配置",
    "is_enabled": 1,
    "is_default": 1,
    "smtp_from": "<EMAIL>",
    "smtp_smarthost": "smtp.gmail.com:587",
    "smtp_auth_username": "<EMAIL>",
    "smtp_auth_password": "your_password",
    "smtp_require_tls": 1,
    "email_to": "<EMAIL>,<EMAIL>",
    "email_subject": "【告警通知】{{.alert_name}}",
    "email_body": "告警详情：{{.description}}"
}
```

### 4. 更新告警方式

```http
PUT /v1/alertsmethods/edit
Content-Type: application/json

{
    "id": 1,
    "name": "更新后的邮件告警",
    "smtp_from": "<EMAIL>",
    "email_to": "<EMAIL>,<EMAIL>,<EMAIL>"
}
```

### 5. 删除告警方式

```http
DELETE /v1/alertsmethods/delete
Content-Type: application/json

{
    "ids": [1, 2, 3]
}
```

### 6. 获取支持的告警方式类型

```http
GET /v1/alertsmethods/types
```

**响应示例:**
```json
{
    "msg": "ok",
    "data": [
        {
            "type": "email",
            "name": "邮件告警",
            "description": "通过SMTP发送邮件告警",
            "fields": [
                {
                    "field": "smtp_from",
                    "name": "SMTP发件人邮箱",
                    "type": "email",
                    "required": true
                },
                // ... 其他字段定义
            ]
        }
    ]
}
```

### 7. 测试告警方式

```http
POST /v1/alertsmethods/test/{method_id}
Content-Type: application/json

{
    "message": "这是一条测试告警消息"
}
```

## 配置示例

### 邮件告警配置示例

```json
{
    "name": "生产环境邮件告警",
    "method_type": "email",
    "description": "生产环境的邮件告警配置",
    "is_enabled": 1,
    "is_default": 1,
    "smtp_from": "<EMAIL>",
    "smtp_smarthost": "smtp.gmail.com:587",
    "smtp_auth_username": "<EMAIL>",
    "smtp_auth_password": "your_app_password",
    "smtp_require_tls": 1,
    "email_to": "<EMAIL>,<EMAIL>",
    "email_subject": "【{{.severity}}告警】{{.alert_name}}",
    "email_body": "告警时间：{{.timestamp}}\n告警描述：{{.description}}\n告警详情：{{.details}}"
}
```

### 钉钉告警配置示例

```json
{
    "name": "钉钉群告警",
    "method_type": "dingtalk",
    "description": "发送到钉钉群的告警",
    "is_enabled": 1,
    "dingtalk_webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=your_token",
    "dingtalk_secret": "your_secret_key",
    "dingtalk_at_mobiles": "13800138000,13900139000",
    "dingtalk_at_all": 0,
    "dingtalk_title": "【{{.severity}}告警】{{.alert_name}}",
    "dingtalk_content": "## 告警通知\n\n**告警时间:** {{.timestamp}}\n\n**告警描述:** {{.description}}"
}
```

### Webhook告警配置示例

```json
{
    "name": "第三方系统Webhook",
    "method_type": "webhook",
    "description": "发送到第三方系统的Webhook告警",
    "is_enabled": 1,
    "webhook_url": "http://your-system.com/api/alerts",
    "webhook_method": "POST",
    "webhook_headers": "{\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer your_token\"}",
    "webhook_body": "{\"alert_name\": \"{{.alert_name}}\", \"severity\": \"{{.severity}}\", \"description\": \"{{.description}}\"}",
    "webhook_timeout": 10
}
```

## 前端集成说明

前端可以直接使用这些字段来构建表单：

1. 根据 `method_type` 显示对应的配置字段
2. 使用 `/v1/alertsmethods/types` 接口获取字段定义
3. 根据字段的 `type` 和 `required` 属性渲染表单控件
4. 提交时将所有字段值直接发送到后端API

这种设计使得前端可以非常灵活地处理不同类型的告警方式配置，无需复杂的JSON解析和构造。
