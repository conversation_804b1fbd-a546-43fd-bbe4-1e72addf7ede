from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Text
from sqlalchemy.exc import ResourceClosedError
import traceback
import logging
logging.basicConfig()
logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
from sqlalchemy.pool import NullPool

# 初始化数据库连接和元数据
engine = create_engine('dqlite+pydqlite://192.168.214.101:9001/hci_db', echo=True,isolation_level="READ UNCOMMITTED", poolclass=NullPool)
metadata = MetaData()

# 手动定义 migrate_version 表的结构
migrate_version = Table(
    'migrate_version', metadata,
    Column('repository_id', String(250), primary_key=True),
    Column('repository_path', Text),
    Column('version', Integer),
)


# 创建表 migrate_version
# with engine.connect() as connection:
#     connection.execute("""
        # CREATE TABLE IF NOT EXISTS migrate_version (
        #     repository_id VARCHAR(250) NOT NULL,
        #     repository_path TEXT,
        #     version INTEGER,
        #     PRIMARY KEY (repository_id)
        # )
#     """)

# 创建与数据库的连接

# with engine.connect() as connection:
#     trans = connection.begin()
#     try:
#         result = connection.execute(migrate_version.select())
#         trans.commit()  # 明确提交事务
#         # 检查结果集是否返回了行
#         rows = result.fetchall()
#         for row in rows:
#             print(row)

#     except:
#         trans.rollback()

print("222222222222222222222222222222222222222222222")
with engine.connect() as connection:
    try:
        print("333333333333333333333333333333333333333333333333")
        result = connection.execute(migrate_version.select())
        print("444444444444444444444444444444444444444444444444")
        # 检查结果集是否返回了行
        if result.returns_rows:
            print("444444444444444444444444444444444444444444444444")
            rows = result.fetchall()
            for row in rows:
                print(row)
        else:
            print("No rows returned from the query.")

    except ResourceClosedError as e:
        traceback.print_exc()
        print("Result set is closed. Cannot fetch rows.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")