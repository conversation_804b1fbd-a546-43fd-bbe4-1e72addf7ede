from app.celery import app
from api.libvirt.client import Client as LibvirtClient
from api.log.log import CustomLogger
from celery import Task
# from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
import xml.etree.ElementTree as ET
from api.libvirt.client import Client
import os
import uuid

import logging
import subprocess
import time

import os
import shutil
import xml.etree.ElementTree as ET
from datetime import datetime
logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class VmCreateTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )


class VmCreateCallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )


@app.task(name="create_vm")
def create_vm(result, form):
    print(f"create_vm-> result: {result}")
    print(f"create_vm-> form: {form}")
    if not form:
        print("VM creation failed.")
        return {"task_status": "failed", "error": "参数为空"}

    if form.get("status") == "failed":
        print("VM creation failed.")
        print(result.get("error"))
        return {"task_status": "failed", "error": result.get("error")}

    form["disk"] = result[0]
    form["interface_info"] = result[1]

    # import time
    # time.sleep(5)

    host_ip = form.get("host_ip", "")
    client = LibvirtClient()
    res = client.create_dom_vm(client, form)
    form["spice_port"] = res.get("spice_port", 0)
    form["vnc_port"] = res.get("vnc_port", 0)
    form["status"] = res.get("status", "nostate")
    print("VM creation successful.")
    return form

@app.task(name="update_vm")
def update_vm(vm_data):
    """
    更新虚拟机配置
    接收完整的VM配置数据作为参数，不访问数据库
    """
    print(f"update_vm-> vm_data: {vm_data}")
    if not vm_data:
        print("VM update failed: vm_data is empty")
        return {"task_status": "failed", "error": "参数为空"}

    domain_id = vm_data.get("domain_id")
    if not domain_id:
        print("VM update failed: domain_id is missing")
        return {"task_status": "failed", "error": "虚拟机ID不能为空"}

    try:
        # 使用传入的完整配置调用libvirt API
        host_ip = vm_data.get("host_ip", "")
        client = LibvirtClient()
        res = client.update_dom_vm(vm_data)

        result = {
            "domain_id": domain_id,
            "vm_name": vm_data.get("vm_name", ""),
            "host_ip": host_ip,
            "spice_port": res.get("spice_port", 0),
            "vnc_port": res.get("vnc_port", 0),
            "status": res.get("status", "nostate"),
            "error": res.get("error") if res.get("result") == "failed" else "",
            "task_status": "success" if res.get("result") == "success" else "failed",
            "vm_xml": res.get("vm_xml", "")
        }

        if result["task_status"] == "success":
            print(f"VM {vm_data.get('vm_name', 'Unknown')} update successful.")
        else:
            print(f"VM {vm_data.get('vm_name', 'Unknown')} update failed: {result.get('error')}")

        return result

    except Exception as e:
        error_msg = f"Error updating VM: {str(e)}"
        print(error_msg)
        return {"task_status": "failed", "error": error_msg}

@app.task(name="clone_vm")
def clone_vm(disk_results, form):
    print(f"clone_vm-> disk_results: {disk_results}")
    print(f"clone_vm-> disk_results type: {type(disk_results)}")
    print(f"clone_vm-> form: {form}")
    if not form:
        print("VM creation failed.")
        return False

    if form.get("status") == "failed":
        print("VM creation failed.")
        print(form.get("error"))
        return False

    # 检查磁盘克隆结果
    # disk_results 可能是 group 的结果，需要正确处理
    if disk_results:
        print(f"Processing disk_results: {disk_results}")
        # 如果是 group 的结果，disk_results 是一个列表
        if isinstance(disk_results, list):
            for i, disk_result in enumerate(disk_results):
                print(f"Processing disk_result[{i}]: {disk_result}, type: {type(disk_result)}")
                # 处理嵌套列表结构
                if isinstance(disk_result, list):
                    for j, result in enumerate(disk_result):
                        print(f"Processing nested result[{j}]: {result}, type: {type(result)}")
                        if isinstance(result, dict) and result.get("status") == "failed":
                            print(f"Disk cloning failed: {result.get('error')}")
                            form["status"] = "failed"
                            form["error"] = f"Disk cloning failed: {result.get('error')}"
                            return form
                elif isinstance(result, dict):
                    if disk_result.get("status") == "failed":
                        print(f"Disk cloning failed: {disk_result.get('error')}")
                        form["status"] = "failed"
                        form["error"] = f"Disk cloning failed: {disk_result.get('error')}"
                        return form
        else:
            # 如果是单个结果
            if disk_results.get("status") == "failed":
                print(f"Disk cloning failed: {disk_results.get('error')}")
                form["status"] = "failed"
                form["error"] = f"Disk cloning failed: {disk_results.get('error')}"
                return form

    domain_id = ""
    domain_name = ""

    form["interface"] = form.get("interface_info")


    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = form.get("host_ip", "")
    client = LibvirtClient()
    old_vm_name = form.get("old_vm_name")

    old_vm_info = client.get_vm_info_by_name(client, old_vm_name)

    # 解析并放入form
    # 虚拟化类型
    form["virtualization"] = old_vm_info.get("virtualization", "kvm")
    # vcpu
    form["vcpu_unit"] = old_vm_info.get("cpu_count")
    # 内存及单位
    memory = old_vm_info.get("memory")
    if memory is not None:
        # 假设memory单位为MB或GB
        if memory >= 1024 and memory % 1024 == 0:
            form["memory_unit"] = int(memory // 1024)
            form["memory_unit_type"] = "GB"
        else:
            form["memory_unit"] = int(memory)
            form["memory_unit_type"] = "MB"
    # 图形协议
    form["display_protocol"] = old_vm_info.get("display_protocol", "spice")
    # 输入设备
    form["input_devices"] = old_vm_info.get("input_devices", {"mouse": "usb", "keyboard": "usb"})

    res = client.create_dom_vm(client, form)
    form["spice_port"] = res.get("spice_port", 0)
    form["vnc_port"] = res.get("vnc_port", 0)
    form["task_status"] = "success"
    form["status"] = res.get("status", "nostate")
    print("VM clone successful.")
    return form

# @app.task(name="clone_vm")
# def clone_vm(form):
#     """
#     虚拟机克隆
#     :param form:
#     :return:
#     """
#     vm_name = form.get("vm_name", "")
#     host = form.get("host", "")
#     # 删除虚拟机的逻辑
#     client = LibvirtClient()
#     res = client.vm_clone(client, vm_name)
#     print(f"克隆虚拟机 VM: {vm_name}")
#     form.update(res)
#     return form


@app.task(name="delete_vm")
def delete_vm(form):
    """
    删除单个
    :param form:
    :return:
    """
    vm_name = form.get("name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient()
    try:
        res = client.del_vm(client, vm_name)
        if res:
            print(f"虚拟机删除成功 VM: {vm_name}")
            form["task_status"] = "success"
        else:
            print(f"虚拟机删除失败 VM: {vm_name}")
            form["task_status"] = "failed"
    except Exception as e:
        print(f"虚拟机删除失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form




@app.task(name="open_vm")
def open_vm(form):
    """
    开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()

    try:
        res = client.start_vm(client, vm_name)
        if res:
            print(f"虚拟机开机 VM: {vm_name}")
            form["task_status"] = "success"
            # 获取虚拟机XML并解析spice/vnc端口
            dom = client.get_vm_info_by_name(client, vm_name)
            vnc_port = dom.get("vnc_port", 0)
            spice_port = dom.get("spice_port", 0)

        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
    except Exception as e:
        print(f"虚拟机开机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["vnc_port"] = 0
        form["spice_port"] = 0

    return form


@app.task(name="close_vm")
def close_vm(form):
    """
    关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()

    try:
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机关机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"


    return form



@app.task(name="destroy_vm")
def destroy_vm(form):
    """
    强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机强制关机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="pause_vm")
def pause_vm(form):
    """
    暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机暂停失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="recover_vm")
def recover_vm(form):
    """
    恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机恢复失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="reboot_vm")
def reboot_vm(form):
    """
    重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
        form["task_status"] = "success"
        dom = client.get_vm_info_by_name(client, vm_name)

        vnc_port = dom.get("vnc_port", 0)
        spice_port = dom.get("spice_port", 0)

        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
    except Exception as e:
        print(f"虚拟机重启失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["vnc_port"] = 0
        form["spice_port"] = 0
    return form


@app.task(name="restart_vm")
def restart_vm(form):
    """
    强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.force_reboot_vm(client, vm_name)
        dom = client.get_vm_info_by_name(client, vm_name)
        vnc_port = dom.get("vnc_port", 0)
        spice_port = dom.get("spice_port", 0)

        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
        print(f"虚拟机强制重启 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机强制重启失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="migrate_vm")
def migrate_vm(form):
    """
    迁移虚拟机
    :param form: {
        "vm_name": "虚拟机名称",
        "source_host": "源主机IP",
        "target_host": "目标主机IP",
        "migration_type": "live|offline",  # 可选，默认live
        "flags": None  # 可选，libvirt迁移标志
    }
    :return:
    """
    vm_name = form.get("vm_name", "")
    source_host = form.get("source_host", "")
    target_host = form.get("target_host", "")
    migration_type = form.get("migration_type", "live")
    flags = form.get("flags", None)

    try:
        # 连接到源主机
        source_client = LibvirtClient(host=source_host)

        # 执行迁移
        result = source_client.migrate_vm(
            source_client,
            vm_name,
            target_host,
            migration_type,
            flags
        )

        form.update(result)
        form["task_status"] = result.get("status", "failed")

        if result.get("status") == "success":
            print(f"虚拟机迁移成功: {vm_name} -> {target_host}")
        else:
            print(f"虚拟机迁移失败: {vm_name} -> {target_host}, 错误: {result.get('message')}")

    except Exception as e:
        error_msg = f"迁移虚拟机时发生异常: {str(e)}"
        print(error_msg)
        form["task_status"] = "failed"
        form["status"] = "failed"
        form["message"] = error_msg

    return form


@app.task(name="check_migration_support")
def check_migration_support(form):
    """
    检查虚拟机迁移支持
    :param form: {
        "vm_name": "虚拟机名称",
        "source_host": "源主机IP",
        "target_host": "目标主机IP"
    }
    :return:
    """
    vm_name = form.get("vm_name", "")
    source_host = form.get("source_host", "")
    target_host = form.get("target_host", "")

    try:
        # 连接到源主机
        source_client = LibvirtClient(host=source_host)

        # 检查迁移支持
        result = source_client.check_migration_support(
            source_client,
            vm_name,
            target_host
        )

        form.update(result)
        form["task_status"] = "success"

        print(f"迁移支持检查完成: {vm_name} -> {target_host}, 支持: {result.get('supported')}")

    except Exception as e:
        error_msg = f"检查迁移支持时发生异常: {str(e)}"
        print(error_msg)
        form["task_status"] = "failed"
        form["supported"] = False
        form["message"] = error_msg

    return form


@app.task(name="batch_delete_vm")
def batch_delete_vm(form):
    """
    批量删除
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        # 删除虚拟机的逻辑
        client.del_vm(client, vm_name)
        print(f"删除虚拟机 VM: {vm_name}")
    return f"批量删除虚拟机成功"


@app.task(name="batch_open_vm")
def batch_open_vm(form):
    """
    批量开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.start_vm(client, vm_name)
        print(f"虚拟机开机 VM: {vm_name}")
    return f"批量开机虚拟机成功"


@app.task(name="batch_close_vm")
def batch_close_vm(form):
    """
    批量关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
    return f"批量关机虚拟机成功"


@app.task(name="batch_destroy_vm")
def batch_destroy_vm(form):
    """
    批量强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
    return f"批量强制关机虚拟机成功"


@app.task(name="batch_pause_vm")
def batch_pause_vm(form):
    """
    批量暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
    return f"批量暂停虚拟机成功"


@app.task(name="batch_recover_vm")
def batch_recover_vm(form):
    """
    批量恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
    return f"批量恢复虚拟机成功"


@app.task(name="batch_reboot_vm")
def batch_reboot_vm(form):
    """
    批量重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
    return f"批量重启虚拟机成功"


@app.task(name="batch_restart_vm")
def batch_restart_vm(form):
    """
    批量强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_reboot_vm(client, vm_name)
        print(f"虚拟机强制重启 VM: {vm_name}")
    return f"批量强制重启虚拟机成功"


@app.task(name="create_vm_snapshot")
def create_vm_snapshot(form):
    """
    创建虚拟机快照
    :param form:
    :return:
    """
    print("create_vm_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    try:
        r = client.create_snapshot(client, form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "创建快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "创建快照返回值无效"
        print(f"创建快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")
    except Exception as e:
        print(f"创建快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form

@app.task(name="delete_vm_snapshot")
def delete_vm_snapshot(form):
    """
    删除虚拟机快照
    :param form:
    :return:
    """
    print("delete_vm_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    try:
        r = client.delete_snapshot(client, form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "删除快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "删除快照返回值无效"
        print(f"删除快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")
    except Exception as e:
        print(f"删除快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form


@app.task(name="restore_vm_snapshot")
def restore_vm_snapshot(form):
    """
    虚拟机快照还原
    :param form:
    :return:
    """
    print("restore_vm_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    try:
        r = client.restore_snapshot(client, form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "还原快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "还原快照返回值无效"
        print(f"还原快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")
    except Exception as e:
        print(f"还原快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form


@app.task(name="snapshot_list")
def snapshot_list(form):
    """
    虚拟机快照列表
    :param form:
    :return:
    """
    print("snapshot_list 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    r = client.snapshot_list(client, form)
    return r




@app.task(name="create_vm_external_snapshot")
def create_vm_external_snapshot(form):
    """
    TODO 开发中，禁止使用
    创建虚拟机外部快照
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称",
        "description": "快照描述",
        "host_ip": "主机IP",
        "disks": [磁盘信息列表]
    }
    :return:
    """
    print("create_vm_external_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    host_ip = form.get("host_ip")

    try:
        # 使用指定主机的libvirt客户端
        client = LibvirtClient()

        # 准备外部快照参数
        snapshot_form = {
            "vm_name": vm_name,
            "snapshot_name": form.get("snapshot_name"),
            "description": form.get("description", ""),
            "snapshot_type": "external",  # 外部快照
            "disks": form.get("disks", [])
        }

        r = client.create_external_snapshot(snapshot_form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "创建外部快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "创建外部快照返回值无效"

        print(f"创建外部快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")

    except Exception as e:
        print(f"创建外部快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form

@app.task(name="delete_vm_external_snapshot")
def delete_vm_external_snapshot(form):
    """
    TODO 开发中，禁止使用
    删除虚拟机外部快照
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称",
        "host_ip": "主机IP"
    }
    :return:
    """
    print("delete_vm_external_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    host_ip = form.get("host_ip")

    try:
        # 使用指定主机的libvirt客户端
        client = LibvirtClient()

        snapshot_form = {
            "vm_name": vm_name,
            "snapshot_name": form.get("snapshot_name"),
            "snapshot_type": "external"
        }

        r = client.delete_external_snapshot(snapshot_form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "删除外部快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "删除外部快照返回值无效"

        print(f"删除外部快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")

    except Exception as e:
        print(f"删除外部快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form

@app.task(name="restore_vm_from_external_snapshot")
def restore_vm_from_external_snapshot(form):
    """
    TODO 开发中，禁止使用
    从外部快照恢复虚拟机
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称",
        "restore_type": "恢复类型",
        "host_ip": "主机IP",
        "disks": [磁盘信息列表]
    }
    :return:
    """
    print("restore_vm_from_external_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    host_ip = form.get("host_ip")

    try:
        # 使用指定主机的libvirt客户端
        client = LibvirtClient(host_ip)

        restore_form = {
            "vm_name": vm_name,
            "snapshot_name": form.get("snapshot_name"),
            "restore_type": form.get("restore_type", "full"),
            "snapshot_type": "external",
            "disks": form.get("disks", [])
        }

        r = client.restore_from_external_snapshot(restore_form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "从外部快照恢复失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "从外部快照恢复返回值无效"

        print(f"从外部快照恢复{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")

    except Exception as e:
        print(f"从外部快照恢复异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)

    return form

@app.task(name="export_vm_backup")
def export_vm_backup(form):
    """
    TODO 开发中，禁止使用
    导出虚拟机备份
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称",
        "export_path": "导出路径",
        "export_format": "导出格式",
        "host_ip": "主机IP",
        "disks": [磁盘信息列表]
    }
    :return:
    """
    print("export_vm_backup 参数:", form)
    vm_name = form.get("vm_name")
    host_ip = form.get("host_ip")

    try:
        # 使用指定主机的libvirt客户端
        client = LibvirtClient(host_ip)

        export_form = {
            "vm_name": vm_name,
            "snapshot_name": form.get("snapshot_name"),
            "export_path": form.get("export_path"),
            "export_format": form.get("export_format", "qcow2"),
            "disks": form.get("disks", [])
        }

        r = client.export_vm_backup(export_form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "导出虚拟机备份失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "导出虚拟机备份返回值无效"

        print(f"导出虚拟机备份{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")

    except Exception as e:
        print(f"导出虚拟机备份异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)

    return form

@app.task(name="get_vm_snapshot_list")
def get_vm_snapshot_list(form):
    """
    TODO 开发中，禁止使用
    获取虚拟机快照列表
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "host_ip": "主机IP"
    }
    :return:
    """
    print("get_vm_snapshot_list 参数:", form)
    vm_name = form.get("vm_name")
    host_ip = form.get("host_ip")

    try:
        # 使用指定主机的libvirt客户端
        client = LibvirtClient(host_ip)

        snapshot_form = {
            "vm_name": vm_name,
            "snapshot_type": "external"
        }

        r = client.get_snapshot_list(snapshot_form)
        if r and isinstance(r, dict) and r.get("result") == "success":
            return r.get("snapshots", [])
        else:
            print(f"获取快照列表失败: {vm_name}")
            return []

    except Exception as e:
        print(f"获取快照列表异常: {str(e)}")
        return []


# ==================== 新版备份管理任务 ====================

def parse_vm_disk_mapping(vm_xml):
    """
    解析虚拟机XML配置，获取磁盘文件路径到设备名的映射
    
    :param vm_xml: 虚拟机XML配置
    :return: {磁盘文件路径: 设备名} 的字典
    """
    import xml.etree.ElementTree as ET
    
    if not vm_xml:
        return {}
    
    try:
        root = ET.fromstring(vm_xml)
        disk_mapping = {}
        
        # 查找所有磁盘设备
        devices = root.find('devices')
        if devices is not None:
            for disk in devices.findall('disk[@type="file"]'):
                # 获取源文件路径
                source = disk.find('source')
                if source is not None:
                    file_path = source.get('file')
                    
                    # 获取目标设备名
                    target = disk.find('target')
                    if target is not None:
                        device_name = target.get('dev')
                        
                        if file_path and device_name:
                            disk_mapping[file_path] = device_name
        
        return disk_mapping
        
    except Exception as e:
        print(f"解析虚拟机XML失败: {str(e)}")
        return {}

@app.task(name="backup_create")
def backup_create(form):
    """
    创建虚拟机备份 - 基于libvirt外部快照的在线备份方案
    :param form: {
        "backup_id": "备份ID",
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "backup_name": "备份名称",
        "backup_type": "备份类型: external/internal/full",
        "description": "备份描述",
        "host_ip": "主机IP",
        "host_id": "主机ID",
        "valid_volumes": "有效的存储卷列表",
        "created_by": "创建者",
        "created_by_role": "创建者角色"
    }
    :return: 备份创建结果
    """


    backup_id = form.get('backup_id')
    vm_name = form.get('vm_name')
    backup_name = form.get('backup_name')
    valid_volumes = form.get('valid_volumes', [])

    # 添加缺失的变量定义
    vm_id = form.get('vm_id')
    host_id = form.get('host_id')
    host_ip = form.get('host_ip')

    # 记录备份开始时间
    backup_start_time = time.time()

    # 创建进程锁文件，防止并发备份
    lock_file = f"/tmp/{vm_name}_backup.lock"

    try:
        print(f"=== 虚拟机在线备份开始 ===")
        print(f"虚拟机: {vm_name}")
        print(f"备份ID: {backup_id}")
        print(f"备份名称: {backup_name}")
        print(f"备份时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 步骤1：检查备份锁
        if os.path.exists(lock_file):
            with open(lock_file, 'r') as f:
                existing_pid = f.read().strip()
            print(f"错误：备份正在进行中，PID: {existing_pid}")
            return {
                "result": "error",
                "message": "备份正在进行中，请稍后再试",
                "backup_id": backup_id
            }

        # 步骤2：创建锁文件
        with open(lock_file, 'w') as f:
            f.write(str(os.getpid()))

        print(f"创建备份锁: {lock_file}")

        # 连接本地libvirt
        client = Client()

        # 步骤3：检查虚拟机状态
        try:
            domain = client.conn.lookupByName(vm_name)
            if not domain:
                raise Exception(f"虚拟机 {vm_name} 不存在")

            # 获取虚拟机基本信息
            vm_uuid = domain.UUIDString()
            state, _ = domain.state()
            vm_status = "running" if state == 1 else f"state_{state}"

            # 获取虚拟机配置信息
            vm_xml = domain.XMLDesc(0)
            root = ET.fromstring(vm_xml)
            disk_mapping = parse_vm_disk_mapping(vm_xml)

            vm_info = {
                'memory_size': int(root.find('memory').text) if root.find('memory') is not None else 0,
                'vcpu_count': int(root.find('vcpu').text) if root.find('vcpu') is not None else 0
            }

            # 检查虚拟机运行状态
            if state != 1:  # VIR_DOMAIN_RUNNING = 1
                raise Exception(f"虚拟机未运行，当前状态: {state}")

        except Exception as e:
            print(f"获取虚拟机失败: {str(e)}")
            return {
                "result": "error",
                "message": f"获取虚拟机失败: {str(e)}",
                "backup_id": backup_id
            }

        # 准备备份目录和磁盘信息
        backup_disks = []
        snapshot_name = f"{backup_name}_snapshot"

        for volume_info in valid_volumes:
            volume = volume_info['volume']
            pool = volume_info['pool']
            disk = volume_info['disk']

            # 构建备份目录路径 - 基于存储池路径
            pool_path = pool.get('storage_local_dir', '/var/lib/libvirt/images')
            backup_base_dir = f"{pool_path}/backup"
            vm_backup_dir = f"{backup_base_dir}/{vm_name}"
            backup_specific_dir = f"{vm_backup_dir}/{backup_name}"

            # 确保目录存在
            os.makedirs(backup_specific_dir, exist_ok=True)

            # 生成备份文件路径
            original_path = volume.get('path', '')
            volume_name = volume.get('name', '')
            volume_format = volume.get('volume_type', 'qcow2')
            
            # 处理StorageVolume.path的两种格式
            if original_path.endswith(f".{volume_format}") or os.path.isfile(original_path):
                # 情况1：完整路径，直接使用
                original_path = original_path
            else:
                # 情况2：目录路径，需要拼接卷名和格式
                original_path = f"{original_path}/{volume_name}.{volume_format}"
            
            # 使用原文件名，不添加额外标识
            backup_file_name = os.path.basename(original_path)
            backup_file_path = f"{backup_specific_dir}/{backup_file_name}"

            # 从XML映射中获取正确的设备名
            device_name = disk_mapping.get(original_path)
            if not device_name:
                # 如果XML中找不到映射，使用磁盘记录中的设备名作为后备
                device_name = disk.get('dev', f"vd{chr(ord('a') + len(backup_disks))}")
                print(f"警告：无法从XML中找到磁盘 {original_path} 的设备映射，使用后备设备名: {device_name}")

            # 获取原始磁盘大小
            original_size = 0
            try:
                if os.path.exists(original_path):
                    original_size = os.path.getsize(original_path)
            except Exception as e:
                print(f"获取原始磁盘大小失败: {str(e)}")

            backup_disks.append({
                'device': device_name,  # 使用从XML解析出的正确设备名
                'original_path': original_path,
                'backup_path': backup_file_path,
                'volume_id': volume.get('id'),
                'pool_id': pool.get('id'),
                'format': volume.get('volume_type', 'qcow2'),  # 使用原始卷类型
                'original_volume_type': volume.get('volume_type', 'qcow2'),  # 保存原始卷类型
                'original_protocol_type': volume.get('protocol_type', 'file'),  # 保存原始协议类型
                'original_size': original_size
            })

        print(f"准备备份 {len(backup_disks)} 个磁盘")

        # 步骤4：创建外部快照（原子操作）
        print("步骤1: 创建外部快照")
        try:
            # 构建每个磁盘的快照参数
            disk_spec_args = []
            for disk_info in backup_disks:
                device = disk_info['device']
                original_path = disk_info['original_path']

                # 生成快照文件路径（临时文件，用于快照链）
                volume_format = disk_info.get('format', 'qcow2')
                snapshot_file_name = f"{vm_name}-{snapshot_name}-{device}.{volume_format}"
                snapshot_dir = os.path.dirname(original_path)
                snapshot_path = f"{snapshot_dir}/{snapshot_file_name}"

                # 添加磁盘快照参数：device,snapshot=external,file=path
                disk_spec = f"{device},snapshot=external,file={snapshot_path}"
                disk_spec_args.extend(['--diskspec', disk_spec])

                # 记录快照文件路径，用于后续清理
                disk_info['snapshot_temp_path'] = snapshot_path

            # 使用virsh命令创建原子快照，为每个磁盘指定快照参数
            snapshot_cmd = [
                'virsh', 'snapshot-create-as', vm_name, snapshot_name,
                '--disk-only', '--atomic', '--no-metadata'
            ]
            snapshot_cmd.extend(disk_spec_args)

            print(f"执行快照命令: {' '.join(snapshot_cmd)}")
            result = subprocess.run(snapshot_cmd, capture_output=True, text=True)

            if result.returncode != 0:
                raise Exception(f"快照创建失败: {result.stderr}")

            print("快照创建成功")
            print(f"已为 {len(backup_disks)} 个磁盘创建外部快照")

        except Exception as e:
            print(f"创建快照失败: {str(e)}")
            return {
                "result": "error",
                "message": f"创建快照失败: {str(e)}",
                "backup_id": backup_id
            }

        # 步骤5：备份基础磁盘（此时基础磁盘处于只读冻结状态）
        print("步骤2: 备份基础磁盘")
        copied_disks = []

        for disk_info in backup_disks:
            try:
                original_path = disk_info['original_path']
                backup_path = disk_info['backup_path']

                print(f"备份磁盘: {original_path} -> {backup_path}")

                # 使用cp命令复制（基础磁盘此时是只读的，安全复制）
                copy_cmd = ['cp', original_path, backup_path]
                result = subprocess.run(copy_cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    raise Exception(f"磁盘复制失败: {result.stderr}")

                # 获取备份文件信息
                stat_result = os.stat(backup_path)

                # 生成新卷的详细信息
                device_name = disk_info['device']
                backup_file_name = os.path.basename(backup_path)

                # 为新卷生成唯一名称（可自定义防止重复）
                new_volume_name = f"{backup_name}-{device_name}-{int(time.time())}"

                disk_info.update({
                    'copied': True,
                    'backup_size': stat_result.st_size,
                    'backup_created_time': stat_result.st_ctime,
                    'backup_modified_time': stat_result.st_mtime,
                    # 新卷信息
                    'new_volume_info': {
                        'name': new_volume_name,
                        'path': backup_path,
                        'filename': backup_file_name,
                        'device': device_name,
                        'capacity': stat_result.st_size,
                        'allocation': stat_result.st_size,
                        'volume_type': disk_info['format'],  # 使用原始卷类型
                        'use_type': 'backup',
                        'remark': f"虚拟机 {vm_name} 的备份磁盘 {device_name}，备份时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                    }
                })

                copied_disks.append(disk_info)
                print(f"磁盘备份完成: {backup_path}, 大小: {stat_result.st_size} 字节")

            except Exception as e:
                print(f"复制磁盘失败 {disk_info['device']}: {str(e)}")
                disk_info.update({
                    'copied': False,
                    'error': str(e)
                })
                copied_disks.append(disk_info)

        # 步骤6：在线合并快照（关键步骤）
        print("步骤3: 在线合并快照")
        try:
            for disk_info in backup_disks:
                device = disk_info['device']
                print(f"合并快照磁盘: {device}")

                # 使用virsh blockcommit进行在线合并
                commit_cmd = [
                    'virsh', 'blockcommit', vm_name, device,
                    '--active', '--verbose', '--pivot'
                ]

                print(f"执行合并命令: {' '.join(commit_cmd)}")
                result = subprocess.run(commit_cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    print(f"警告：磁盘 {device} 合并失败: {result.stderr}")
                    # 不立即返回错误，继续处理其他磁盘
                else:
                    print(f"磁盘 {device} 合并成功")

        except Exception as e:
            print(f"合并快照过程出现异常: {str(e)}")
            # 合并失败不影响备份结果，但需要记录

        # 步骤7：清理快照元数据
        print("步骤4: 清理快照元数据")
        try:
            cleanup_cmd = ['virsh', 'snapshot-delete', vm_name, snapshot_name, '--metadata']
            result = subprocess.run(cleanup_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print("快照元数据清理完成")
            else:
                print(f"快照元数据清理失败: {result.stderr}")

        except Exception as e:
            print(f"清理快照元数据异常: {str(e)}")

        # 步骤8：清理临时快照文件
        print("步骤5: 清理临时快照文件")
        for disk_info in backup_disks:
            snapshot_temp_path = disk_info.get('snapshot_temp_path')
            if snapshot_temp_path and os.path.exists(snapshot_temp_path):
                try:
                    os.remove(snapshot_temp_path)
                    print(f"删除临时快照文件: {snapshot_temp_path}")
                    disk_info['temp_file_cleaned'] = True
                except Exception as e:
                    print(f"警告：删除临时快照文件失败 {snapshot_temp_path}: {str(e)}")
                    disk_info['temp_file_cleaned'] = False
            else:
                disk_info['temp_file_cleaned'] = True  # 文件不存在，视为已清理

        # 步骤9：验证备份结果
        print("=== 备份验证 ===")
        total_backup_size = sum(disk.get('backup_size', 0) for disk in copied_disks if disk.get('copied'))
        successful_disks = [disk for disk in copied_disks if disk.get('copied')]

        # 验证备份文件完整性
        for disk_info in successful_disks:
            backup_path = disk_info['backup_path']
            try:
                check_cmd = ['qemu-img', 'check', backup_path]
                result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=30)
                integrity_check = 'passed' if result.returncode == 0 else 'failed'
                if result.returncode != 0:
                    print(f"警告：备份文件完整性验证失败: {backup_path}")
                    disk_info['integrity_check'] = 'failed'
                else:
                    disk_info['integrity_check'] = 'passed'
            except Exception as e:
                print(f"完整性验证异常: {str(e)}")
                disk_info['integrity_check'] = 'error'

        print(f"=== 备份完成 ===")
        print(f"成功备份: {len(successful_disks)}/{len(backup_disks)} 个磁盘")
        print(f"总备份大小: {total_backup_size} 字节")
        print(f"备份时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("系统已恢复到单磁盘状态")

        # 收集完整的备份元数据
        backup_end_time = time.time()
        backup_duration = backup_end_time - backup_start_time

        # 构建完整的元数据信息
        backup_metadata = {
            # 备份基本信息
            "backup_info": {
                "backup_id": backup_id,
                "backup_name": backup_name,
                "backup_type": "external_snapshot",
                "backup_method": "libvirt_external_snapshot",
                "created_at": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(backup_start_time)),
                "completed_at": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(backup_end_time)),
                "duration_seconds": round(backup_duration, 2)
            },

            # 虚拟机信息
            "vm_info": {
                "vm_id": vm_id,
                "vm_name": vm_name,
                "vm_uuid": vm_uuid,
                "vm_status": vm_status,
                "total_disks": len(backup_disks),
                "memory_size": vm_info.get('memory_size'),
                "vcpu_count": vm_info.get('vcpu_count')
            },

            # 主机环境信息
            "host_info": {
                "host_id": host_id,
                "host_ip": host_ip,
                "hypervisor": "libvirt",
                "snapshot_name": snapshot_name,
                "storage_pools": []  # 收集实际使用的存储池信息
            },

            # 备份统计
            "backup_statistics": {
                "total_disks": len(backup_disks),
                "successful_disks": len(successful_disks),
                "failed_disks": len(backup_disks) - len(successful_disks),
                "total_backup_size": total_backup_size,
                "total_original_size": sum(disk.get('original_size', 0) for disk in backup_disks),
                "compression_ratio": round(total_backup_size / max(sum(disk.get('original_size', 1) for disk in backup_disks), 1), 4),
                "average_disk_size": round(total_backup_size / max(len(successful_disks), 1), 2),
                "backup_speed_mbps": round((total_backup_size / (1024 * 1024)) / max(backup_duration, 1), 2)
            },

            # 磁盘详细信息
            "disk_details": []
        }

        # 为每个磁盘添加详细的元数据
        storage_pools_used = set()  # 收集使用的存储池

        for disk_info in copied_disks:
            # 获取原始卷的完整信息
            original_volume_info = {}
            pool_info = {}

            if disk_info.get('volume_id'):
                try:
                    # 这里可以查询数据库获取更多卷信息，暂时使用已有信息
                    original_volume_info = {
                        "volume_id": disk_info.get('volume_id'),
                        "pool_id": disk_info.get('pool_id'),
                        "original_volume_type": disk_info.get('original_volume_type'),
                        "original_protocol_type": disk_info.get('original_protocol_type')
                    }

                    # 收集存储池信息
                    pool_info = {
                        "pool_id": disk_info.get('pool_id'),
                        "pool_path": disk_info.get('pool_path'),
                        "backup_base_path": disk_info.get('backup_base_path')
                    }

                    if pool_info["pool_id"]:
                        storage_pools_used.add((pool_info["pool_id"], pool_info["pool_path"], pool_info["backup_base_path"]))

                except Exception as e:
                    print(f"获取原始卷信息失败: {str(e)}")

            # 构建磁盘元数据
            disk_metadata = {
                # 基本信息
                "device": disk_info.get('device'),
                "device_type": "disk",
                "bus_type": "virtio",  # 可以从VM配置中获取

                # 原始磁盘信息
                "original_disk": {
                    "path": disk_info.get('original_path'),
                    "format": disk_info.get('format'),
                    "size": disk_info.get('original_size', 0),
                    "volume_info": original_volume_info
                },

                # 备份磁盘信息
                "backup_disk": {
                    "path": disk_info.get('backup_path'),
                    "filename": os.path.basename(disk_info.get('backup_path', '')),
                    "size": disk_info.get('backup_size', 0),
                    "format": disk_info.get('format'),
                    "created_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                    "integrity_check": disk_info.get('integrity_check', 'unknown')
                },

                # 快照信息
                "snapshot_info": {
                    "snapshot_temp_path": disk_info.get('snapshot_temp_path'),
                    "snapshot_created": True,
                    "snapshot_merged": True,
                    "temp_file_cleaned": disk_info.get('temp_file_cleaned', False)
                },

                # 备份结果
                "backup_result": {
                    "copied": disk_info.get('copied', False),
                    "copy_method": "cp",
                    "backup_size": disk_info.get('backup_size', 0),
                    "compression_ratio": round(disk_info.get('backup_size', 0) / max(disk_info.get('original_size', 1), 1), 4)
                },

                # 新卷信息（如果有）
                "new_volume_info": disk_info.get('new_volume_info', {})
            }

            backup_metadata["disk_details"].append(disk_metadata)

        # 添加使用的存储池信息
        backup_metadata["host_info"]["storage_pools"] = list(storage_pools_used)

        return {
            "result": "success",
            "message": "备份创建成功",
            "backup_id": backup_id,
            "backup_name": backup_name,
            "vm_name": vm_name,
            "vm_xml": vm_xml,  # 添加虚拟机XML配置
            "snapshot_name": snapshot_name,
            "backup_disks": copied_disks,
            "total_backup_size": total_backup_size,
            "successful_disk_count": len(successful_disks),
            "backup_status": "completed",
            "backup_metadata": backup_metadata  # 添加完整的元数据
        }

    except Exception as e:
        print(f"创建备份异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "result": "error",
            "message": str(e),
            "backup_id": backup_id,
            "backup_status": "failed"
        }

    finally:
        # 清理锁文件
        try:
            if os.path.exists(lock_file):
                os.remove(lock_file)
                print(f"清理备份锁: {lock_file}")
        except Exception as e:
            print(f"清理锁文件失败: {str(e)}")


@app.task(name="backup_delete")
def backup_delete(form):
    """
    删除虚拟机备份文件
    :param form: {
        "backup_id": "备份ID",
        "backup_name": "备份名称",
        "vm_name": "虚拟机名称",
        "backup_files": [{"path": "文件路径", "volume_name": "卷名称", "volume_id": "卷ID"}],
        "force_delete": "是否强制删除",
        "host_ip": "主机IP",
        "created_by": "操作者",
        "created_by_role": "操作者角色"
    }
    :return: 备份删除结果
    """
    import os
    from datetime import datetime

    try:
        print(f"=== 开始删除备份文件任务 ===")
        print(f"删除参数: {form}")

        backup_id = form.get('backup_id')
        backup_name = form.get('backup_name', '')
        vm_name = form.get('vm_name', 'unknown')
        backup_files = form.get('backup_files', [])
        force_delete = form.get('force_delete', False)
        host_ip = form.get('host_ip', '')
        username = form.get('created_by', 'system')

        if not backup_id:
            return {
                "result": "error",
                "message": "缺少备份ID",
                "backup_id": backup_id,
                "backup_name": backup_name,
                "vm_name": vm_name
            }

        if not backup_files:
            print(f"备份 {backup_name} 没有关联的文件")
            return {
                "result": "success",
                "message": "没有需要删除的备份文件",
                "backup_id": backup_id,
                "backup_name": backup_name,
                "vm_name": vm_name,
                "deleted_files": [],
                "failed_files": [],
                "deleted_dirs": [],
                "force_delete": force_delete,
                "total_files": 0,
                "deleted_count": 0,
                "failed_count": 0,
                "host_ip": host_ip,
                "created_by": username
            }

        deleted_files = []
        failed_files = []
        backup_dirs = set()

        print(f"开始删除备份 {backup_name} 的 {len(backup_files)} 个文件")

        # 删除每个备份文件
        for file_info in backup_files:
            file_path = file_info.get('path', '')
            volume_name = file_info.get('volume_name', '')

            if not file_path:
                continue

            file_result = {
                "path": file_path,
                "volume_name": volume_name
            }

            try:
                if os.path.exists(file_path):
                    if force_delete:
                        # 强制删除：删除文件
                        os.remove(file_path)
                        print(f"删除备份文件: {file_path}")
                        file_result.update({
                            "deleted": True,
                            "existed": True
                        })

                        # 记录备份目录用于后续清理
                        backup_dir = os.path.dirname(file_path)
                        backup_dirs.add(backup_dir)

                    else:
                        # 软删除：只标记，不删除文件
                        print(f"软删除模式，保留备份文件: {file_path}")
                        file_result.update({
                            "deleted": False,
                            "existed": True,
                            "soft_delete": True
                        })

                    deleted_files.append(file_result)

                else:
                    print(f"备份文件不存在: {file_path}")
                    file_result.update({
                        "deleted": False,
                        "existed": False,
                        "not_found": True
                    })
                    deleted_files.append(file_result)

            except Exception as e:
                print(f"删除文件 {file_path} 失败: {str(e)}")
                file_result.update({
                    "deleted": False,
                    "existed": os.path.exists(file_path),
                    "error": str(e)
                })
                failed_files.append(file_result)

        # 尝试删除空的备份目录（仅在强制删除模式下）
        deleted_dirs = []
        if force_delete and backup_dirs:
            for backup_dir in backup_dirs:
                try:
                    if os.path.exists(backup_dir) and os.path.isdir(backup_dir):
                        # 检查目录是否为空
                        if not os.listdir(backup_dir):
                            os.rmdir(backup_dir)
                            print(f"删除空备份目录: {backup_dir}")
                            deleted_dirs.append(backup_dir)
                        else:
                            print(f"备份目录不为空，跳过删除: {backup_dir}")
                except Exception as e:
                    print(f"删除备份目录 {backup_dir} 失败: {str(e)}")

        # 构建返回结果
        result = {
            "result": "success" if not failed_files else "partial",
            "backup_id": backup_id,
            "backup_name": backup_name,
            "vm_name": vm_name,
            "deleted_files": deleted_files,
            "failed_files": failed_files,
            "deleted_dirs": deleted_dirs,
            "force_delete": force_delete,
            "total_files": len(backup_files),
            "deleted_count": len(deleted_files),
            "failed_count": len(failed_files),
            "host_ip": host_ip,
            "created_by": username
        }

        print(f"=== 备份文件删除任务完成 ===")
        print(f"备份 {backup_name}: 成功处理 {len(deleted_files)} 个文件, 失败 {len(failed_files)} 个文件")

        return result

    except Exception as e:
        print(f"删除备份文件异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "result": "error",
            "message": str(e),
            "backup_id": form.get('backup_id'),
            "backup_name": form.get('backup_name', ''),
            "vm_name": form.get('vm_name', '')
        }


@app.task(name="backup_restore")
def backup_restore(form):
    """
    从备份还原虚拟机（创建新虚拟机）

    参数格式:
    {
        "new_vm_id": "新虚拟机ID",
        "new_vm_name": "新虚拟机名称",
        "backup_id": "备份ID",
        "backup_name": "备份名称",
        "original_vm": {...},  # 原虚拟机信息
        "host": {...},         # 主机信息
        "backup_volumes": [...], # 备份存储卷信息
        "new_interfaces": [...], # 新网络接口配置
        "username": "操作者",
        "role": "操作者角色"
    }
    """


    new_vm_id = form.get('new_vm_id')
    new_vm_name = form.get('new_vm_name')
    backup_id = form.get('backup_id')
    backup_name = form.get('backup_name')
    original_vm = form.get('original_vm', {})
    backup_volumes = form.get('backup_volumes', [])
    new_interfaces = form.get('new_interfaces', [])
    username = form.get('username', 'system')
    role = form.get('role', 'operator')

    restore_start_time = datetime.now()

    try:
        print(f"开始还原备份 {backup_name} 到新虚拟机 {new_vm_name}")

        # 1. 检查并复制备份文件到新卷位置
        restored_volumes = []
        total_restore_size = 0

        for backup_volume in backup_volumes:
            original_path = backup_volume.get('original_path')  # 备份文件路径
            new_path = backup_volume.get('new_path')           # 新卷路径
            new_volume_name = backup_volume.get('new_volume_name')
            volume_type = backup_volume.get('volume_type', 'qcow2')

            print(f"处理卷: {new_volume_name}")
            print(f"  备份文件: {original_path}")
            print(f"  目标路径: {new_path}")

            # 检查备份文件是否存在
            if not original_path or not os.path.exists(original_path):
                raise Exception(f"备份文件不存在: {original_path}")

            # 确保目标目录存在
            target_dir = os.path.dirname(new_path)
            os.makedirs(target_dir, exist_ok=True)

            # 复制备份文件到新位置
            print(f"复制文件: {original_path} -> {new_path}")
            shutil.copy2(original_path, new_path)

            # 验证复制结果
            if not os.path.exists(new_path):
                raise Exception(f"文件复制失败: {new_path}")

            # 获取文件大小
            file_size = os.path.getsize(new_path)
            total_restore_size += file_size

            # 验证文件完整性
            try:
                result = subprocess.run([
                    'qemu-img', 'check', new_path
                ], capture_output=True, text=True, timeout=30)
                integrity_check = 'passed' if result.returncode == 0 else 'failed'
                if result.returncode != 0:
                    print(f"警告：备份文件完整性检查失败: {result.stderr}")
            except Exception as e:
                print(f"警告：无法执行完整性检查: {str(e)}")
                integrity_check = 'unknown'

            restored_volumes.append({
                'original_path': original_path,
                'new_path': new_path,
                'new_volume_name': new_volume_name,
                'volume_type': volume_type,
                'size': file_size,
                'copied': True,
                'integrity_check': integrity_check,
                'new_volume_id': backup_volume.get('new_volume_id')
            })

            print(f"卷 {new_volume_name} 复制完成，大小: {file_size} 字节")

        # 2. 获取备份时保存的虚拟机XML配置
        backup_xml = form.get('backup_xml')
        original_vm = form.get('original_vm', {})
        
        if backup_xml:
            # 使用备份时保存的XML配置
            original_xml = backup_xml
            print("使用备份时保存的虚拟机XML配置")
        else:
            # 如果备份中没有XML，尝试从当前虚拟机获取（向后兼容）
            try:
                print("备份中没有XML配置，尝试从当前虚拟机获取")
                xml_result = subprocess.run([
                    'virsh', 'dumpxml', original_vm.get('name', '')
                ], capture_output=True, text=True)

                if xml_result.returncode == 0:
                    original_xml = xml_result.stdout
                    print("成功获取当前虚拟机XML配置")
                else:
                    print("无法获取虚拟机XML，使用默认模板")
                    original_xml = None
            except Exception as e:
                print(f"获取虚拟机XML失败: {str(e)}")
                original_xml = None

        # 3. 生成新虚拟机的XML配置
        print(f"原始XML配置: {original_xml}")
        if original_xml:
            # 基于原XML修改
            print("基于原XML修改")
            vm_xml = modify_vm_xml(original_xml, new_vm_name, restored_volumes, new_interfaces)
        else:
            # 生成默认XML
            print("生成默认XML")
            vm_xml = generate_default_vm_xml(new_vm_name, original_vm, restored_volumes, new_interfaces)

        # 4. 定义虚拟机到libvirt
        print(f"生成虚拟机XML: {vm_xml}")
        xml_file = f"/tmp/{new_vm_name}_restore.xml"
        with open(xml_file, 'w') as f:
            f.write(vm_xml)

        print(f"定义虚拟机: {new_vm_name}")
        define_result = subprocess.run([
            'virsh', 'define', xml_file
        ], capture_output=True, text=True)

        if define_result.returncode != 0:
            raise Exception(f"定义虚拟机失败: {define_result.stderr}")

        # 清理临时XML文件
        os.remove(xml_file)

        # 5. 启动虚拟机
        print(f"启动虚拟机: {new_vm_name}")
        start_result = subprocess.run([
            'virsh', 'start', new_vm_name
        ], capture_output=True, text=True)

        vm_started = start_result.returncode == 0
        if not vm_started:
            print(f"启动虚拟机失败: {start_result.stderr}")
            # 启动失败不算致命错误，虚拟机已经定义成功

        # 6. 计算还原统计信息
        restore_end_time = datetime.now()
        duration_seconds = (restore_end_time - restore_start_time).total_seconds()

        # 7. 构建成功返回结果
        result = {
            "result": "success",
            "new_vm_id": new_vm_id,
            "new_vm_name": new_vm_name,
            "backup_id": backup_id,
            "backup_name": backup_name,
            "vm_xml": vm_xml,  # 添加生成的虚拟机XML配置
            "restored_volumes": restored_volumes,
            "vm_started": vm_started,
            "restore_statistics": {
                "total_volumes": len(restored_volumes),
                "successful_volumes": len([v for v in restored_volumes if v['copied']]),
                "failed_volumes": len([v for v in restored_volumes if not v['copied']]),
                "total_restore_size": total_restore_size,
                "duration_seconds": duration_seconds,
                "restore_speed_mbps": round(total_restore_size / (1024*1024) / max(duration_seconds, 1), 2)
            },
            "restore_metadata": {
                "restore_info": {
                    "restore_type": "full_restore",
                    "restore_method": "backup_to_new_vm",
                    "started_at": restore_start_time.isoformat(),
                    "completed_at": restore_end_time.isoformat(),
                    "duration_seconds": duration_seconds
                },
                "source_backup": {
                    "backup_id": backup_id,
                    "backup_name": backup_name,
                    "original_vm_name": original_vm.get('name')
                },
                "target_vm": {
                    "vm_id": new_vm_id,
                    "vm_name": new_vm_name,
                    "vcpu": original_vm.get('vcpu'),
                    "memory": original_vm.get('memory'),
                    "started": vm_started
                },
                "volume_details": restored_volumes,
                "operator_info": {
                    "username": username,
                    "role": role,
                    "operation": "backup_restore"
                }
            }
        }

        print(f"备份还原成功: 从备份 {backup_name} 创建虚拟机 {new_vm_name}")
        return result

    except Exception as e:
        error_msg = str(e)
        print(f"备份还原失败: {error_msg}")
        import traceback
        traceback.print_exc()

        # 清理已创建的文件
        try:
            for volume in locals().get('restored_volumes', []):
                if volume.get('copied') and os.path.exists(volume['new_path']):
                    os.remove(volume['new_path'])
                    print(f"清理文件: {volume['new_path']}")
        except Exception as cleanup_error:
            print(f"清理文件失败: {str(cleanup_error)}")

        # 清理已定义的虚拟机
        try:
            subprocess.run(['virsh', 'undefine', new_vm_name],
                         capture_output=True, text=True)
            print(f"清理虚拟机定义: {new_vm_name}")
        except Exception as cleanup_error:
            print(f"清理虚拟机定义失败: {str(cleanup_error)}")

        return {
            "result": "failed",
            "new_vm_id": new_vm_id,
            "new_vm_name": new_vm_name,
            "backup_id": backup_id,
            "error": error_msg,
            "restored_volumes": locals().get('restored_volumes', []),
            "restore_metadata": {
                "restore_info": {
                    "restore_type": "full_restore",
                    "started_at": restore_start_time.isoformat(),
                    "failed_at": datetime.now().isoformat()
                },
                "error_details": {
                    "error_message": error_msg,
                    "error_type": type(e).__name__
                },
                "operator_info": {
                    "username": username,
                    "role": role,
                    "operation": "backup_restore"
                }
            }
        }

def modify_vm_xml(original_xml, new_vm_name, restored_volumes, new_interfaces):
    """
    修改原始虚拟机XML配置，更新名称、磁盘路径和网络接口
    
    :param original_xml: 原始虚拟机XML配置
    :param new_vm_name: 新虚拟机名称
    :param restored_volumes: 还原的存储卷信息列表
    :param new_interfaces: 新网络接口信息列表
    :return: 修改后的XML配置
    """
    import xml.etree.ElementTree as ET
    
    try:
        # 解析XML
        root = ET.fromstring(original_xml)
        
        # 1. 更新虚拟机名称
        name_elem = root.find('name')
        if name_elem is not None:
            name_elem.text = new_vm_name
        
        # 2. 移除UUID，让libvirt自动生成新的UUID
        uuid_elem = root.find('uuid')
        if uuid_elem is not None:
            root.remove(uuid_elem)
            print(f"已移除原UUID元素")
        else:
            print(f"未找到UUID元素")
        
        # 2.1 移除sysinfo中的UUID
        sysinfo_elem = root.find('sysinfo')
        if sysinfo_elem is not None:
            system_elem = sysinfo_elem.find('system')
            if system_elem is not None:
                for entry in system_elem.findall('entry'):
                    if entry.get('name') == 'uuid':
                        system_elem.remove(entry)
                        print(f"已移除sysinfo中的UUID entry")
        
        # 2.2 移除可能导致冲突的其他标识符
        for conflict_elem in ['uuid', 'sysinfo', 'idmap']:
            elem = root.find(conflict_elem)
            if elem is not None:
                root.remove(elem)
                print(f"已移除 {conflict_elem} 元素")
        
        # 3. 移除os元素中的boot配置和smbios引用
        os_elem = root.find('os')
        if os_elem is not None:
            # 移除boot元素
            for boot_elem in os_elem.findall('boot'):
                os_elem.remove(boot_elem)
            
            # 移除smbios引用，避免sysinfo不可用错误
            smbios_elem = os_elem.find('smbios')
            if smbios_elem is not None:
                os_elem.remove(smbios_elem)
                print(f"已移除os元素中的smbios引用")
        
        # 4. 移除一些可能导致冲突的元素
        for elem_name in ['seclabel', 'metadata']:
            elem = root.find(elem_name)
            if elem is not None:
                root.remove(elem)
        
        # 5. 更新磁盘配置
        devices = root.find('devices')
        if devices is not None:
            # 移除所有现有磁盘
            for disk in devices.findall('disk[@type="file"]'):
                devices.remove(disk)
            
            # 添加新的磁盘配置
            for i, volume_info in enumerate(restored_volumes):
                disk_elem = ET.SubElement(devices, 'disk')
                disk_elem.set('type', 'file')
                disk_elem.set('device', 'disk')
                
                # 驱动配置
                driver_elem = ET.SubElement(disk_elem, 'driver')
                driver_elem.set('name', 'qemu')
                driver_elem.set('type', volume_info.get('volume_type', 'qcow2'))
                
                # 源文件路径
                source_elem = ET.SubElement(disk_elem, 'source')
                source_elem.set('file', volume_info['new_path'])
                
                # 目标设备
                target_elem = ET.SubElement(disk_elem, 'target')
                target_elem.set('dev', f"vd{chr(ord('a') + i)}")  # vda, vdb, vdc...
                target_elem.set('bus', 'virtio')
                
                # 启动顺序（第一个磁盘）
                if i == 0:
                    boot_elem = ET.SubElement(disk_elem, 'boot')
                    boot_elem.set('order', '1')
        
        # 6. 更新网络接口配置
        if new_interfaces and devices is not None:
            # 移除所有现有网络接口
            for interface in devices.findall('interface'):
                devices.remove(interface)
            
            # 添加新的网络接口
            for interface_info in new_interfaces:
                interface_elem = ET.SubElement(devices, 'interface')
                interface_elem.set('type', 'bridge')
                
                # MAC地址
                mac_elem = ET.SubElement(interface_elem, 'mac')
                mac_elem.set('address', interface_info.get('mac', ''))
                
                # 源网桥
                source_elem = ET.SubElement(interface_elem, 'source')
                source_elem.set('bridge', interface_info.get('bridge', 'virbr0'))
                
                # 所有网桥都使用OVS virtualport配置
                virtualport_elem = ET.SubElement(interface_elem, 'virtualport')
                virtualport_elem.set('type', 'openvswitch')
                
                # 添加interfaceid参数
                parameters_elem = ET.SubElement(virtualport_elem, 'parameters')
                interface_id = interface_info.get('interface_id') or interface_info.get('port_id') or str(uuid.uuid4())
                parameters_elem.set('interfaceid', interface_id)
                
                # 模型
                model_elem = ET.SubElement(interface_elem, 'model')
                model_elem.set('type', 'virtio')
        
        # 转换回XML字符串
        return ET.tostring(root, encoding='unicode')
        
    except Exception as e:
        print(f"修改XML配置失败: {str(e)}")
        # 如果修改失败，返回生成的默认XML
        return generate_default_vm_xml(new_vm_name, {}, restored_volumes, new_interfaces)


def generate_default_vm_xml(new_vm_name, original_vm, restored_volumes, new_interfaces):
    """
    生成默认的虚拟机XML配置
    
    :param new_vm_name: 新虚拟机名称
    :param original_vm: 原虚拟机信息
    :param restored_volumes: 还原的存储卷信息列表
    :param new_interfaces: 新网络接口信息列表
    :return: 生成的XML配置
    """
    # 从原虚拟机信息获取配置，如果没有则使用默认值
    memory_mb = original_vm.get('memory', 2048)  # 默认2GB内存
    vcpu_count = original_vm.get('vcpu', 2)      # 默认2个CPU
    os_type = original_vm.get('os_type', 'linux')
    cpu_arch = original_vm.get('cpu_arch', 'x86_64')
    
    xml_template = f'''<domain type='kvm'>
  <name>{new_vm_name}</name>
  <memory unit='MiB'>{memory_mb}</memory>
  <currentMemory unit='MiB'>{memory_mb}</currentMemory>
  <vcpu placement='static'>{vcpu_count}</vcpu>
  <os>
    <type arch='{cpu_arch}' machine='pc-i440fx-2.9'>hvm</type>
  </os>
  <features>
    <acpi/>
    <apic/>
    <vmport state='off'/>
  </features>
  <cpu mode='host-model' check='partial'>
    <model fallback='allow'/>
  </cpu>
  <clock offset='utc'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
  </clock>
  <on_poweroff>destroy</on_poweroff>
  <on_reboot>restart</on_reboot>
  <on_crash>destroy</on_crash>
  <pm>
    <suspend-to-mem enabled='no'/>
    <suspend-to-disk enabled='no'/>
  </pm>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>'''
    
    # 添加磁盘配置
    for i, volume_info in enumerate(restored_volumes):
        device_name = f"vd{chr(ord('a') + i)}"  # vda, vdb, vdc...
        volume_type = volume_info.get('volume_type', 'qcow2')
        volume_path = volume_info['new_path']
        
        disk_xml = f'''
    <disk type='file' device='disk'>
      <driver name='qemu' type='{volume_type}'/>
      <source file='{volume_path}'/>
      <target dev='{device_name}' bus='virtio'/>'''
        
        # 第一个磁盘设置为启动盘
        if i == 0:
            disk_xml += '''
      <boot order='1'/>'''
        
        disk_xml += '''
    </disk>'''
        
        xml_template += disk_xml
    
    # 添加网络接口配置
    if new_interfaces:
        for interface_info in new_interfaces:
            mac_address = interface_info.get('mac', '52:54:00:00:00:01')
            bridge_name = interface_info.get('bridge', '')
            
            # 基础接口XML
            interface_xml = f'''
    <interface type='bridge'>
      <mac address='{mac_address}'/>
      <source bridge='{bridge_name}'/>
      <model type='virtio'/>'''
            
            # 所有网桥都使用OVS virtualport配置
            interface_xml += f'''
      <virtualport type='openvswitch'>
        <parameters interfaceid='{interface_info.get('interface_id') or interface_info.get('port_id') or str(uuid.uuid4())}'/>
      </virtualport>'''
            
            interface_xml += '''
    </interface>'''
            xml_template += interface_xml
    else:
        # 默认网络接口
        xml_template += '''
    <interface type='bridge'>
      <mac address='52:54:00:00:00:01'/>
      <source bridge='virbr0'/>
      <model type='virtio'/>
      <virtualport type='openvswitch'>
        <parameters interfaceid='{str(uuid.uuid4())}'/>
      </virtualport>
    </interface>'''
    
    # 添加其他设备配置
    xml_template += '''
    <serial type='pty'>
      <target type='isa-serial' port='0'>
        <model name='isa-serial'/>
      </target>
    </serial>
    <console type='pty'>
      <target type='serial' port='0'/>
    </console>
    <input type='tablet' bus='usb'>
      <address type='usb' bus='0' port='1'/>
    </input>
    <input type='mouse' bus='ps2'/>
    <input type='keyboard' bus='ps2'/>
    <graphics type='vnc' port='-1' autoport='yes' listen='0.0.0.0'>
      <listen type='address' address='0.0.0.0'/>
    </graphics>
    <video>
      <model type='cirrus' vram='16384' heads='1' primary='yes'/>
      <address type='pci' domain='0x0000' bus='0x00' slot='0x02' function='0x0'/>
    </video>
    <memballoon model='virtio'>
      <address type='pci' domain='0x0000' bus='0x00' slot='0x05' function='0x0'/>
    </memballoon>
  </devices>
</domain>'''
    
    return xml_template

def modify_vm_xml(original_xml, new_vm_name, restored_volumes, new_interfaces):
    """
    修改原始虚拟机XML配置，更新名称、磁盘路径和网络接口
    
    :param original_xml: 原始虚拟机XML配置
    :param new_vm_name: 新虚拟机名称
    :param restored_volumes: 还原的存储卷信息列表
    :param new_interfaces: 新网络接口信息列表
    :return: 修改后的XML配置
    """
    import xml.etree.ElementTree as ET
    
    try:
        # 解析XML
        root = ET.fromstring(original_xml)
        
        # 1. 更新虚拟机名称
        name_elem = root.find('name')
        if name_elem is not None:
            name_elem.text = new_vm_name
        
        # 2. 移除UUID，让libvirt自动生成新的UUID
        uuid_elem = root.find('uuid')
        if uuid_elem is not None:
            root.remove(uuid_elem)
            print(f"已移除原UUID元素")
        else:
            print(f"未找到UUID元素")
        
        # 2.1 移除sysinfo中的UUID
        sysinfo_elem = root.find('sysinfo')
        if sysinfo_elem is not None:
            system_elem = sysinfo_elem.find('system')
            if system_elem is not None:
                for entry in system_elem.findall('entry'):
                    if entry.get('name') == 'uuid':
                        system_elem.remove(entry)
                        print(f"已移除sysinfo中的UUID entry")
        
        # 2.2 移除可能导致冲突的其他标识符
        for conflict_elem in ['uuid', 'sysinfo', 'idmap']:
            elem = root.find(conflict_elem)
            if elem is not None:
                root.remove(elem)
                print(f"已移除 {conflict_elem} 元素")
        
        # 3. 移除os元素中的boot配置和smbios引用
        os_elem = root.find('os')
        if os_elem is not None:
            # 移除boot元素
            for boot_elem in os_elem.findall('boot'):
                os_elem.remove(boot_elem)
            
            # 移除smbios引用，避免sysinfo不可用错误
            smbios_elem = os_elem.find('smbios')
            if smbios_elem is not None:
                os_elem.remove(smbios_elem)
                print(f"已移除os元素中的smbios引用")
        
        # 4. 移除一些可能导致冲突的元素
        for elem_name in ['seclabel', 'metadata']:
            elem = root.find(elem_name)
            if elem is not None:
                root.remove(elem)
        
        # 5. 更新磁盘配置
        devices = root.find('devices')
        if devices is not None:
            # 移除所有现有磁盘
            for disk in devices.findall('disk[@type="file"]'):
                devices.remove(disk)
            
            # 添加新的磁盘配置
            for i, volume_info in enumerate(restored_volumes):
                disk_elem = ET.SubElement(devices, 'disk')
                disk_elem.set('type', 'file')
                disk_elem.set('device', 'disk')
                
                # 驱动配置
                driver_elem = ET.SubElement(disk_elem, 'driver')
                driver_elem.set('name', 'qemu')
                driver_elem.set('type', volume_info.get('volume_type', 'qcow2'))
                
                # 源文件路径
                source_elem = ET.SubElement(disk_elem, 'source')
                source_elem.set('file', volume_info['new_path'])
                
                # 目标设备
                target_elem = ET.SubElement(disk_elem, 'target')
                target_elem.set('dev', f"vd{chr(ord('a') + i)}")  # vda, vdb, vdc...
                target_elem.set('bus', 'virtio')
                
                # 启动顺序（第一个磁盘）
                if i == 0:
                    boot_elem = ET.SubElement(disk_elem, 'boot')
                    boot_elem.set('order', '1')
        
        # 6. 更新网络接口配置
        if new_interfaces and devices is not None:
            # 移除所有现有网络接口
            for interface in devices.findall('interface'):
                devices.remove(interface)
            
            # 添加新的网络接口
            for interface_info in new_interfaces:
                interface_elem = ET.SubElement(devices, 'interface')
                interface_elem.set('type', 'bridge')
                
                # MAC地址
                mac_elem = ET.SubElement(interface_elem, 'mac')
                mac_elem.set('address', interface_info.get('mac', ''))
                
                # 源网桥
                source_elem = ET.SubElement(interface_elem, 'source')
                source_elem.set('bridge', interface_info.get('bridge', 'virbr0'))
                
                # 所有网桥都使用OVS virtualport配置
                virtualport_elem = ET.SubElement(interface_elem, 'virtualport')
                virtualport_elem.set('type', 'openvswitch')
                
                # 添加interfaceid参数
                parameters_elem = ET.SubElement(virtualport_elem, 'parameters')
                interface_id = interface_info.get('interface_id') or interface_info.get('port_id') or str(uuid.uuid4())
                parameters_elem.set('interfaceid', interface_id)
                
                # 模型
                model_elem = ET.SubElement(interface_elem, 'model')
                model_elem.set('type', 'virtio')
        
        # 转换回XML字符串
        return ET.tostring(root, encoding='unicode')
        
    except Exception as e:
        print(f"修改XML配置失败: {str(e)}")
        # 如果修改失败，返回生成的默认XML
        return generate_default_vm_xml(new_vm_name, {}, restored_volumes, new_interfaces)


def generate_default_vm_xml(new_vm_name, original_vm, restored_volumes, new_interfaces):
    """
    生成默认的虚拟机XML配置
    
    :param new_vm_name: 新虚拟机名称
    :param original_vm: 原虚拟机信息
    :param restored_volumes: 还原的存储卷信息列表
    :param new_interfaces: 新网络接口信息列表
    :return: 生成的XML配置
    """
    # 从原虚拟机信息获取配置，如果没有则使用默认值
    memory_mb = original_vm.get('memory', 2048)  # 默认2GB内存
    vcpu_count = original_vm.get('vcpu', 2)      # 默认2个CPU
    os_type = original_vm.get('os_type', 'linux')
    cpu_arch = original_vm.get('cpu_arch', 'x86_64')
    
    xml_template = f'''<domain type='kvm'>
  <name>{new_vm_name}</name>
  <memory unit='MiB'>{memory_mb}</memory>
  <currentMemory unit='MiB'>{memory_mb}</currentMemory>
  <vcpu placement='static'>{vcpu_count}</vcpu>
  <os>
    <type arch='{cpu_arch}' machine='pc-i440fx-2.9'>hvm</type>
  </os>
  <features>
    <acpi/>
    <apic/>
    <vmport state='off'/>
  </features>
  <cpu mode='host-model' check='partial'>
    <model fallback='allow'/>
  </cpu>
  <clock offset='utc'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
  </clock>
  <on_poweroff>destroy</on_poweroff>
  <on_reboot>restart</on_reboot>
  <on_crash>destroy</on_crash>
  <pm>
    <suspend-to-mem enabled='no'/>
    <suspend-to-disk enabled='no'/>
  </pm>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>'''
    
    # 添加磁盘配置
    for i, volume_info in enumerate(restored_volumes):
        device_name = f"vd{chr(ord('a') + i)}"  # vda, vdb, vdc...
        volume_type = volume_info.get('volume_type', 'qcow2')
        volume_path = volume_info['new_path']
        
        disk_xml = f'''
    <disk type='file' device='disk'>
      <driver name='qemu' type='{volume_type}'/>
      <source file='{volume_path}'/>
      <target dev='{device_name}' bus='virtio'/>'''
        
        # 第一个磁盘设置为启动盘
        if i == 0:
            disk_xml += '''
      <boot order='1'/>'''
        
        disk_xml += '''
    </disk>'''
        
        xml_template += disk_xml
    
    # 添加网络接口配置
    if new_interfaces:
        for interface_info in new_interfaces:
            mac_address = interface_info.get('mac', '52:54:00:00:00:01')
            bridge_name = interface_info.get('bridge', '')
            
            # 基础接口XML
            interface_xml = f'''
    <interface type='bridge'>
      <mac address='{mac_address}'/>
      <source bridge='{bridge_name}'/>
      <model type='virtio'/>'''
            
            # 所有网桥都使用OVS virtualport配置
            interface_xml += f'''
      <virtualport type='openvswitch'>
        <parameters interfaceid='{interface_info.get('interface_id') or interface_info.get('port_id') or str(uuid.uuid4())}'/>
      </virtualport>'''
            
            interface_xml += '''
    </interface>'''
            xml_template += interface_xml
    else:
        # 默认网络接口
        xml_template += '''
    <interface type='bridge'>
      <mac address='52:54:00:00:00:01'/>
      <source bridge='virbr0'/>
      <model type='virtio'/>
      <virtualport type='openvswitch'>
        <parameters interfaceid='{str(uuid.uuid4())}'/>
      </virtualport>
    </interface>'''
    
    # 添加其他设备配置
    xml_template += '''
    <serial type='pty'>
      <target type='isa-serial' port='0'>
        <model name='isa-serial'/>
      </target>
    </serial>
    <console type='pty'>
      <target type='serial' port='0'/>
    </console>
    <input type='tablet' bus='usb'>
      <address type='usb' bus='0' port='1'/>
    </input>
    <input type='mouse' bus='ps2'/>
    <input type='keyboard' bus='ps2'/>
    <graphics type='vnc' port='-1' autoport='yes' listen='0.0.0.0'>
      <listen type='address' address='0.0.0.0'/>
    </graphics>
    <video>
      <model type='cirrus' vram='16384' heads='1' primary='yes'/>
      <address type='pci' domain='0x0000' bus='0x00' slot='0x02' function='0x0'/>
    </video>
    <memballoon model='virtio'>
      <address type='pci' domain='0x0000' bus='0x00' slot='0x05' function='0x0'/>
    </memballoon>
  </devices>
</domain>'''
    
    return xml_template
