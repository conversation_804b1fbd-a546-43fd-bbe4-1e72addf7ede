from typing import Any, Dict, List
import uuid
import logging
import os
import psutil
import socket
import traceback

from app.celery import app
from api.ovs.client import Client as OvsClient
from utils.tools import generate_unique_port_name


@app.task(name="configure_network")
def configure_network(network_name, configuration):
    # 配置网络的逻辑
    print(f"Configuring Network: {network_name} with configuration: {configuration}")
    import time
    time.sleep(3)
    return f"Network {network_name} configured successfully."


@app.task(name="create_bridge")
def create_bridge(form):
    """
    创建网桥
    """
    ip = form.get("host_ip", "")
    switch_id = form.get("switch_id", "")
    card = form.get("card", "")
    name = form.get("name", "")
    client = OvsClient()
    res = client.create_ovs_bridge(client, name)
    if res and card != "":
        try:
            # 绑定物理网卡到网桥
            bind_result = client.bind_physical_interface(client, switch_name=name, interface_name=card)
            if bind_result:
                print(f"Successfully bound interface {card} to bridge {name}")
            else:
                print(f"Failed to bind interface {card} to bridge {name}")
        except Exception as e:
            print(f"Error binding interface {card}: {str(e)}")
            print(f"Trace: {traceback.format_exc()}")  # 添加更详细的错误跟踪
    return form

@app.task(name="create_distributed_bridge")
def create_distributed_bridge(form):
    """
    创建分布式网桥并绑定物理网卡
    """
    print(f"Creating distributed bridge with form: {form}")
    try:
        name = form.get("switch_name", "")
        ips = form.get("remote_ip", "")
        net_interfaces = form.get("net_interfaces", [])  # 物理网卡列表
        
        client = OvsClient()
        
        # 1. 创建 Geneve 隧道
        res = client.add_geneve_tunnel(client, form)
        if not res:
            print(f"Failed to create Geneve tunnel for bridge {name}")
            form["result"] = "failed"
            form["error"] = "Failed to create Geneve tunnel"
            return form

        # 2. 处理物理网卡绑定
        if net_interfaces:
            print(f"Found physical interfaces to bind: {net_interfaces}")
            success_interfaces = []
            failed_interfaces = []
            
            for interface in net_interfaces:
                # 检查是否是测试网卡（根据需要添加其他条件）
                if interface in ("ens3", 'eth0'):
                    print(f"Skipping test interface: {interface}")
                    continue
                    
                try:
                    # 绑定物理网卡到网桥
                    bind_result = client.bind_physical_interface(client, switch_name=name, interface_name=interface)
                    if bind_result:
                        success_interfaces.append(bind_result)
                        print(f"Successfully bound interface {interface} to bridge {name}")
                    else:
                        failed_interfaces.append(interface)
                        print(f"Failed to bind interface {interface} to bridge {name}")
                except Exception as e:
                    failed_interfaces.append(interface)
                    print(f"Error binding interface {interface}: {str(e)}")
                    print(f"Trace: {traceback.format_exc()}")  # 添加更详细的错误跟踪

            # 更新返回结果
            form["bound_interfaces"] = success_interfaces
            if failed_interfaces:
                form["failed_interfaces"] = failed_interfaces

        # 设置最终结果
        if res:
            form["result"] = "success"
            print(f"Successfully created distributed bridge {name}")
        else:
            form["result"] = "failed"
            print(f"Failed to create distributed bridge {name}")

        return form

    except Exception as e:
        print(f"Error in create_distributed_bridge: {str(e)}")
        form["result"] = "failed"
        form["error"] = str(e)
        return form

@app.task(name="del_bridge")
def del_bridge(form):
    """
    删除网桥
    """
    print(f"Deleting bridge with form: {form}")
    name = form.get("switch_name", "")
    client = OvsClient()
    res = client.delete_ovs_bridge(client, name)
    if res:
        print(f"Successfully deleted bridge {name}")
        form["status"] = "success"
    else:
        print(f"Failed to delete bridge {name}")
        form["status"] = "failed"
       
    return form


@app.task(name="domain_create_bridge_port")
def domain_create_bridge_port(form):
    """
    创建虚拟机时的网桥端口操作
    Args:
        form: 包含网络配置信息的字典列表
    Returns:
        dict: 包含操作结果的字典
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始创建网桥端口: {form}")
    
    if not form or not isinstance(form, list):
        logger.error("无效的输入参数")
        return {"status": "error", "message": "无效的输入参数"}

    results = []
    for net_form in form:
        try:
            # 参数验证
            required_fields = ["switch", "switch_group", "port_name"]
            if not all(field in net_form for field in required_fields):
                missing_fields = [field for field in required_fields if field not in net_form]
                logger.error(f"缺少必要字段: {missing_fields}")
                continue

            switch = net_form.get("switch")
            switch_group = net_form.get("switch_group")
            port_name = net_form.get("port_name")

            # 验证交换机信息
            if not switch or not isinstance(switch, dict):
                logger.error(f"无效的交换机信息: {switch}")
                continue

            # 验证端口组信息
            if not switch_group or not isinstance(switch_group, dict):
                logger.error(f"无效的端口组信息: {switch_group}")
                continue

            # 准备创建端口的参数
            port_config = {
                'tap_name': port_name,
                'mtu': switch.get("mtu"),
                'vlan_tag': switch_group.get("vlan_id"),
                'bridge_name': switch.get("name")
            }

            # 创建端口
            # client = OvsClient()
            try:
                # client.create_tap_interface(client, port_config)
                results.append({
                    "status": "success",
                    "port_name": port_name,
                    "switch": switch.get("name"),
                    "vlan_id": switch_group.get("vlan_id")
                })
                logger.info(f"成功创建端口: {port_name}")
            except Exception as e:
                logger.error(f"创建端口失败: {port_name}, 错误: {str(e)}")
                results.append({
                    "status": "error",
                    "port_name": port_name,
                    "error": str(e)
                })

        except Exception as e:
            logger.error(f"处理端口配置时发生错误: {str(e)}")
            results.append({
                "status": "error",
                "error": str(e)
            })

    # 检查是否所有端口都创建成功
    success_count = sum(1 for r in results if r.get("status") == "success")
    
    # 记录操作结果
    if success_count == len(form):
        logger.info("所有端口创建成功")
    elif success_count > 0:
        logger.warning(f"部分端口创建成功: {success_count}/{len(form)}")
    else:
        logger.error("所有端口创建失败")
    
    # 将操作结果添加到原始表单中
    for i, result in enumerate(results):
        if i < len(form):
            form[i]["operation_status"] = result.get("status")
            if result.get("status") == "error":
                form[i]["error"] = result.get("error")
    
    return form

@app.task(name="create_bridge_port")
def create_bridge_port(form):
    """
    在网桥上创建端口
    Args:
        form: 包含网络配置信息的字典列表
    Returns:
        dict: 包含操作结果的字典
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始创建网桥端口: {form}")
    
    if not form or not isinstance(form, list):
        logger.error("无效的输入参数")
        return {"status": "error", "message": "无效的输入参数"}

    results = []
    for net_form in form:
        try:
            # 参数验证
            required_fields = ["switch", "switch_group", "port_name"]
            if not all(field in net_form for field in required_fields):
                missing_fields = [field for field in required_fields if field not in net_form]
                logger.error(f"缺少必要字段: {missing_fields}")
                continue

            switch = net_form.get("switch")
            switch_group = net_form.get("switch_group")
            port_name = net_form.get("port_name")

            # 验证交换机信息
            if not switch or not isinstance(switch, dict):
                logger.error(f"无效的交换机信息: {switch}")
                continue

            # 验证端口组信息
            if not switch_group or not isinstance(switch_group, dict):
                logger.error(f"无效的端口组信息: {switch_group}")
                continue

            # 准备创建端口的参数
            port_config = {
                'tap_name': port_name,
                'mtu': switch.get("mtu"),
                'vlan_tag': switch_group.get("vlan_id"),
                'bridge_name': switch.get("name")
            }

            # 创建端口
            client = OvsClient()
            try:
                client.create_tap_interface(client, port_config)
                results.append({
                    "status": "success",
                    "port_name": port_name,
                    "switch": switch.get("name"),
                    "vlan_id": switch_group.get("vlan_id")
                })
                logger.info(f"成功创建端口: {port_name}")
            except Exception as e:
                logger.error(f"创建端口失败: {port_name}, 错误: {str(e)}")
                results.append({
                    "status": "error",
                    "port_name": port_name,
                    "error": str(e)
                })

        except Exception as e:
            logger.error(f"处理端口配置时发生错误: {str(e)}")
            results.append({
                "status": "error",
                "error": str(e)
            })

    # 检查是否所有端口都创建成功
    success_count = sum(1 for r in results if r.get("status") == "success")
    
    # 记录操作结果
    if success_count == len(form):
        logger.info("所有端口创建成功")
    elif success_count > 0:
        logger.warning(f"部分端口创建成功: {success_count}/{len(form)}")
    else:
        logger.error("所有端口创建失败")
    
    # 将操作结果添加到原始表单中
    for i, result in enumerate(results):
        if i < len(form):
            form[i]["operation_status"] = result.get("status")
            if result.get("status") == "error":
                form[i]["error"] = result.get("error")
    
    return form

@app.task(name="del_bridge_port")
def del_bridge_port(form):
    """
    从网桥上删除端口
    """
    ip = form.get("ip", "")
    client = OvsClient()
    client.delete_tap_interface(client,form)


def get_physical_interfaces() -> List[str]:
    """
    通过读取 /sys/class/net 目录获取物理网卡名称列表
    """
    physical_interfaces = []
    net_dir = '/sys/class/net'
    
    if not os.path.exists(net_dir):
        return []
        
    for interface in os.listdir(net_dir):
        # 跳过虚拟网卡和回环接口
        if interface.startswith(('lo', 'docker', 'veth', 'br-', 'virbr')):
            continue
            
        # 检查是否为物理网卡
        device_path = os.path.join(net_dir, interface, 'device')
        if os.path.exists(device_path):
            physical_interfaces.append(interface)
            
    return physical_interfaces

@app.task(name="get_physical_interfaces_task")
def get_physical_interfaces_task() -> List[Dict[str, Any]]:
    """
    获取本机所有物理网卡信息
    
    Returns:
        List[Dict[str, Any]]: 网卡信息列表，每个网卡包含以下信息：
            - name: 网卡名称
            - mac: MAC地址
            - ipv4: IPv4地址列表
            - ipv6: IPv6地址列表
            - speed: 网卡速度(Mbps)
            - mtu: MTU值
            - status: 网卡状态(up/down)
    """
    interfaces = []
    
    # 获取物理网卡列表
    physical_interfaces = get_physical_interfaces()
    
    # 获取网络接口信息
    net_if_addrs = psutil.net_if_addrs()
    net_if_stats = psutil.net_if_stats()
    
    for name in physical_interfaces:
        interface = {
            'name': name,
            'mac': '',
            'ipv4': [],
            'ipv6': [],
            'speed': 0,
            'mtu': 0,
            'status': 'down'
        }
        
        # 获取MAC地址和IP地址
        if name in net_if_addrs:
            for addr in net_if_addrs[name]:
                if addr.family == socket.AF_PACKET:  # MAC地址
                    interface['mac'] = addr.address
                elif addr.family == socket.AF_INET:  # IPv4
                    interface['ipv4'].append(addr.address)
                elif addr.family == socket.AF_INET6:  # IPv6
                    interface['ipv6'].append(addr.address)
        
        # 获取网卡状态和MTU
        if name in net_if_stats:
            stats = net_if_stats[name]
            interface['mtu'] = stats.mtu
            interface['status'] = 'up' if stats.isup else 'down'
            
            # 获取网卡速度
            try:
                with open(f'/sys/class/net/{name}/speed', 'r') as f:
                    interface['speed'] = int(f.read().strip())
            except (FileNotFoundError, ValueError):
                interface['speed'] = 0
        
        interfaces.append(interface)
    
    return interfaces

# TODO 交换机绑定物理网卡

@app.task(name="switch_bind_physical_interface")
def switch_bind_physical_interface(form):
    """
    绑定物理网卡到交换机
    Args:
        switch_id: 交换机ID
        nic_name: 物理网卡名称
        host_id: 主机ID
    Returns:
        dict: 包含操作结果的字典
    """
    switch_name = form.get("switch_name", "")
    nic_name = form.get("nic_name", "")
    host_id = form.get("host_id", "")
    try:
        # 获取交换机信息
        client = OvsClient()
        result = client.bind_physical_interface(client, switch_name, nic_name)
        if result:
            form["task_status"] = "success"
        else:
            form["task_status"] = "failed"
        
        return form
    
    except Exception as e:
        return form
    
@app.task(name="switch_unbind_physical_interface")
def switch_unbind_physical_interface(form):
    """
    交换机解绑物理网卡
    Args:
        switch_id: 交换机ID
        nic_name: 物理网卡名称
        host_id: 主机ID
    Returns:
        dict: 包含操作结果的字典
    """
    switch_name = form.get("switch_name", "")
    nic_name = form.get("nic_name", "")
    host_id = form.get("host_id", "")
    try:
        # 获取交换机信息
        client = OvsClient()
        result = client.unbind_physical_interface(client, switch_name, nic_name)
        if result:
            form["task_status"] = "success"
        else:
            form["task_status"] = "failed"
        
        return form
    
    except Exception as e:
        return form