from app.celery import app
from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from api.log.log import CustomLogger
from utils.db import get_dbi
import traceback
from celery import Task, group
import requests
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
from config.settings import QUEUE_NAME
from app.agents.vm_tasks import restart_vm
from utils.tools import get_system_info

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()

    

@app.task(name="host_task")
def host_task(form):
    systeminfo = get_system_info()
    if isinstance(form, dict):
        form.update(systeminfo)
    else:
        try:
            form = dict(form)
            form.update(systeminfo)
        except Exception as e:
            print("创建主机任务报错：", e)
    print("增加主机任务函数：", form)
    # 异步调用回调任务
    return form


    

    