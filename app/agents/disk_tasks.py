from app.celery import app
from app.agents.vm_tasks import VmCreateTask
from sqlalchemy.orm import joinedload
from utils.db import get_dbi
from api.libvirt.client import Client as LibvirtClient
from utils.tools import convert_to_bytes
from api.iscsi.iscsi_client import ISCSIManager
import shutil
import subprocess
from api.ceph.mod.volume import CephRBDManager

@app.task(name="create_sub_disk")
def create_sub_disk(vm_name, form):
    print(f"create_sub_disk:{vm_name} {form}")
    res = []
    for disk in form:
        storage_type_code = disk.get("storage_type_code")
        type_code = disk.get("type_code")
        # 检查卷是否存在
        is_exist = disk.get("is_exist")
        if is_exist:
            res.append(disk)
            print("存储卷已存在，跳过")
            continue
        
        print("存储卷不存在，进行创建")
        print("存储池类型为：", disk.get("type_code"))
        print("存储卷类型为：", disk.get("storage_type_code"))
        if type_code == "IP-SAN":
            # 如果是IP-SAN存储卷，直接返回表单数据
            print("IP-SAN存储卷，直接返回表单数据")
            disk["size"] = disk.get("capacity", 0)
            disk["name"] = disk["name"] + "." + disk["volume_format"]
            iscsi_form = {
                "path": disk.get("path", ""),
                "name": disk["disk_name"],
                "size": disk["size"],
                "size_unit": disk.get("disk_unit_type", "GB"),
                "format": disk.get("storage_type_code", "qcow2"),
            }
            res = ISCSIManager.create_disk_volume(iscsi_form)
            if res:
                print("创建IP-SAN存储卷成功")
                
                disk["status"] = 4
                disk["allocation"] = disk["capacity"]
                disk["create_res"] = True
                
            else:
                print("创建IP-SAN存储卷失败")
                disk["create_res"] = False
                
        elif type_code == "local":
            libvirtClient = LibvirtClient()
            pool_name = disk.get("pool_name")
            disk_name = disk.get("disk_name")
            storage_type_code = disk.get("storage_type_code", "qcow2")
            virt_form = {
                "storage_device_id": disk.get("disk_id"),
                "storage_pool_id": disk.get("pool_id"),
                "name": disk_name,
                "path": disk.get("path", ""),
                "encrypt": 0,
                "volume_type": storage_type_code,
                "join_type": 3,
                "capacity": disk.get("capacity", 0),  # 容量单位为字节
                "preallocation": 1,
                "remark": "",
                "storage_pool_name": pool_name,
                "storage_type_code": disk.get("type_code", "local"),
                "volume_format" : storage_type_code,
            }
            
            r = libvirtClient.create_storage_pool_volume(libvirtClient, virt_form)

            if r:
                # 获取创建的卷的详情
                volume = libvirtClient.get_storage_pool_volume_info(libvirtClient, pool_name, disk_name+ "." + storage_type_code)
                print("将存储卷信息转换成数据库信息", volume)
                disk["status"] = volume["type"]
                disk["allocation"] = volume["allocation"]
                disk["create_res"] = True
            else:
                print("创建本地存储卷失败")
                disk["create_res"] = False
                
        elif type_code == "Ceph":
            # Ceph 存储卷创建流程
            print("Ceph 存储卷创建")
            print("参数：", disk)

            try:
                # 获取 Ceph 相关参数
                ceph_pool_name = disk.get("ceph_pool_name", "")  # 使用pool_name而不是ceph_pool_name
                pool_username = disk.get("pool_username", "")
                pool_keyring = disk.get("pool_keyring", "")
                volume_name = disk.get("disk_name", "")  # 使用disk_name而不是name
                capacity = int(disk.get("capacity", 0))
                # 使用从存储设备获取的集群IP信息
                cluster_ips = disk.get("mon_hosts", "")  # 使用cluster_ips而不是ceph_cluster_ip

                if not all([ceph_pool_name, pool_username, pool_keyring, volume_name, capacity, cluster_ips]):
                    print("缺少必要的 Ceph 参数")
                    disk["create_res"] = False
                    res.append(disk)
                    continue

                # 将字节转换为GB
                capacity_gb = capacity / (1024 * 1024 * 1024)  # 转换为GB
                print(f"创建 Ceph 卷: {volume_name}")
                print(f"池名称: {ceph_pool_name}")
                print(f"容量: {capacity_gb:.2f}GB")
                print(f"集群 IPs: {cluster_ips}")

                # 构建集群 IP 列表
                cluster_ips_list = cluster_ips.split(',')

                # 导入 Ceph 卷管理模块
                from api.ceph.mod.volume import QemuImgCephManager

                # 创建 qemu-img Ceph 管理器
                ceph_manager = QemuImgCephManager(
                    cluster_ips=cluster_ips_list,
                    username=pool_username,
                    keyring=pool_keyring,
                    pool_name=ceph_pool_name
                )

                # 使用 qemu-img 创建 RBD 卷
                success, message = ceph_manager.create_volume_qemu(
                    volume_name=volume_name,
                    size_gb=capacity_gb,
                    image_format="raw" if disk.get("storage_type_code") == "raw" else "qcow2"
                )

                if success:
                    print(f"Ceph 卷创建成功: {message}")
                    disk["status"] = 1  # 创建成功状态
                    disk["allocation"] = capacity
                    disk["create_res"] = True
                else:
                    print(f"Ceph 卷创建失败: {message}")
                    disk["create_res"] = False

            except ImportError as e:
                error_msg = f"导入 Ceph 模块失败: {str(e)}"
                print(error_msg)
                disk["create_res"] = False
                res.append(disk)
                continue
            except Exception as e:
                error_msg = f"创建 Ceph 存储卷异常: {str(e)}"
                print(error_msg)
                disk["create_res"] = False
                res.append(disk)
                continue
                
        res.append(disk)
            
        # form1 = {
        #     'storage_pool_name': disk.get("pool"),
        #     # 'name': ".".join([disk.get("disk_name"), disk.get("storage_type_code")]),
        #     'name': ".".join([disk.get("disk_name"), disk.get("disk_type")]),
        #     'storage_type_code': disk.get('storage_type_code', 'qcow2'),
        #     'capacity': convert_to_bytes(disk.get('size'), disk.get('disk_unit_type')) ,  # 转换成字节
        # }
        # if not libvirtClient.ensure_remote_disk_exists(libvirtClient, form1):
        #     return {'status': 'failed', 'message': f"Failed to create volume {form1.get('name')} in pool {form1.get('storage_pool_name')}"}

    return res



@app.task(name="clone_disk")
def clone_disk(disk_info_list):
    """
    disk_info_list: [
        {
            "old_path": "/path/to/source.qcow2",
            "new_path": "/path/to/clone.qcow2",
            "volume_type": "qcow2" or "raw",
            "clone_type": "full" or "linked"  # 默认 full
            # 当为 Ceph 时额外字段:
            "ceph_pool_name": "rbd",
            "pool_username": "admin",
            "pool_keyring": "AQB...==",
            "mon_hosts": "***********:6789,***********:6789"
        },
        ...
    ]
    """
    results = []

    if isinstance(disk_info_list, dict):
        disk_info_list = [disk_info_list]

    for disk_info in disk_info_list:
        src_path = disk_info["old_path"]
        dst_path = disk_info["new_path"]
        clone_type = disk_info.get("clone_type", "full")

        is_ceph = bool(disk_info.get("ceph_pool_name"))

        try:
            if clone_type == "linked":
                if is_ceph:
                    # === Ceph 链接克隆 ===
                    ceph_pool_name = disk_info["ceph_pool_name"]
                    pool_username = disk_info["pool_username"]
                    pool_keyring = disk_info["pool_keyring"]
                    mon_hosts = disk_info["mon_hosts"].split(",")

                    ceph = CephRBDManager(
                        cluster_ips=mon_hosts,
                        username=pool_username,
                        keyring=pool_keyring,
                        pool_name=ceph_pool_name
                    )
                    # 从 old_path 中解析出卷名（去掉 pool 前缀）
                    src_volume = src_path.split("/")[-1]
                    dst_volume = dst_path.split("/")[-1]
                    ok, msg = ceph.clone_volume_linked(src_volume, dst_volume)
                    if not ok:
                        raise RuntimeError(msg)

                else:
                    # 本地/共享存储链接克隆
                    fmt = disk_info.get("volume_type", "qcow2")
                    if fmt != "qcow2":
                        raise RuntimeError("仅 qcow2 支持链接克隆")
                    cmd = ["qemu-img", "create", "-f", "qcow2",
                           "-b", src_path, "-F", fmt, dst_path]
                    subprocess.check_call(cmd)

            else:  # full
                if is_ceph:
                    # === Ceph 全量克隆 ===
                    ceph_pool_name = disk_info["ceph_pool_name"]
                    pool_username = disk_info["pool_username"]
                    pool_keyring = disk_info["pool_keyring"]
                    mon_hosts = disk_info["mon_hosts"].split(",")

                    ceph = CephRBDManager(
                        cluster_ips=mon_hosts,
                        username=pool_username,
                        keyring=pool_keyring,
                        pool_name=ceph_pool_name
                    )
                    src_volume = src_path.split("/")[-1]
                    dst_volume = dst_path.split("/")[-1]
                    ok, msg = ceph.clone_volume_full(src_volume, dst_volume)
                    if not ok:
                        raise RuntimeError(msg)

                else:
                    # 本地/共享存储全克隆
                    shutil.copyfile(src_path, dst_path)

            results.append({"status": "success", "new_path": dst_path, "disk_info": disk_info})

        except Exception as e:
            results.append({"status": "failed", "error": str(e),
                            "old_path": src_path, "new_path": dst_path,
                            "disk_info": disk_info})

    return results