from app.celery import app
from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from api.log.log import CustomLogger
from utils.db import get_dbi
import traceback
from celery import Task, group
import requests
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
from config.settings import QUEUE_NAME
from app.agents.vm_tasks import restart_vm
from app.agents.host_tasks import host_task
from db.model.hci.compute import Host, HostStorageDeviceMapping
from db.model.hci.storage import StorageDevice

import logging

logger = logging.getLogger(__name__)
from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)


@app.task(name="create_host")
def create_host(form):
    """
    分发主机创建操作
    :param form: 创建主机信息
    :return:
    """

    jobs = []
    """
    启动 本地asyn
    启动 本地hci_ctl
    启动 本地hci_agent
    """
    print("分发主机任务")
    host_ip = form.get("host_ip")
    queue_name = "queue_" + host_ip
    
    task_chain = (
        host_task.s(form).set(queue=queue_name) |
        host_task_callback.s().set(queue=QUEUE_NAME)
    )
    
    result = task_chain.apply_async()
    
    print("主机任务创建成功")
    
    return result
    
    # 使用源主机的队列
    #
    # # 分发子任务到对应的主机队列
    # job = migrate_vm.s(migration_form).set(queue=queue_name)
    # jobs.append(job)

    # if jobs:
    #     # # 使用 group 来聚合所有子任务，并指定最终的回调函数
    #     # job_group = group(jobs) | start_vm_migration_callback.s().set(queue=QUEUE_NAME)
    #     # result = job_group.apply_async()
    #     # print(f"已分发 {len(jobs)} 个虚拟机迁移任务")
    #     name = form.get("name", "")
    #     return f"分发了 {name} 的任务"
    # else:
    #     print("没有需要创建的主机")
    #     return "没有需要创建的主机"
    

@app.task(name="host_task_callback") 
def host_task_callback(form):
    try:
        print(f"主机任务回调函数：{form}")
        host_id = form.get("host_id")
        cpu_cores = form.get("cpu_cores")
        memory = form.get("memory")
        storage = form.get("storage")
        model = form.get("model")
        vendor = form.get("vendor")

        # 参数校验
        if not host_id:
            return {"msg": "缺少主机ID", "code": 400}
        if cpu_cores is not None and (not isinstance(cpu_cores, int) or cpu_cores <= 0):
            return {"msg": "CPU核数无效", "code": 400}
        if memory is not None and (not isinstance(memory, int) or memory <= 0):
            return {"msg": "内存无效", "code": 400}
        if storage is not None and (not isinstance(storage, int) or storage <= 0):
            return {"msg": "存储无效", "code": 400}

        dbi = get_dbi()
        with dbi.session_scope() as session:
            host = session.query(Host).filter(Host.id == host_id).first()
            if not host:
                return {"msg": "未找到主机", "code": 404}
            if cpu_cores is not None:
                host.cpu_cores = cpu_cores
            if memory is not None:
                host.memory = memory
            if storage is not None:
                host.storage = storage

            # 2. 创建或更新存储设备
            device = session.query(StorageDevice).filter(
                StorageDevice.ip_mgmt == host.ip).first()
            if not device:
                device = StorageDevice(
                    ip_mgmt=host.ip,
                    device_type="local",
                    device_name=host.name,
                    total_capacity=host.storage,
                    model=model,
                    vendor=vendor
                )
                session.add(device)
                session.flush()  # 获取设备ID
            else:
                # 更新已有设备信息
                device.total_capacity = host.storage
                device.model = model
                device.vendor = vendor

            # 3. 创建存储设备和主机的关联（查重）
            mapping_exists = session.query(HostStorageDeviceMapping).filter_by(
                host_id=host.id, storage_device_id=device.id).first()
            if not mapping_exists:
                device_mapping = HostStorageDeviceMapping(
                    host_id=host.id,
                    storage_device_id=device.id
                )
                session.add(device_mapping)
            session.commit()
        return {"msg": "主机信息更新成功", "code": 200}
    except Exception as e:
        print("主机任务回调函数报错：", str(e))
        return {"msg": f"主机任务回调异常: {str(e)}", "code": 500}