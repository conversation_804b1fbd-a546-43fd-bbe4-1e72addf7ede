from app.celery import app
import time

@app.task(name="add_tasks", ignore_result=False)
def add_tasks(x, y):
    """
    测试任务
    """
    
    print("add_tasks")
    print(x + y)
    return  x + y



@app.task(name="sub_tasks")
def sub_tasks(x, y):
    """
    测试任务
    """
    
    print("sub_tasks")
    print(x - y)
    return  x - y

@app.task(name="on_success_callback")
def on_success_callback(result):
    print(f"Callback got result: {result}")