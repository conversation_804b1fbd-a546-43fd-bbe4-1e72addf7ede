from app.celery import app
from app.tasks.vm_tasks import VmCreateTask
from sqlalchemy.orm import joinedload
from utils.db import get_dbi
from db.model.hci.storage import StoragePool, StorageVolume
from api.libvirt.client import Client as LibvirtClient
from db.model.hci.user_resource_quota import (
    UserQuota
)
from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)

from api.log.log import CustomLogger
import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


@app.task(base=VmCreateTask, name="create_disk")
def create_disk(vm_name, resources):
    
    #创建虚拟机之前先创建硬盘
    print(f"Creating VM Disk: {vm_name} with resources: {resources}")

    new_disks = []
    disks = resources.get("disk", [])
    
    for disk in disks:
        disk_type = disk.get("disk_type")
        if disk_type == "selected_volume":
            with  get_dbi().session_scope() as session:
                volume_id = disk.get("volume_id")
                volume = session.query(StorageVolume).filter(StorageVolume.id==volume_id).options(joinedload(StorageVolume.pool)).first()
                source_path = "%s/%s" % (volume.path, volume.name)
                
                new_disk = {
                    "source": source_path,
                    "format": disk.get("storage_type_code"),
                    "size": disk.get("size"),
                    "volume_type": disk_type

                }
                new_disks.append(new_disk)
                
        if disk_type == "new_volume":
            
            print("创建存储卷")
            pool_id = disk.get("pool_id")
            volume_name = disk.get("name")
            host_ip = "xxx" # 从pool拿到在哪个主机上创建
            pool_path = "xxx" # 从pool拿到在哪个路径上创建
            pool_name = "xxx" # 从pool拿到在哪个路径上创建
            
            form = {} # 从disk拿到创建卷的信息
            
            libvirtClient = LibvirtClient(host=host_ip)
            form = {} # 从disk拿到创建卷的信息
            r = libvirtClient.create_storage_pool_volume(libvirtClient, form)

            if not r:
                return {"status": "failed", "error": "创建存储卷失败"}
            

            volume = libvirtClient.get_storage_pool_volume_info(libvirtClient, pool_name, volume_name)
            print("将存储卷信息转换成数据库信息")
            data = {
                "name": disk.get("name"),
                "storage_pool_id":  pool_id,
                "type": disk.get("storage_type_code"),
                "path": pool_path,
                "status": 1,
                "capacity": disk.get("size") # 单位是字节
            }
            
            #写入数据库
            

        
        
            new_disks.append(new_disk)
        
    return {"status": "success", "form": resources, "disk": new_disks}


# @app.task(base=VmCreateTask, name="create_sub_disk")
# def create_sub_disk(vm_name, form):
#     disk_list = form.get("disk", [])
#     host_ip = form.get("host_ip", "")
#     libvirtClient = LibvirtClient(host=host_ip)
#     for disk in disk_list:
#         # 检查卷是否存在
#         form1 = {
#             'storage_pool_name': disk.get("pool"),
#             'name': ".".join([disk.get("disk_name"), disk.get("storage_type_code")]),
#             'storage_type_code': disk.get('storage_type_code', 'qcow2'),
#             'capacity': convert_to_bytes(disk.get('size'), disk.get('disk_unit_type')) ,  # 转换成字节
#         }
#         if not libvirtClient.ensure_remote_disk_exists(libvirtClient, form1):
#             return {'status': 'failed', 'message': f"Failed to create volume {form1.get('name')} in pool {form1.get('storage_pool_name')}"}

#     return {"status": "success", "form": form, "disk": disk_list}

@app.task(name="create_sub_disk_callback")
def create_sub_disk_callback(form):
    print(f"create_sub_disk_callback:{form}")
    
    dbi = get_dbi()
    with dbi.session_scope() as session:
      
        for disk in form:
            role = disk.get("role", "")
            username = disk.get("username", "")
            disk_name = disk.get("disk_name", "")
            disk_id = disk.get("disk_id")
            status = disk.get("status", 0)
            allocation = disk.get("allocation", 0)
            create_res = disk.get("create_res", False)
            user_quota_id = disk.get("user_quota_id", "")
            capacity = disk.get("capacity", 0)

            is_exist = disk.get("is_exist")
            if is_exist:
                print("磁盘已存在")
                continue
            if create_res:
                print("磁盘信息：", disk)
                print("新的存储卷创建成功，开始修改数据库信息")
                volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                print(volume.id)
                if volume:
                    # volume.status = result["status"]
                    # volume.allocation = result["allocation"]
                    volume = session.query(StorageVolume).filter_by(id=disk_id).update(
                        {StorageVolume.status: status, 
                         StorageVolume.allocation: allocation})
                    session.commit()
                if user_quota_id:
                    # 查找用户配额记录
                    user_quota = session.query(UserQuota).filter_by(id=user_quota_id).first()
                    if user_quota:
                        # 累加已用存储
                        user_quota.storage_used = (user_quota.storage_used or 0) + capacity
                        session.commit()
                    else:
                        print(f"未找到用户配额记录: {user_quota_id}")
                        
                print("创建失败日志记录")
                new_logger.log(
                    username, "创建虚拟机存储卷", "创建", "成功", role,
                f"创建虚拟机存储卷: {disk_name} 成功")
                
            else:
                print("创建失败日志记录")
                new_logger.log(
                    username, "创建虚拟机存储卷", "创建", "失败", role,
                f"创建虚拟机存储卷: {disk_name} 失败")
    return form
                            


@app.task(base=VmCreateTask, name="create_sub_disk_with_template")
def create_sub_disk_with_template(vm_name, template):
    disk_path = template.get("disk_path")
    disk_name = template.get("disk_name")
    disk_type = template.get("disk_type", "qcow2")
    host_ip = template.get("host_ip", "")
    libvirtClient = LibvirtClient(host=host_ip)
    # 检查卷是否存在
    form1 = {
        'storage_pool_name': disk_path,
        'name': ".".join([disk_name, disk_type]),
        'storage_type_code': disk_type,
        'capacity': convert_to_bytes("20", "GB") ,  # 转换成字节
    }
    if not libvirtClient.ensure_remote_disk_exists(libvirtClient, form1):
        return {'status': 'failed', 'message': f"Failed to create volume {form1.get('name')} in pool {form1.get('storage_pool_name')}"}

    return {"status": "success", "template": template, "disk": disk_name}

def convert_to_bytes(size: str, unit: str) -> str:
    """
    将不同内存单位转换为字节，处理字符串类型的输入输出

    参数:
        size: 数值(字符串)，如 "4"、"4096"
        unit: 单位(字符串)，如 "KiB", "MiB", "GiB"

    返回:
        bytes_str: 转换后的字节数(字符串)
    """
    # 定义转换因子
    units = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 * 1024,
        'GB': 1024 * 1024 * 1024
    }

    # 检查单位是否支持
    unit = unit.upper()
    if unit not in units:
        raise ValueError(f"不支持的单位: {unit}. 支持的单位有: {', '.join(units.keys())}")

    try:
        # 将输入字符串转换为浮点数进行计算
        size_num = float(size)
        # 转换为字节并转回字符串
        bytes_str = str(int(size_num * units[unit]))
        return bytes_str
    except ValueError:
        raise ValueError(f"无效的数值: {size}")


def convert_to_gigabytes(size: str, unit: str) -> str:
    """ 将不同内存单位转换为 GB，并且输出 GB 单位的值
    参数:
        size: 数值（字符串），如 "4"、"4096"
        unit: 单位（字符串），如 "KB", "MB", "GB"

    返回:
        gb_str: 转换后的 GB 值（字符串）
    """
    # 定义转换因子
    units = {
        'KB': 1024,
        'MB': 1024 * 1024,
        'GB': 1024 * 1024 * 1024
    }

    # 检查单位是否支持
    unit = unit.upper()
    if unit not in units:
        raise ValueError(f"不支持的单位: {unit}. 支持的单位有: {', '.join(units.keys())}")

    try:
        # 将输入字符串转换为浮点数进行计算
        size_num = int(size)
        # 转换为字节
        bytes_num = size_num * units[unit]
        # 转换为 GB 并转回字符串
        gb_str = bytes_num / (1024 * 1024 * 1024)
        return gb_str
    except ValueError:
        raise ValueError(f"无效的数值: {size}")
