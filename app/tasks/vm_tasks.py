from sqlalchemy.orm import joinedload

from app.celery import app
from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient

from utils.db import get_dbi
from db.model.hci.network import Switch, SwitchPorts, SwitchPortGroups
from db.model.hci.compute import Domain, DomainDisk, DomainInterface, Host, DomainXml
from db.model.hci.storage import StoragePool, StorageVolume
from db.model.hci.backup import DomainBackup, BackupVolumeMapping
import traceback
from celery import Task, group
import requests
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
from config.settings import QUEUE_NAME
from app.agents.vm_tasks import (
    open_vm, close_vm, destroy_vm, pause_vm,
    recover_vm, reboot_vm, restart_vm,
    delete_vm, backup_delete,
)
from db.model.hci.user_resource_quota import (
    UserQuota, UserVmAssignment, UserHostAssignment, UserClusterAssignment, UserStoragePool
)
from api.log.log import CustomLogger
import logging

logger = logging.getLogger(__name__)
from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)
from datetime import datetime


class VmCreateTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )


class VmCreateCallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )


@app.task(name="create_vm_callback_abandon")
def create_vm_callback_abandon(results):
    # 废弃
    print(f"Task completed! The result is: {results}")

    # dbi = get_dbi()

    # # with dbi.session_scope() as session:
    # #     domain_list = session.query(Domain).all()
    # #     print(domain_list)

    # 通过results 来合并虚机的json信息
    form = {}
    interface = []
    disk = []
    domain_id = ""
    domain_name = ""

    # 测试等10秒
    import time
    time.sleep(20)

    for result in results:
        if result.get("status") == "failed":
            print("VM creation failed.")
            print(result.get("error"))
            return False

        if result.get("form", {}):
            form = result.get("form", {})
            if form:
                domain_id = form.get("domain_id", "")
                domain_name = form.get("name", "")

        if result.get("interface", []):
            interface = result.get("interface", [])

        if result.get("disk", []):
            disk = result.get("disk", [])

    data = form.copy()
    data["interface"] = interface
    data["disk"] = disk

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = data.get("host_ip", "")
    # client = LibvirtClient(host_ip)
    # res = client.create_dom_vm(client, data)

    print("VM creation successful.")

    dbi = get_dbi()
    with dbi.session_scope() as session:

        # 通过domain_id来修改domain表的状态
        domain = session.query(Domain).filter(Domain.id == domain_id).first()
        domain.status = "running"
        session.add(domain)

        # 根据disk_id 需要修改盘和虚机的关系
        disks = data.get("disk", [])
        for disk in disks:
            disk_id = disk.get("disk_id", "")
            pass

        # 根据port_id 需要修改网卡和虚机的关系
        interfaces = data.get("interface", [])
        for interface in interfaces:
            port_id = interface.get("port_id", "")
            pass

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdroms = data.get("cdrom", [])
        for cdrom in cdroms:
            cdrom_id = cdrom.get("cdrom_id", "")
            pass

    # TODO 用代理方式
    nameko_config = {
        'AMQP_URI': settings.MSG_AMQP_URI
    }
    body_data = {"id": domain_id, "name": domain_name}
    MESSAGE_EVENT = "refresh"
    event = MESSAGE_EVENT
    channel = "vm_list"
    with ClusterRpcProxy(nameko_config) as cluster_rpc:
        websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
        websocket_hub.broadcast(channel, event, body_data)

    # TODO 发送消息总线上 用rest api方式
    # channel_vm_list_path = "http://10.168.2.4:9000/send/vm_list"
    # data = {"id": "vm_table", "action": "refresh"}

    # #requests 发送 rest
    # # Send the POST request
    # response = requests.post(channel_vm_list_path, json=data)
    # # Check the response
    # if response.status_code == 200:
    #     print("Request was successful")
    #     print("Response data:", response.json())
    # else:
    #     print(f"Request failed with status code: {response.status_code}")
    #     print("Response content:", response.text)


@app.task(base=VmCreateCallbackTask, name="hello")
def hello(results):
    print("hello")


@app.task(base=VmCreateCallbackTask, name="create_vm_callback_new")
def create_vm_callback_new(info, result):
    print(f"Task completed! The result is: {result}")
    if not result:
        print("VM creation failed.")
        return False

    if result.get("status") == "failed":
        print("VM creation failed.")
        print(result.get("error"))
        return False

    domain_id = ""
    domain_name = ""

    # 测试等10秒
    import time
    time.sleep(5)

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = result.get("host_ip", "")
    client = LibvirtClient(host_ip)
    res = client.create_dom_vm(client, result)

    print("VM creation successful.")

    dbi = get_dbi()
    with dbi.session_scope() as session:

        if result.get("domain_id"):
            domain_id = result.get("domain_id", "")
            session.query(Domain).filter(Domain.id == domain_id).update(
                {Domain.status: "running"})
            session.commit()

        # 根据disk_id 需要修改盘和虚机的关系
        if result.get("disk", []):
            disks = result.get("disk", [])
            for disk in disks:
                disk_id = disk.get("disk_id", "")
                pass

        # 根据port_id 需要修改网卡和虚机的关系
        if result.get("interface", []):
            interfaces = result.get("interface", [])
            for interface in interfaces:
                port_id = interface.get("port_id", "")
                pass

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdroms = result.get("cdrom", [])
        for cdrom in cdroms:
            cdrom_id = cdrom.get("cdrom_id", "")
            pass

    # #TODO 用代理方式
    # nameko_config = {
    #     'AMQP_URI': settings.MSG_AMQP_URI
    # }
    # body_data = {"id": domain_id, "name": domain_name }
    # MESSAGE_EVENT = "refresh"
    # event = MESSAGE_EVENT
    # channel = "vm_list"
    # with ClusterRpcProxy(nameko_config) as cluster_rpc:
    #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
    #     websocket_hub.broadcast(channel, event, body_data)


@app.task(name="create_vm_callback")
def create_vm_callback(result):

    role = result.get("role", "")
    username = result.get("username", "")
    vm_name = result.get("vm_name", "")
    vm_xml = result.get("vm_xml", "")

    print(f"create_vm_callback:{result}")
    domain_id = result.get("domain_id")
    host_id = result.get("host_id")
    host_ip = result.get("host_ip", "")
    user_quota_id = result.get("user_quota_id", "")

    task_result = result.get("task_status", False)

    if task_result:
        dbi = get_dbi()
        with dbi.session_scope() as session:
            spice_port = result.get("spice_port", 0)
            vnc_port = result.get("vnc_port", 0)
            status = result.get("status", "error")

            if result.get("domain_id"):
                domain_id = result.get("domain_id", "")
                domain = session.query(Domain).filter(
                    Domain.id == domain_id).first()
                if domain:
                    domain.status = status
                    domain.spice_port = spice_port
                    domain.vnc_port = vnc_port
                    session.commit()
                else:
                    print(f"未找到虚拟机: {domain_id}")

            # 更新配额
            if user_quota_id and domain:
                user_quota = session.query(UserQuota).filter_by(
                    id=user_quota_id).first()
                if user_quota:
                    user_quota.cpu_used = (
                        user_quota.cpu_used or 0) + (domain.vcpu or 0)
                    user_quota.memory_used = (
                        user_quota.memory_used or 0) + (domain.memory or 0)
                    session.commit()
                else:
                    print(f"未找到用户配额记录: {user_quota_id}")

            # 根据disk_id 需要修改盘和虚机的关系
            disks = result.get("disk", [])
            domain_disks = []
            for disk in disks:
                print("开始处理磁盘和虚拟机关系")
                print("disk信息", disk)
                disk_id = disk.get("disk_id", "")
                pool_id = disk.get("pool_id", "")
                domain_disk = DomainDisk(
                    domain_id=domain_id,
                    storage_pool_id=pool_id,
                    storage_vol_id=disk_id,
                    type_code="file",  # 默认文件类型
                    device="disk",     # 默认磁盘设备
                    dev="vda",         # 默认设备名（可根据实际情况调整）
                    bus="virtio",      # 默认总线类型
                    qemu_type=disk.get("storage_type_code", "qcow2"),  # 默认磁盘格式
                    boot_order=0,      # 默认引导顺序
                    host_id=host_id,  # 必须提供 host_id（外键）
                )
                domain_disks.append(domain_disk)
            if domain_disks:
                session.add_all(domain_disks)

            # 根据port_id 需要修改网卡和虚机的关系
            interfaces = result.get("interface", [])
            domain_interfaces = []
            for interface in interfaces:
                print("开始处理网卡和虚拟机关系")
                print("interface信息", interface)
                port_id = interface.get("switch_port_id", "")
                ip = interface.get("ip", "")
                mac = interface.get("mac", "")
                domain_interface = DomainInterface(
                    domain_id=domain_id,
                    switch_port_id=port_id,
                    type_code="bridge",  # 默认桥接模式
                    mac=mac,  # 生成随机或默认 MAC
                    model="virtio",     # 默认网卡型号
                    ip=ip,
                )
                domain_interfaces.append(domain_interface)
            if domain_interfaces:
                session.add_all(domain_interfaces)

            # 根据cdrom_id 需要修改光驱和虚机的关系
            cdrom_disks = []
            cdroms = result.get("cdrom", [])
            for cdrom in cdroms:
                disk_id = cdrom.get("disk_id", "")
                if not disk_id:
                    continue
                pool_id = cdrom.get("pool_id", "")
                cdrom_disk = DomainDisk(
                    domain_id=domain_id,
                    storage_pool_id=pool_id,
                    storage_vol_id=disk_id,
                    type_code="file",   # 光驱通常是文件
                    device="cdrom",     # 设备类型为光驱
                    dev="hda",          # 光驱通常用 ide 总线
                    bus="ide",          # 光驱总线类型
                    qemu_type="raw",    # 光驱格式通常为 raw
                    boot_order=1,       # 光驱引导顺序
                    host_id=host_id,  # 必须提供 host_id
                )
                cdrom_disks.append(cdrom_disk)
            if cdrom_disks:
                session.add_all(cdrom_disks)

            if vm_xml != "":
                xml = DomainXml(
                    domain_id=domain_id,
                    xml_content=vm_xml,
                    xml_type="domain",
                    version=1,
                    is_active=True,
                    description="虚拟机创建时的初始XML配置",
                    created_by=username
                )
                session.add(xml)
                session.commit()

        new_logger.log(
            username, "虚拟机", "创建", "成功", role, "{} 创建云主机: {},成功".format(
                role, vm_name)
        )
    else:

        new_logger.log(
            username, "虚拟机", "创建", "失败", role, "{} 创建云主机: {},失败".format(
                role, vm_name)
        )

    # #TODO 用代理方式
    # nameko_config = {
    #     'AMQP_URI': settings.MSG_AMQP_URI
    # }
    # body_data = {"id": domain_id, "name": domain_name }
    # MESSAGE_EVENT = "refresh"
    # event = MESSAGE_EVENT
    # channel = "vm_list"
    # with ClusterRpcProxy(nameko_config) as cluster_rpc:
    #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
    #     websocket_hub.broadcast(channel, event, body_data)


@app.task(name="clone_vm_callback")
def clone_vm_callback(result):
    print(f"clone_vm_callback:{result}")

    role = result.get("role", "")
    username = result.get("username", "")
    vm_name = result.get("vm_name", "")

    task_result = result.get("task_status", False)
    clone_tag = result.get("clone_tag", "full")

    domain_id = result.get("domain_id")
    # host_id = result.get("host_id")
    # host_ip = result.get("host_ip", "")

    dbi = get_dbi()

    if task_result:

        with dbi.session_scope() as session:
            spice_port = result.get("spice_port", "")
            vnc_port = result.get("vnc_port", "")
            status = result.get("status", "")

            if result.get("domain_id"):
                domain_id = result.get("domain_id", "")
                session.query(Domain).filter(Domain.id == domain_id).update(
                    {Domain.status: status,
                     Domain.spice_port: spice_port,
                     Domain.vnc_port: vnc_port
                     })
                session.commit()
                print("记录克隆成功日志")

                if clone_tag == "full":
                    print("记录完全克隆成功日志")
                    new_logger.log(
                        username, "虚拟机", "完全克隆", "成功", role,
                        "虚拟机完全克隆: {},成功".format(vm_name)
                    )
                if clone_tag == "link":
                    print("记录链接克隆成功日志")
                    new_logger.log(
                        username, "虚拟机", "链接克隆", "成功", role,
                        "虚拟机链接克隆: {},成功".format(vm_name)
                    )
                
    else:
        if clone_tag == "full":
            print("记录完全克隆成功日志")
            new_logger.log(
                username, "虚拟机", "完全克隆", "失败", role,
                "虚拟机完全克隆: {},失败".format(vm_name)
            )
            
        if clone_tag == "link":
            print("记录链接克隆成功日志")
            new_logger.log(
                username, "虚拟机", "链接克隆", "失败", role,
                "虚拟机链接克隆: {},失败".format(vm_name)
            )
            


@app.task(name="clone_vm")
def clone_vm(form):
    """
    虚拟机克隆
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient(host)
    client.vm_clone(client, vm_name)
    print(f"克隆虚拟机 VM: {vm_name}")
    return f"VM {vm_name} clone successfully."



@app.task(name="distach_op_vm_del")
def distach_op_vm_del(form):
    """
    删除虚拟机任务分发
    :param form:
    :return:
    """
    # print(f"distach_op_vm_del: {form}")
    
    jobs = []
    for target in form:
        print(f"target: {target}")
        
        ip = target['host']['ip']
        data = target
        queue_name = "queue_" + ip
        print(f"queue_name: {queue_name}")

        # 分发子任务
        job = delete_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | delete_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="delete_vm_callback")
def delete_vm_callback(form):
    """
    删除单个
    :param form:
    :return:
    """
    print(f"delete_vm_callback:{form}")
    dbi = get_dbi()

    for vm in form:
        username = vm.get("username", "")
        role = vm.get("role", "")
        vm_name = vm.get("vm_name", "")
        _id = vm.get("id", "")
        task_status = vm.get("task_status", "")
        if task_status == "success":
            with dbi.session_scope() as session:
                # 判断用户和虚拟机的绑定关系
                # 1. 判断用户和虚拟机的绑定关系
                user_vm_assignment = session.query(UserVmAssignment).filter(
                    UserVmAssignment.vm_id == _id
                ).first()
                if user_vm_assignment:
                    user_id = user_vm_assignment.user_id
                    # 2. 获取用户配额信息
                    user_quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
                    # 3. 获取虚拟机资源信息
                    domain = session.query(Domain).filter(Domain.id == _id).first()
                    if user_quota and domain:
                        # 扣减配额（防止负数）
                        user_quota.cpu_used = max((user_quota.cpu_used or 0) - (domain.vcpu or 0), 0)
                        user_quota.memory_used = max((user_quota.memory_used or 0) - (domain.memory or 0), 0)
                
                
                    # 删除用户和虚拟机的绑定关系
                    session.query(UserVmAssignment).filter(UserVmAssignment.vm_id == _id).delete()

                # 删除虚拟机和磁盘的绑定关系，磁盘本身不做删除
                session.query(DomainDisk).filter(DomainDisk.domain_id == _id).delete()
                
                # 查询和虚拟机绑定的网络端口id
                switch_port_ids_info = session.query(DomainInterface.switch_port_id)\
                                    .filter(DomainInterface.domain_id == _id)\
                                    .all()  # 返回的是元组列表，如 [(1,), (2,)]
                # 提取纯 ID 列表
                switch_port_ids = [id[0] for id in switch_port_ids_info if id[0] is not None]
                
                # 批量删除 switch_port 表中的记录
                # 删除相关的网络端口的数据，
                # 网络端口只有在虚拟机运行的时候才会实际创建，所以虚拟机删除的时候将网络端口数据删除
                if switch_port_ids:
                    deleted_count = session.query(SwitchPorts)\
                                        .filter(SwitchPorts.id.in_(switch_port_ids))\
                                        .delete(synchronize_session=False)
                # 删除虚拟机和网络端口的绑定关系                      
                session.query(DomainInterface).filter(DomainInterface.domain_id == _id).delete()
                
                
                
                # 删除对应的虚拟机数据
                session.query(Domain).filter(Domain.id == _id).delete()
                session.commit()

            new_logger.log(
                username, "虚拟机", "删除虚拟机", "成功", role,
                f"删除虚拟机: {vm_name} 成功"
            )

        if task_status == "failed":
            e = vm.get("error", "")
            print(f"删除虚拟机 {vm_name} 失败: {str(e)}")
            new_logger.log(
                username, "虚拟机", "删除虚拟机", "失败", role,
                f"删除虚拟机: {vm_name} 失败, 错误: {str(e)}"
            )
    print(f"删除虚拟机任务执行完成")
    return f"VM {vm_name} deleted successfully."


@app.task(name="distach_op_vm_open")
def distach_op_vm_open(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = open_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | open_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


# @app.task(name="open_vm")
# def open_vm(form):
#     """
#     开机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)

#     client.start_vm(client, vm_name)
#     print(f"虚拟机开机 VM: {vm_name}")
#     return f"开机虚拟机成功"

@app.task(name="open_vm_callback")
def open_vm_callback(form):
    """
    开机
    :param form:
    :return:
    """
    print("open_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "开机", "成功", role,
                "虚拟机开机: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"开机虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "开机", "失败", role,
                "虚拟机开机: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()

    result = f"虚拟机开机任务执行完成 开机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


@app.task(name="distach_op_vm_close")
def distach_op_vm_close(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = close_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | close_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="close_vm_callback")
def close_vm_callback(form):
    """
    关机
    :param form:
    :return:
    """
    print("close_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "关机", "成功", role,
                "虚拟机关机: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            new_logger.log(
                username, "虚拟机", "关机", "失败", role,
                "虚拟机关机: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutoff",
                 Domain.spice_port: 0,  # 清空动态端口
                 Domain.vnc_port: 0      # 清空动态端口
                 },
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机关机任务执行完成 关机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="close_vm")
# def close_vm(form):
#     """
#     关机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.stop_vm(client, vm_name)
#     print(f"虚拟机关机 VM: {vm_name}")
#     return f"关机虚拟机成功"

@app.task(name="distach_op_vm_destroy")
def distach_op_vm_destroy(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = destroy_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | destroy_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="destroy_vm_callback")
def destroy_vm_callback(form):
    """
    强制关机
    :param form:
    :return:
    """
    print("destroy_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "强制关机", "成功", role,
                "虚拟机强制关机: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"强制关机虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "强制关机", "失败", role,
                "虚拟机强制关机: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutoff",
                 Domain.spice_port: 0,  # 清空动态端口
                 Domain.vnc_port: 0      # 清空动态端口
                 },
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机强制关机任务执行完成 强制关机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="destroy_vm")
# def destroy_vm(form):
#     """
#     强制关机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.force_stop_vm(client, vm_name)
#     print(f"虚拟机强制关机 VM: {vm_name}")
#     return f"强制关机虚拟机成功"


@app.task(name="distach_op_vm_pause")
def distach_op_vm_pause(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = pause_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | pause_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="pause_vm_callback")
def pause_vm_callback(form):
    """
    暂停
    :param form:
    :return:
    """
    print("pause_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "暂停", "成功", role,
                "虚拟机暂停: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"暂停虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "暂停", "失败", role,
                "虚拟机暂停: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "paused"},
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机暂停任务执行完成 暂停成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="pause_vm")
# def pause_vm(form):
#     """
#     暂停
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.suspend_vm(client, vm_name)
#     print(f"虚拟机暂停 VM: {vm_name}")
#     return f"批量暂停虚拟机成功"


@app.task(name="distach_op_vm_recover")
def distach_op_vm_recover(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = recover_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | recover_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="recover_vm_callback")
def recover_vm_callback(form):
    """
    恢复
    :param form:
    :return:
    """
    print("recover_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "恢复", "成功", role,
                "虚拟机恢复: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"恢复虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "恢复", "失败", role,
                "虚拟机恢复: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "running"},
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机恢复任务执行完成 恢复成功 {success_count} 失败 {failed_count}"
    print(result)
    return result

# @app.task(name="recover_vm")
# def recover_vm(form):
#     """
#     恢复
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.resume_vm(client, vm_name)
#     print(f"虚拟机恢复 VM: {vm_name}")
#     return f"批量恢复虚拟机成功"


@app.task(name="distach_op_vm_reboot")
def distach_op_vm_reboot(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = reboot_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | reboot_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="reboot_vm_callback")
def reboot_vm_callback(form):
    """
    重启
    :param form:
    :return:
    """
    print("reboot_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据
    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "重启", "成功", role,
                "虚拟机重启: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"重启虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "重启", "失败", role,
                "虚拟机重启: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()

    result = f"虚拟机重启任务执行完成 重启成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="reboot_vm")
# def reboot_vm(form):
#     """
#     重启
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.reboot_vm(client, vm_name)
#     print(f"虚拟机重启 VM: {vm_name}")
#     return f"批量重启虚拟机成功"


@app.task(name="distach_op_vm_restart")
def distach_op_vm_restart(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = restart_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | restart_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="restart_vm_callback")
def restart_vm_callback(form):
    """
    强制重启
    :param form:
    :return:
    """
    print("restart_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据
    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "强制重启", "成功", role,
                "虚拟机强制重启: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"强制重启虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "强制重启", "失败", role,
                "虚拟机强制重启: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()

    result = f"虚拟机强制重启任务执行完成 强制重启成功 {success_count} 失败 {failed_count}"
    print(result)
    return result

# @app.task(name="restart_vm")
# def restart_vm(form):
#     """
#     强制重启
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.force_reboot_vm(client, vm_name)
#     print(f"虚拟机强制重启 VM: {vm_name}")
#     return f"批量强制重启虚拟机成功"


@app.task(name="batch_delete_vm")
def batch_delete_vm(form):
    """
    批量删除
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        # 删除虚拟机的逻辑
        client.del_vm(client, vm_name)
        print(f"删除虚拟机 VM: {vm_name}")
    return f"批量删除虚拟机成功"


@app.task(name="batch_open_vm")
def batch_open_vm(form):
    """
    批量开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.start_vm(client, vm_name)
        print(f"虚拟机开机 VM: {vm_name}")
    return f"批量开机虚拟机成功"


@app.task(name="batch_close_vm")
def batch_close_vm(form):
    """
    批量关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
    return f"批量关机虚拟机成功"


@app.task(name="batch_destroy_vm")
def batch_destroy_vm(form):
    """
    批量强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
    return f"批量强制关机虚拟机成功"


@app.task(name="batch_pause_vm")
def batch_pause_vm(form):
    """
    批量暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
    return f"批量暂停虚拟机成功"


@app.task(name="batch_recover_vm")
def batch_recover_vm(form):
    """
    批量恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
    return f"批量恢复虚拟机成功"


@app.task(name="batch_reboot_vm")
def batch_reboot_vm(form):
    """
    批量重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
    return f"批量重启虚拟机成功"


@app.task(name="batch_restart_vm")
def batch_restart_vm(form):
    """
    批量强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_reboot_vm(client, vm_name)
        print(f"虚拟机强制重启 VM: {vm_name}")
    return f"批量强制重启虚拟机成功"


@app.task(name="create_start_vm_migration")
def create_start_vm_migration(form):
    """
    分发虚拟机迁移操作
    :param form: 迁移任务列表，每个元素包含虚拟机迁移信息
    :return:
    """

    from app.agents.vm_tasks import migrate_vm

    jobs = []
    for target in form:
        # 从每个target中获取迁移信息
        vm_name = target.get("vm_name", "")
        vm_id = target.get("vm_id", "")
        source_host = target.get("source_host", "")
        target_host = target.get("target_host", "")
        migration_type = target.get("migration_type", "live")
        username = target.get("username", "")
        role = target.get("role", "")

        # 构建迁移任务数据
        migration_form = {
            "vm_name": vm_name,
            "vm_id": vm_id,  # 添加vm_id用于回调更新数据库
            "source_host": source_host,
            "target_host": target_host,
            "migration_type": migration_type,
            "username": username,
            "role": role,
        }

        # 使用源主机的队列
        queue_name = "queue_" + source_host

        # 分发子任务到对应的主机队列
        job = migrate_vm.s(migration_form).set(queue=queue_name)
        jobs.append(job)

    if jobs:
        # 使用 group 来聚合所有子任务，并指定最终的回调函数
        job_group = group(jobs) | start_vm_migration_callback.s().set(
            queue=QUEUE_NAME)
        result = job_group.apply_async()
        print(f"已分发 {len(jobs)} 个虚拟机迁移任务")
        return f"分发了 {len(jobs)} 个迁移任务"
    else:
        print("没有需要迁移的虚拟机")
        return "没有需要迁移的虚拟机"


@app.task(name="start_vm_migration_callback")
def start_vm_migration_callback(results):
    """
    开始虚拟机迁移回调
    :param results: 迁移任务结果列表
    :return:
    """
    print("start_vm_migration_callback 参数:", results)

    from db.model.hci.compute import Domain, Host

    dbi = get_dbi()
    success_count = 0
    failed_count = 0

    # 处理每个迁移结果
    for result in results:
        if not isinstance(result, dict):
            continue

        vm_name = result.get("vm_name", "")
        vm_id = result.get("vm_id", "")
        target_host = result.get("target_host", "")
        task_status = result.get("task_status", "failed")
        username = result.get("username", "")
        role = result.get("role", "")

        try:
            if task_status == "success":
                # 更新数据库中虚拟机的主机信息
                with dbi.session_scope() as session:
                    # 查找目标主机
                    target_host_obj = session.query(Host).filter(
                        Host.ip == target_host).first()
                    if target_host_obj and vm_id:
                        # 更新虚拟机的主机ID
                        session.query(Domain).filter(Domain.id == vm_id).update({
                            Domain.host_id: target_host_obj.id
                        })
                        session.commit()
                        print(f"已更新虚拟机 {vm_name} 的主机信息到 {target_host}")

                success_count += 1
                new_logger.log(
                    username, "虚拟机", "迁移", "成功", role,
                    f"虚拟机迁移: {vm_name} 到 {target_host}, 成功"
                )
                print(f"虚拟机迁移成功: {vm_name} 到 {target_host}")
            else:
                failed_count += 1
                new_logger.log(
                    username, "虚拟机", "迁移", "失败", role,
                    f"虚拟机迁移: {vm_name} 到 {target_host}, 失败"
                ) 
                print(
                    f"虚拟机迁移失败: {vm_name}, 错误: {result.get('message', '未知错误')}")

        except Exception as e:
            new_logger.log(
                username, "虚拟机", "迁移", "失败", role,
                f"虚拟机迁移: {vm_name} 到 {target_host}, 错误: {str(e)}"
            )
            print(f"处理迁移回调时发生错误: {vm_name}, 错误: {str(e)}")
            failed_count += 1

    result_msg = f"虚拟机迁移任务完成 - 成功: {success_count}, 失败: {failed_count}"
    print(result_msg)

    return {
        "message": result_msg,
        "success_count": success_count,
        "failed_count": failed_count,
        "results": results
    }


@app.task(name="create_vm_snapshot_callback")
def create_vm_snapshot_callback(form):
    """
    创建虚拟机快照回调
    :param form:
    :return:
    """
    print("create_vm_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")

    if task_status == "success":
        new_logger.log(
            username, "虚拟机", "创建快照", "成功", role,
            f"虚拟机: {vm_name} 创建快照: {snapshot_name} 成功"
        )
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "创建快照", "失败", role,
            f"虚拟机: {vm_name} 创建快照: {snapshot_name} 失败, 错误: {error}"
        )
    return form

@app.task(name="delete_vm_snapshot_callback")
def delete_vm_snapshot_callback(form):
    """
    删除虚拟机快照回调
    :param form:
    :return:
    """
    print("delete_vm_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")

    if task_status == "success":
        new_logger.log(
            username, "虚拟机", "删除快照", "成功", role,
            f"虚拟机: {vm_name} 删除快照: {snapshot_name} 成功"
        )
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "删除快照", "失败", role,
            f"虚拟机: {vm_name} 删除快照: {snapshot_name} 失败, 错误: {error}"
        )
    return form

@app.task(name="restore_vm_snapshot_callback")
def restore_vm_snapshot_callback(form):
    """
    还原虚拟机快照回调
    :param form:
    :return:
    """
    print("restore_vm_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")

    if task_status == "success":
        new_logger.log(
            username, "虚拟机", "还原快照", "成功", role,
            f"虚拟机: {vm_name} 还原快照: {snapshot_name} 成功"
        )
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "还原快照", "失败", role,
            f"虚拟机: {vm_name} 还原快照: {snapshot_name} 失败, 错误: {error}"
        )
    return form


@app.task(name="create_vm_external_snapshot_callback")
def create_vm_external_snapshot_callback(form):
    """
    TODO 开发中，禁止使用
    创建虚拟机外部快照回调
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称", 
        "description": "快照描述",
        "host_ip": "主机IP",
        "disks": [磁盘信息列表],
        "task_status": "任务状态",
        "username": "用户名",
        "role": "角色",
        "disk_files": [新创建的磁盘文件信息],
        "total_disks": "总磁盘数",
        "created_disks": "成功创建的磁盘数"
    }
    :return:
    """
    print("create_vm_external_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")
    disk_files = form.get("disk_files", [])
    total_disks = form.get("total_disks", 0)
    created_disks = form.get("created_disks", 0)

    if task_status == "success":
        # 构建磁盘信息摘要
        disk_summary = ""
        if disk_files:
            disk_paths = [d.get("snapshot_path", "") for d in disk_files if d.get("created")]
            if disk_paths:
                disk_summary = f", 创建磁盘文件: {', '.join([path.split('/')[-1] for path in disk_paths[:3]])}"
                if len(disk_paths) > 3:
                    disk_summary += f" 等{len(disk_paths)}个文件"
        
        new_logger.log(
            username, "虚拟机", "创建外部快照", "成功", role,
            f"虚拟机: {vm_name} 创建外部快照: {snapshot_name} 成功, 磁盘数: {created_disks}/{total_disks}{disk_summary}"
        )
        
        # 记录磁盘文件详情
        for disk in disk_files:
            if disk.get("created"):
                print(f"外部快照磁盘创建成功: 设备={disk.get('device')}, 路径={disk.get('snapshot_path')}, 大小={disk.get('size')}字节")
            else:
                print(f"外部快照磁盘创建失败: 设备={disk.get('device')}, 路径={disk.get('snapshot_path')}")
                
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "创建外部快照", "失败", role,
            f"虚拟机: {vm_name} 创建外部快照: {snapshot_name} 失败, 错误: {error}"
        )

    return form


@app.task(name="delete_vm_external_snapshot_callback")
def delete_vm_external_snapshot_callback(form):
    """
    TODO 开发中，禁止使用
    删除虚拟机外部快照回调
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称",
        "host_ip": "主机IP",
        "task_status": "任务状态",
        "username": "用户名",
        "role": "角色",
        "deleted_disk_files": [被删除的磁盘文件信息],
        "total_disks": "总磁盘数",
        "successfully_deleted": "成功删除的磁盘数"
    }
    :return:
    """
    print("delete_vm_external_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")
    deleted_disk_files = form.get("deleted_disk_files", [])
    total_disks = form.get("total_disks", 0)
    successfully_deleted = form.get("successfully_deleted", 0)

    if task_status == "success":
        # 构建磁盘删除信息摘要
        disk_summary = ""
        if deleted_disk_files:
            deleted_paths = [d.get("snapshot_path", "") for d in deleted_disk_files if d.get("deleted")]
            if deleted_paths:
                disk_summary = f", 删除磁盘文件: {', '.join([path.split('/')[-1] for path in deleted_paths[:3]])}"
                if len(deleted_paths) > 3:
                    disk_summary += f" 等{len(deleted_paths)}个文件"
        
        new_logger.log(
            username, "虚拟机", "删除外部快照", "成功", role,
            f"虚拟机: {vm_name} 删除外部快照: {snapshot_name} 成功, 磁盘数: {successfully_deleted}/{total_disks}{disk_summary}"
        )
        
        # 记录磁盘文件删除详情
        for disk in deleted_disk_files:
            if disk.get("deleted"):
                manually_deleted = " (手动删除)" if disk.get("manually_deleted") else ""
                print(f"外部快照磁盘删除成功: 设备={disk.get('device')}, 路径={disk.get('snapshot_path')}{manually_deleted}")
            else:
                print(f"外部快照磁盘删除失败: 设备={disk.get('device')}, 路径={disk.get('snapshot_path')}")
                
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "删除外部快照", "失败", role,
            f"虚拟机: {vm_name} 删除外部快照: {snapshot_name} 失败, 错误: {error}"
        )
   
    return form


@app.task(name="restore_vm_from_external_snapshot_callback")
def restore_vm_from_external_snapshot_callback(form):
    """
    TODO 开发中，禁止使用
    从外部快照恢复虚拟机回调
    :param form: {
        "vm_id": "虚拟机ID",
        "vm_name": "虚拟机名称",
        "snapshot_name": "快照名称",
        "restore_type": "恢复类型",
        "host_ip": "主机IP",
        "disks": [磁盘信息列表],
        "task_status": "任务状态",
        "username": "用户名",
        "role": "角色",
        "was_running": "恢复前是否运行中"
    }
    :return:
    """
    print("restore_vm_from_external_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    restore_type = form.get("restore_type", "full")
    role = form.get("role", "")
    username = form.get("username", "")
    was_running = form.get("was_running", False)

    if task_status == "success":
        # 构建恢复信息摘要
        restore_summary = f"恢复类型: {restore_type}"
        if was_running:
            restore_summary += ", 恢复后已启动"
        
        new_logger.log(
            username, "虚拟机", "从外部快照恢复", "成功", role,
            f"虚拟机: {vm_name} 从外部快照: {snapshot_name} 恢复成功, {restore_summary}"
        )
        
        print(f"外部快照恢复成功: 虚拟机={vm_name}, 快照={snapshot_name}, 类型={restore_type}, 运行状态={'已启动' if was_running else '已关闭'}")
        
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "从外部快照恢复", "失败", role,
            f"虚拟机: {vm_name} 从外部快照: {snapshot_name} 恢复失败, 错误: {error}"
        )

    return form




# ==================== 新版备份管理回调函数 ====================

@app.task(name="backup_create_callback")
def backup_create_callback(form):
    """
    创建虚拟机备份回调函数 - 更新数据库
    :param form: {
        "backup_id": "备份ID",
        "result": "任务执行结果: success/error",
        "message": "执行消息",
        "backup_disks": "备份磁盘信息列表",
        "total_backup_size": "备份总大小",
        "successful_disk_count": "成功备份的磁盘数量",
        "backup_status": "备份状态: completed/failed",
        "vm_name": "虚拟机名称",
        "backup_name": "备份名称"
    }
    :return: 处理结果
    """
    from datetime import datetime
    
    try:
        print(f"=== 备份创建回调开始 ===")
        print(f"回调参数: {form}")
        
        backup_id = form.get('backup_id')
        result = form.get('result', 'error')
        backup_disks = form.get('backup_disks', [])
        total_backup_size = form.get('total_backup_size', 0)
        successful_disk_count = form.get('successful_disk_count', 0)
        backup_status = form.get('backup_status', 'failed')
        
        if not backup_id:
            print("错误：缺少备份ID")
            return {"result": "error", "message": "缺少备份ID"}
        
        dbi = get_dbi()
        with dbi.session_scope() as session:
            # 1. 更新备份记录状态
            backup_record = session.query(DomainBackup).filter(
                DomainBackup.id == backup_id
            ).first()
            
            if not backup_record:
                print(f"错误：找不到备份记录 {backup_id}")
                return {"result": "error", "message": f"找不到备份记录 {backup_id}"}
            
            if result == 'success':
                # 更新备份状态和结果信息
                backup_record.backup_status = backup_status
                backup_record.backup_end_time = datetime.now()
                backup_record.total_size = total_backup_size
                backup_record.disk_count = successful_disk_count
                
                # 保存虚拟机XML配置到backup_xml字段
                vm_xml = form.get('vm_xml')
                if vm_xml:
                    backup_record.backup_xml = vm_xml
                    print(f"保存虚拟机XML配置到备份记录")
                
                backup_record.error_message = None
                backup_record.backup_metadata = form.get("backup_metadata", {})
            
                print(f"更新备份记录状态: {backup_status}")
                
                # 2. 为成功备份的磁盘创建存储卷记录和关联关系
                if backup_disks:
                    for disk_info in backup_disks:
                        if not disk_info.get('copied', False):
                            continue  # 跳过备份失败的磁盘
                        
                        # 获取任务返回的新卷信息
                        new_volume_info = disk_info.get('new_volume_info')
                        if not new_volume_info:
                            print(f"警告：磁盘 {disk_info.get('device')} 缺少新卷信息")
                            continue
                        
                        try:
                            # 获取原始存储卷信息
                            original_volume_id = disk_info.get('volume_id')
                            if not original_volume_id:
                                print(f"警告：磁盘 {disk_info.get('device')} 缺少原始卷ID")
                                continue
                            
                            original_volume = session.query(StorageVolume).filter(
                                StorageVolume.id == original_volume_id
                            ).first()
                            
                            if not original_volume:
                                print(f"警告：找不到原始存储卷 {original_volume_id}")
                                continue
                            
                            # 使用任务返回的新卷信息创建备份存储卷记录
                            backup_volume = StorageVolume(
                                name=new_volume_info['name'],
                                storage_pool_id=original_volume.storage_pool_id,
                                protocol_type=original_volume.protocol_type,
                                volume_type=new_volume_info['volume_type'],
                                path=new_volume_info['path'],
                                capacity=new_volume_info['capacity'],
                                allocation=new_volume_info['allocation'],
                                use_type=new_volume_info['use_type'],
                                status=1,  # 可用状态
                                remark=new_volume_info['remark']
                            )
                            
                            session.add(backup_volume)
                            session.flush()  # 获取新创建的卷ID
                            
                            print(f"创建备份存储卷: {new_volume_info['name']} -> {new_volume_info['path']}")
                            
                            # 创建备份和存储卷的关联关系
                            volume_mapping = BackupVolumeMapping(
                                vm_backup_id=backup_id,
                                storage_volume_id=backup_volume.id
                            )
                            
                            session.add(volume_mapping)
                            print(f"创建备份卷关联关系: {backup_id} -> {backup_volume.id}")
                            
                            # 更新磁盘信息，记录新创建的备份卷ID
                            disk_info['backup_volume_id'] = str(backup_volume.id)
                            
                        except Exception as e:
                            print(f"处理磁盘 {disk_info.get('device')} 时出错: {str(e)}")
                            continue
                
                # 更新备份元数据，包含新创建的卷信息
                backup_record.backup_metadata = form.get("backup_metadata", {})
            
            else:
                # 更新备份状态和结果信息
                backup_record.backup_status = backup_status
                backup_record.backup_end_time = datetime.now()
                backup_record.error_message = form.get('message', '备份失败')
                backup_record.backup_metadata = {
                    'error_details': form.get('message'),
                    'failed_disks': [disk for disk in backup_disks if not disk.get('copied', False)]
                }
                print(f"备份失败，记录错误信息")
            
            # 3. 记录操作日志
            try:
                created_by = form.get('created_by', 'system')
                vm_name = form.get('vm_name', 'unknown')
                backup_name = form.get('backup_name', 'unknown')
                
                if result == 'success':
                    log_message = f"虚拟机 {vm_name} 备份 {backup_name} 创建成功，共备份 {successful_disk_count} 个磁盘，总大小 {total_backup_size} 字节"
                    new_logger.log(created_by, "虚拟机", "创建备份", "成功", vm_name, log_message)
                else:
                    log_message = f"虚拟机 {vm_name} 备份 {backup_name} 创建失败: {form.get('message', '未知错误')}"
                    new_logger.log(created_by, "虚拟机", "创建备份", "失败", vm_name, log_message)
                    
            except Exception as e:
                print(f"记录操作日志失败: {str(e)}")
            
            session.commit()
            print(f"=== 备份创建回调完成 ===")
            
            return {
                "result": "success",
                "message": "备份回调处理完成",
                "backup_id": backup_id,
                "backup_status": backup_status,
                "created_volumes": successful_disk_count
            }
        
    except Exception as e:
        error_msg = str(e)
        print(f"备份创建回调异常: {error_msg}")
        import traceback
        traceback.print_exc()
        
        # 尝试更新备份状态为失败
        try:
            dbi = get_dbi()
            with dbi.session_scope() as session:
                backup_record = session.query(DomainBackup).filter(
                    DomainBackup.id == backup_id
                ).first()
                if backup_record:
                    backup_record.backup_status = 'failed'
                    backup_record.error_message = f"回调处理失败: {str(e)}"
                    backup_record.backup_end_time = datetime.now()
                    session.commit()
        except Exception as inner_e:
            print(f"更新备份状态失败: {str(inner_e)}")
        
        return {
            "result": "error",
            "message": f"备份回调处理失败: {str(e)}",
            "backup_id": backup_id
        }


@app.task(name="distach_op_backup_del")
def distach_op_backup_del(form):
    """
    删除备份任务分发
    :param form: 备份列表，每个备份包含完整信息
    :return:
    """
    print(f"=== 备份删除任务分发开始 ===")
    print(f"待删除备份数量: {len(form)}")
    
    jobs = []
    for backup_data in form:
        print(f"处理备份: {backup_data.get('backup_name', 'unknown')}")
        
        try:
            # 获取备份关联的虚拟机ID
            domain_id = backup_data.get('domain_id')
            if not domain_id:
                print(f"警告: 备份 {backup_data.get('backup_name')} 没有关联虚拟机ID")
                continue
            
            # 查询虚拟机信息获取主机IP
            dbi = get_dbi()
            with dbi.session_scope() as session:
                # 查询虚拟机及其主机信息
                domain = session.query(Domain).options(
                    joinedload(Domain.host)
                ).filter(Domain.id == domain_id).first()
                
                if not domain or not domain.host:
                    print(f"警告: 备份 {backup_data.get('backup_name')} 关联的虚拟机或主机不存在")
                    continue
                
                host_ip = domain.host.ip
                queue_name = f"queue_{host_ip}"
                print(f"备份 {backup_data.get('backup_name')} 分发到队列: {queue_name}")
                
                # 查询备份关联的存储卷信息
                backup_id = backup_data.get('id')
                volume_mappings = session.query(BackupVolumeMapping).filter(
                    BackupVolumeMapping.vm_backup_id == backup_id
                ).all()
                
                backup_files = []
                for mapping in volume_mappings:
                    volume = session.query(StorageVolume).filter(
                        StorageVolume.id == mapping.storage_volume_id
                    ).first()
                    
                    if volume and volume.path:
                        backup_files.append({
                            "path": volume.path,
                            "volume_name": volume.name,
                            "volume_id": str(volume.id)
                        })
                
                print(f"备份 {backup_data.get('backup_name')} 包含 {len(backup_files)} 个文件")
                
                # 构建传递给删除任务的数据
                task_data = {
                    "backup_id": backup_id,
                    "backup_name": backup_data.get('backup_name'),
                    "vm_name": domain.name,
                    "backup_files": backup_files,
                    "force_delete": backup_data.get('force_delete', False),
                    "host_ip": host_ip,
                    "created_by": backup_data.get('username', 'system'),
                    "created_by_role": backup_data.get('role', 'system')
                }
                
                # 分发子任务到对应主机

                job = backup_delete.s(task_data).set(queue=queue_name)
                jobs.append(job)
                
        except Exception as e:
            print(f"处理备份 {backup_data.get('backup_name')} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            continue
    
    if not jobs:
        print("警告: 没有生成任何删除任务")
        return

    job_group = group(jobs) | backup_delete_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()
    
    print(f"=== 备份删除任务分发完成，共分发 {len(jobs)} 个任务 ===")


@app.task(name="backup_delete_callback")
def backup_delete_callback(results):
    """
    删除虚拟机备份回调函数 - 更新数据库
    :param results: group任务的结果列表，每个结果包含：
    {
        "result": "任务执行结果: success/error/partial",
        "backup_id": "备份ID",
        "backup_name": "备份名称",
        "vm_name": "虚拟机名称",
        "deleted_files": "已删除的文件信息",
        "failed_files": "删除失败的文件信息",
        "deleted_dirs": "已删除的目录信息",
        "force_delete": "是否强制删除",
        "created_by": "操作者",
        "vm_name": "虚拟机名称",
        "backup_name": "备份名称"
    }
    :return: 处理结果
    """
    from datetime import datetime
    
    try:
        print(f"=== 备份删除回调开始 ===")
        print(f"回调结果数量: {len(results)}")
        
        if not results:
            print("警告：没有收到任何删除结果")
            return {"result": "error", "message": "没有收到任何删除结果"}
        
        dbi = get_dbi()
        processed_count = 0
        error_count = 0
        
        with dbi.session_scope() as session:
            # 处理每个备份的删除结果
            for backup_result in results:
                try:
                    backup_id = backup_result.get('backup_id')
                    result = backup_result.get('result', 'error')
                    backup_name = backup_result.get('backup_name', '')
                    vm_name = backup_result.get('vm_name', '')
                    deleted_files = backup_result.get('deleted_files', [])
                    failed_files = backup_result.get('failed_files', [])
                    deleted_dirs = backup_result.get('deleted_dirs', [])
                    force_delete = backup_result.get('force_delete', False)
                    message = backup_result.get('message', '')
                    created_by = backup_result.get('created_by', 'system')
                    
                    if not backup_id:
                        print("错误：缺少备份ID")
                        error_count += 1
                        continue
                    
                    print(f"处理备份删除结果: {backup_name} (ID: {backup_id}), 结果: {result}")
                    
                    # 查询备份记录
                    backup_record = session.query(DomainBackup).filter(
                        DomainBackup.id == backup_id
                    ).first()
                    
                    if not backup_record:
                        print(f"错误：找不到备份记录 {backup_id}")
                        error_count += 1
                        continue
                    
                    if result == 'success':
                        # 删除成功，删除相关数据库记录
                        print(f"备份 {backup_name} 删除成功，开始清理数据库记录")
                        
                        # 1. 查询并删除备份与存储卷的关联关系
                        volume_mappings = session.query(BackupVolumeMapping).filter(
                            BackupVolumeMapping.vm_backup_id == backup_id
                        ).all()
                        
                        deleted_volume_count = 0
                        for mapping in volume_mappings:
                            try:
                                # 获取关联的存储卷
                                backup_volume = session.query(StorageVolume).filter(
                                    StorageVolume.id == mapping.storage_volume_id
                                ).first()
                                
                                if backup_volume:
                                    # 删除存储卷记录
                                    session.delete(backup_volume)
                                    print(f"删除备份存储卷记录: {backup_volume.name}")
                                
                                # 删除关联关系记录
                                session.delete(mapping)
                                deleted_volume_count += 1
                                
                            except Exception as e:
                                print(f"删除存储卷关联 {mapping.storage_volume_id} 时出错: {str(e)}")
                                continue
                        
                        print(f"删除了 {deleted_volume_count} 个存储卷及其关联关系")
                        
                        # 2. 删除备份记录
                        session.delete(backup_record)
                        print(f"删除备份记录: {backup_name}")
                        
                        # 3. 记录操作日志
                        try:
                            log_message = f"虚拟机 {vm_name} 备份 {backup_name} 删除成功，删除了 {len(deleted_files)} 个文件，清理了 {deleted_volume_count} 个存储卷"
                            new_logger.log(created_by, "虚拟机", "删除备份", "成功", vm_name, log_message)
                        except Exception as e:
                            print(f"记录操作日志失败: {str(e)}")
                        
                    elif result == 'partial':
                        # 部分成功，更新备份状态但保留记录
                        backup_record.backup_status = 'failed'
                        backup_record.updated_at = datetime.now()
                        backup_record.error_message = f"部分文件删除失败: {len(failed_files)} 个文件失败"
                        
                        # 更新元数据，记录部分删除信息
                        current_metadata = backup_record.backup_metadata or {}
                        current_metadata['delete_error'] = {
                            'error_at': datetime.now().isoformat(),
                            'error_message': f"部分文件删除失败",
                            'deleted_files': deleted_files,
                            'failed_files': failed_files,
                            'deleted_file_count': len(deleted_files),
                            'failed_file_count': len(failed_files)
                        }
                        backup_record.backup_metadata = current_metadata
                        
                        print(f"备份 {backup_name} 部分删除失败，保留备份记录")
                        
                        # 记录操作日志
                        try:
                            log_message = f"虚拟机 {vm_name} 备份 {backup_name} 部分删除失败: 成功 {len(deleted_files)} 个文件，失败 {len(failed_files)} 个文件"
                            new_logger.log(created_by, "虚拟机", "删除备份", "部分成功", vm_name, log_message)
                        except Exception as e:
                            print(f"记录操作日志失败: {str(e)}")
                        
                    else:
                        # 删除失败，更新状态和错误信息
                        backup_record.backup_status = 'failed'
                        backup_record.updated_at = datetime.now()
                        backup_record.error_message = message or '删除失败'
                        
                        # 更新元数据，记录删除失败信息
                        current_metadata = backup_record.backup_metadata or {}
                        current_metadata['delete_error'] = {
                            'error_at': datetime.now().isoformat(),
                            'error_message': message or '删除失败'
                        }
                        backup_record.backup_metadata = current_metadata
                        
                        print(f"备份 {backup_name} 删除失败，保留备份记录")
                        
                        # 记录操作日志
                        try:
                            log_message = f"虚拟机 {vm_name} 备份 {backup_name} 删除失败: {message or '未知错误'}"
                            new_logger.log(created_by, "虚拟机", "删除备份", "失败", vm_name, log_message)
                        except Exception as e:
                            print(f"记录操作日志失败: {str(e)}")
                    
                    processed_count += 1
                    
                except Exception as e:
                    print(f"处理备份删除结果时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    error_count += 1
                    continue
            
            session.commit()
            print(f"=== 备份删除回调完成 ===")
            print(f"成功处理: {processed_count}, 错误: {error_count}")
            
            return {
                "result": "success" if error_count == 0 else "partial",
                "processed_count": processed_count,
                "error_count": error_count,
                "total_count": len(results)
            }
            
    except Exception as e:
        print(f"备份删除回调异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"result": "error", "message": str(e)}


@app.task(name="backup_restore_callback")
def backup_restore_callback(form):
    """
    从备份恢复虚拟机回调函数 - 更新数据库
    :param form: {
        "backup_id": "备份ID",
        "vm_id": "目标虚拟机ID",
        "restore_type": "恢复类型",
        "result": "任务执行结果",
        "message": "执行消息",
        "restored_files": "恢复的文件信息",
        "new_vm_id": "新虚拟机ID（如果创建了新VM）",
        "created_by": "操作者",
        "created_by_role": "操作者角色"
    }
    :return: 处理结果
    """
    try:
        # TODO: 实现备份恢复回调逻辑
        # 1. 记录恢复操作到vm_backup表
        # 2. 更新相关虚拟机状态
        # 3. 创建新的存储卷记录（如果需要）
        # 4. 记录操作日志
        pass
        
    except Exception as e:
        print(f"备份恢复回调异常: {str(e)}")
        
    return form

@app.task(name="backup_restore_callback")
def backup_restore_callback(form):
    """
    备份还原回调函数
    处理备份还原任务的结果，更新数据库状态和记录日志
    
    参数格式:
    {
        "result": "success/failed",
        "new_vm_id": "新虚拟机ID",
        "new_vm_name": "新虚拟机名称",
        "backup_id": "备份ID",
        "backup_name": "备份名称",
        "restored_volumes": [...],  # 还原的卷信息
        "vm_started": true/false,   # 虚拟机是否启动成功
        "restore_statistics": {...}, # 还原统计信息
        "restore_metadata": {...},   # 还原元数据
        "error": "错误信息（失败时）"
    }
    """
    import datetime
    
    result = form.get('result', 'unknown')
    new_vm_id = form.get('new_vm_id')
    new_vm_name = form.get('new_vm_name')
    backup_id = form.get('backup_id')
    backup_name = form.get('backup_name', '')
    restored_volumes = form.get('restored_volumes', [])
    vm_started = form.get('vm_started', False)
    restore_statistics = form.get('restore_statistics', {})
    restore_metadata = form.get('restore_metadata', {})
    error_msg = form.get('error', '')
    
    try:
        dbi = get_dbi()
        with dbi.session_scope() as session:
            print(f"处理备份还原回调: {new_vm_name} (结果: {result})")
            
            # 1. 查询新创建的虚拟机记录
            new_domain = session.query(Domain).filter(Domain.id == new_vm_id).first()
            if not new_domain:
                print(f"警告: 找不到新虚拟机记录: {new_vm_id}")
                return {"status": "warning", "message": "虚拟机记录不存在"}
            
            # 2. 根据还原结果更新虚拟机状态
            if result == 'success':
                # 还原成功
                if vm_started:
                    new_domain.status = 'running'
                    status_msg = "还原成功并启动"
                else:
                    new_domain.status = 'stopped'
                    status_msg = "还原成功但未启动"
                
                # 更新虚拟机元数据
                if restore_metadata:
                    new_domain.remark = f"从备份 {backup_name} 还原成功"
                
                # 保存虚拟机XML配置到DomainXml表
                vm_xml = form.get('vm_xml')
                if vm_xml:
                    try:
                        # 检查是否已存在该虚拟机的XML记录
                        existing_xml = session.query(DomainXml).filter(
                            DomainXml.domain_id == new_vm_id,
                            DomainXml.is_active == True
                        ).first()
                        
                        if existing_xml:
                            # 如果已存在，将旧记录设为非活跃状态
                            existing_xml.is_active = False
                            existing_xml.updated_at = datetime.datetime.now()
                        
                        # 创建新的XML记录
                        domain_xml = DomainXml(
                            domain_id=new_vm_id,
                            xml_content=vm_xml,
                            xml_type='domain',
                            version=1,
                            is_active=True,
                            description=f"从备份 {backup_name} 还原时生成的XML配置",
                            created_by=restore_metadata.get('operator_info', {}).get('username', 'system'),
                            created_at=datetime.datetime.now(),
                            updated_at=datetime.datetime.now()
                        )
                        session.add(domain_xml)
                        
                        print(f"已保存虚拟机 {new_vm_name} 的XML配置到DomainXml表")
                        
                    except Exception as xml_error:
                        print(f"保存虚拟机XML配置失败: {str(xml_error)}")
                        # 不抛出异常，避免影响主流程
                
                # 记录成功日志
                operator_info = restore_metadata.get('operator_info', {})
                username = operator_info.get('username', 'system')
                role = operator_info.get('role', 'operator')
                
                total_size_mb = restore_statistics.get('total_restore_size', 0) / (1024 * 1024)
                duration = restore_statistics.get('duration_seconds', 0)
                
                success_log = (
                    f"用户 {username}({role}) 成功从备份 {backup_name} 还原虚拟机 {new_vm_name}。"
                    f"还原 {restore_statistics.get('total_volumes', 0)} 个卷，"
                    f"总大小 {total_size_mb:.2f}MB，耗时 {duration:.2f}秒。"
                    f"虚拟机状态: {status_msg}"
                )
                print(success_log)
                
            else:
                # 还原失败
                new_domain.status = 'failed'
                new_domain.remark = f"从备份 {backup_name} 还原失败: {error_msg}"
                
                # 记录失败日志
                operator_info = restore_metadata.get('operator_info', {})
                username = operator_info.get('username', 'system')
                role = operator_info.get('role', 'operator')
                
                failure_log = (
                    f"用户 {username}({role}) 从备份 {backup_name} 还原虚拟机 {new_vm_name} 失败。"
                    f"错误信息: {error_msg}"
                )
                print(failure_log)
                
                # 清理失败的数据库记录
                try:
                    # 删除虚拟机磁盘关联记录
                    domain_disks = session.query(DomainDisk).filter(
                        DomainDisk.domain_id == new_vm_id
                    ).all()
                    for disk in domain_disks:
                        session.delete(disk)
                    
                    # 删除虚拟机网络接口记录
                    domain_interfaces = session.query(DomainInterface).filter(
                        DomainInterface.domain_id == new_vm_id
                    ).all()
                    for interface in domain_interfaces:
                        session.delete(interface)
                    
                    # 删除新创建的存储卷记录
                    for volume_info in restored_volumes:
                        volume_id = volume_info.get('new_volume_id')
                        if volume_id:
                            volume = session.query(StorageVolume).filter(
                                StorageVolume.id == volume_id
                            ).first()
                            if volume:
                                session.delete(volume)
                    
                    # 删除虚拟机记录
                    session.delete(new_domain)
                    
                    print(f"已清理失败的虚拟机相关记录: {new_vm_name}")
                    
                except Exception as cleanup_error:
                    print(f"清理失败记录时出错: {str(cleanup_error)}")
                    # 不抛出异常，避免影响主流程
            
            # 3. 更新时间戳
            new_domain.updated_at = datetime.datetime.now()
            
            # 4. 提交数据库更改
            session.commit()
            
            # 5. 记录操作到备份表（可选）
            try:
                # 可以在这里记录还原操作到备份历史表
                # 例如创建一个还原记录表来跟踪所有还原操作
                pass
            except Exception as log_error:
                print(f"记录还原历史失败: {str(log_error)}")
            
            print(f"备份还原回调处理完成: {new_vm_name}")
            
            return {
                "status": "success",
                "message": f"虚拟机 {new_vm_name} 还原回调处理完成",
                "vm_status": new_domain.status,
                "result": result
            }
            
    except Exception as e:
        error_msg = str(e)
        print(f"备份还原回调处理异常: {error_msg}")
        import traceback
        traceback.print_exc()
        
        # 记录回调处理失败的日志
        failure_log = (
            f"备份还原回调处理失败: 虚拟机 {new_vm_name}，"
            f"备份 {backup_name}，错误: {error_msg}"
        )
        print(failure_log)
        
        return {
            "status": "error",
            "message": f"还原回调处理失败: {error_msg}",
            "vm_name": new_vm_name,
            "backup_name": backup_name
        }
