from app.celery import app
from app.tasks.vm_tasks import VmCreateTask
from sqlalchemy.orm import joinedload
from utils.db import get_dbi
from db.model.hci.storage import StoragePool, StorageVolume
from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)

@app.task(base=VmCreateTask, name="create_cdrom")
def create_cdrom(domain_id, resources):
    print(f"Creating VM cdrom: {domain_id} with resources: {resources}")

    
    
    new_cdroms = []
    cdroms = resources.get("cdrom", [])
    
    with  get_dbi().session_scope() as session:
        for cdrom in cdroms:
            volume_id = cdrom.get("volume_id")
            volume = session.query(StorageVolume).filter(StorageVolume.id==volume_id).options(joinedload(StorageVolume.pool)).first()
            source_path = "%s/%s" % (volume.path, volume.name)
            
            new_cdrom = {
                "source": source_path,

            }
            new_cdroms.append(new_cdrom)
            #print()
        
    return {"status": "success", "form": resources, "cdrom": new_cdroms}


@app.task(name="add_tasks")
def add_tasks(x, y):
    queue_name = add_tasks.request.delivery_info.get('routing_key', 'unknown_queue')
    print(f"Task running in queue: {queue_name}")
    print(f"Adding {x} and {y}")
    return x + y

@app.task(name="callback_task")
def callback_task(result):
    queue_name = callback_task.request.delivery_info.get('routing_key', 'unknown_queue')
    print(f"Callback task running in queue: {queue_name}")
    print(f"Callback received result: {result}")



@app.task(name="task_1")
def task_1():
    print("Running Task 1")
    return "Result from Task 1"

@app.task(name="task_2")
def task_2(result):
    print("Running Task 2")
    return f"{result} and task2"

@app.task(name="task_3")
def task_3(results):
    print(f"Running Task 3 with results: {results}")
    return "Final result from Task 3"