from nameko.standalone.rpc import ClusterRpcProxy
import pika
import json
import uuid
import threading
from functools import partial

from app.celery import app
from celery import Task
from api.libvirt.client import Client
from utils.db import get_dbi
from db.model.hci.storage import StoragePool
from config.settings import MSG_AMQP_URI
import json

import logging
from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)
logger = logging.getLogger(__name__)

class RabbitMQPublisher:
    """RabbitMQ消息发布者，用于发送消息到Go服务并处理回调"""
    
    def __init__(self, amqp_url, exchange='', exchange_type='direct'):
        self.amqp_url = amqp_url
        self.exchange = exchange
        self.exchange_type = exchange_type
        self.connection = None
        self.channel = None
        self.callback_queue = None
        self.response = None
        self.corr_id = None
        self.callbacks = {}
        self.is_connected = False
        self.setup_connection()
        
    def setup_connection(self):
        """建立RabbitMQ连接"""
        try:
            parameters = pika.URLParameters(self.amqp_url)
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # 声明交换机
            if self.exchange:
                self.channel.exchange_declare(
                    exchange=self.exchange,
                    exchange_type=self.exchange_type,
                    durable=True
                )
                
            # 声明回调队列
            result = self.channel.queue_declare(queue='', exclusive=True)
            self.callback_queue = result.method.queue
            
            # 消费回调队列
            self.channel.basic_consume(
                queue=self.callback_queue,
                on_message_callback=self.on_response,
                auto_ack=True
            )
            
            self.is_connected = True
            logger.info("RabbitMQ连接建立成功")
        except Exception as e:
            logger.error(f"RabbitMQ连接失败: {str(e)}")
            self.is_connected = False
    
    def on_response(self, ch, method, props, body):
        """处理回调响应"""
        if props.correlation_id in self.callbacks:
            callback_func = self.callbacks[props.correlation_id]
            try:
                response = json.loads(body)
                callback_func(response)
            except Exception as e:
                logger.error(f"处理回调响应失败: {str(e)}")
            finally:
                # 清理回调记录
                del self.callbacks[props.correlation_id]
    
    def publish(self, routing_key, message, callback=None):
        """发布消息到RabbitMQ"""
        if not self.is_connected:
            self.setup_connection()
            if not self.is_connected:
                logger.error("无法发布消息，RabbitMQ连接未建立")
                return False
        
        try:
            corr_id = str(uuid.uuid4())
            
            # 如果有回调函数，则记录它
            if callback:
                self.callbacks[corr_id] = callback
            
            # 发布消息
            self.channel.basic_publish(
                exchange=self.exchange,
                routing_key=routing_key,
                properties=pika.BasicProperties(
                    reply_to=self.callback_queue,
                    correlation_id=corr_id,
                    content_type='application/json'
                ),
                body=json.dumps(message)
            )
            
            logger.info(f"消息已发布到 {routing_key}: {message}")
            return True
        except Exception as e:
            logger.error(f"发布消息失败: {str(e)}")
            return False
    
    def process_callbacks(self, timeout=1):
        """处理回调，超时时间为1秒"""
        if self.is_connected:
            self.connection.process_data_events(time_limit=timeout)
    
    def close(self):
        """关闭连接"""
        if self.connection and self.connection.is_open:
            self.connection.close()
            self.is_connected = False

# 创建一个全局的RabbitMQ发布者实例
rabbitmq_publisher = RabbitMQPublisher(MSG_AMQP_URI)

@app.task(name="create_scsi_storage_pool")
def create_scsi_storage_pool(form):
    """创建SCSI存储池任务"""
    logger.info(f"开始创建SCSI存储池: {form.get('pool_name')}")
    
    try:
        # 从表单中提取必要参数
        pool_name = form.get('pool_name')
        ip = form.get('ip')
        target = form.get('target')
        lun = form.get('lun')
        mount_path = form.get('mount_path')
        
        # 验证参数
        if not all([pool_name, ip, target, lun, mount_path]):
            logger.error("参数不完整，无法创建存储池")
            return {"status": "error", "message": "参数不完整"}
        
        # 构建消息内容
        message = {
            "pool_name": pool_name,
            "ip": ip,
            "target": target,
            "lun": lun,
            "mount_path": mount_path
        }
        
        # 任务计数器，用于跟踪所有任务是否完成
        task_counter = {
            "total": 1,  # 假设只有一个任务
            "completed": 0,
            "success": 0,
            "failed": 0,
            "results": []
        }
        
        # 定义回调函数
        def on_task_complete(result):
            task_counter["completed"] += 1
            task_counter["results"].append(result)
            
            if result.get("status") == "success":
                task_counter["success"] += 1
            else:
                task_counter["failed"] += 1
                
            logger.info(f"任务完成进度: {task_counter['completed']}/{task_counter['total']}")
            
            # 如果所有任务完成，则更新数据库
            if task_counter["completed"] == task_counter["total"]:
                try:
                    # 更新数据库状态
                    with get_dbi() as db:
                        storage_pool = db.query(StoragePool).filter(StoragePool.name == pool_name).first()
                        if storage_pool:
                            if task_counter["failed"] == 0:
                                storage_pool.status = "active"
                                logger.info(f"存储池 {pool_name} 创建成功")
                            else:
                                storage_pool.status = "error"
                                logger.error(f"存储池 {pool_name} 创建失败")
                            db.commit()
                except Exception as e:
                    logger.error(f"更新数据库状态失败: {str(e)}")
        
        # 发布消息到RabbitMQ
        success = rabbitmq_publisher.publish(
            routing_key="storage.create_scsi_pool",  # 根据实际情况调整路由键
            message=message,
            callback=on_task_complete
        )
        
        if not success:
            return {"status": "error", "message": "发送消息失败"}
            
        # 启动一个线程来处理回调，确保不会阻塞当前进程
        def process_callbacks_thread():
            # 等待足够的时间来接收所有的回调
            timeout = 60  # 60秒超时
            elapsed = 0
            interval = 1  # 每秒检查一次
            
            while elapsed < timeout and task_counter["completed"] < task_counter["total"]:
                rabbitmq_publisher.process_callbacks(interval)
                elapsed += interval
                
            if task_counter["completed"] < task_counter["total"]:
                logger.warning(f"等待回调超时，只完成了 {task_counter['completed']}/{task_counter['total']} 个任务")
        
        callback_thread = threading.Thread(target=process_callbacks_thread)
        callback_thread.daemon = True
        callback_thread.start()
        
        return {"status": "pending", "message": "存储池创建任务已提交"}
        
    except Exception as e:
        logger.error(f"创建存储池时发生错误: {str(e)}")
        return {"status": "error", "message": f"创建存储池失败: {str(e)}"}
