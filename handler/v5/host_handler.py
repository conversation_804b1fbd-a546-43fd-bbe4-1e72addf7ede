# -*- coding: utf-8 -*-

from calendar import c
from unittest import result
from venv import create

import requests
import tornado.ioloop
from sqlalchemy import asc, desc
from sqlalchemy.orm import joinedload

from api.prometheus.client import Client as Pclient
import pyrestful.rest
from db.model.hci.hardware import HardwareInfo

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from settings import QUEUE_NAME
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from db.model.hci.storage import HostStorageDeviceMapping, HostStoragePoolMapping,StorageDevice
from app.tasks.host_tasks import (
    create_host
)
from api.log.log import CustomLogger
from celery import Celery, chord
from util.decorators import role_required, deprecated_api

import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class HostHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    @get(_path="/v5/host/detail/{host_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_host_detail(self, host_id):

        with self.session_scope() as session:
            #查询虚机
            host = session.query(Host).filter(Host.id == host_id).first()
            if host:
                data =  Host.to_dict_merge(host)
                return data

        return {"msg":"没有找到虚机", "code": 400}

    @get(_path="/v5/host/refresh/{host_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_host_refresh_detail(self, host_id):
        with self.session_scope() as session:
            host = session.query(Host).filter(Host.id == host_id).first()
            if host:
                # 通过后端代理请求目标服务
                target_url = f"http://{host.ip}:9178/system/info?ip={host.ip}"
                # target_url = f"http://127.0.0.1:9178/system/info?ip={host.ip}"
                response = requests.get(target_url)  # 需要实现代理逻辑
                return response.json()

    @get(_path="/v5/host/hardware/{host_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_host_hardware(self, host_id):
        with self.session_scope() as session:
            # 查询硬件信息
            hardware_info = session.query(HardwareInfo).filter(HardwareInfo.host_id == host_id).first()
            if not hardware_info:
                return {"msg": "没有找到硬件信息", "code": 400}
            # 将硬件信息转换为字典格式
            hardware_info_dict = hardware_info.to_dict()
            # Initialize empty lists for all hardware components
            hardware_info_dict.update({
                "network_cards": [],
                "disks": [],
                "pci_devices": [],
                "cpu_info": [],
                "memory_info": [],
                "hba_devices": [],
                "gpus": [],
                "usbs": [],
            })

            # Safely handle each hardware component
            if hasattr(hardware_info, 'network_cards') and hardware_info.network_cards:
                hardware_info_dict["network_cards"] = [nc.to_dict() for nc in hardware_info.network_cards]

            if hasattr(hardware_info, 'disks') and hardware_info.disks:
                hardware_info_dict["disks"] = [disk.to_dict() for disk in hardware_info.disks]

            if hasattr(hardware_info, 'usbs') and hardware_info.disks:
                hardware_info_dict["usbs"] = [usb.to_dict() for usb in hardware_info.usbs]

            if hasattr(hardware_info, 'pci_devices') and hardware_info.pci_devices:
                hardware_info_dict["pci_devices"] = [pci.to_dict() for pci in hardware_info.pci_devices]

            if hasattr(hardware_info, 'cpu_infos') and hardware_info.cpu_infos:
                hardware_info_dict["cpu_info"] = [cpu.to_dict() for cpu in hardware_info.cpu_infos]

            if hasattr(hardware_info, 'memory_infos') and hardware_info.memory_infos:
                hardware_info_dict["memory_info"] = [mem.to_dict() for mem in hardware_info.memory_infos]

            if hasattr(hardware_info, 'gpus') and hardware_info.gpus:
                hardware_info_dict["gpus"] = [gpu.to_dict() for gpu in hardware_info.gpus]

            if hasattr(hardware_info, 'hba_devices') and hardware_info.hba_devices:
                hardware_info_dict["hba_devices"] = [hba.to_dict() for hba in hardware_info.hba_devices]
            return {"msg": "查询硬件信息成功", "code": 200, "data": hardware_info_dict}


    @role_required()
    @post(_path="/v5/host/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_list(self, form):
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        type = form.get('type', 'recource')
        _id = form.get('_id', '')

        with self.session_scope() as session:
            # 首先创建基础查询
            query = session.query(Host)

            # 根据不同类型进行过滤
            if type == 'recource':
                # 获取所有相关的域
                query = query.join(Cluster).join(Pool)

            elif type == 'pool':
                query = query.join(Cluster).join(Pool).filter(Pool.id == _id)

            elif type == 'cluster':
                query = query.join(Cluster).filter(Cluster.id == _id)

            # 添加搜索条件
            if search_str:
                query = query.filter(Host.name.ilike(f"%{search_str}%"))

            # 添加排序
            if hasattr(Host, order_by):
                order_column = getattr(Host, order_by)
                if order_type == 'asc':
                    query = query.order_by(asc(order_column))
                else:
                    query = query.order_by(desc(order_column))

            # 获取总记录数
            total_records = query.count()

            # 执行分页
            hosts = query.offset((page - 1) * per_page).limit(per_page).all()

            # 将结果转换为字典列表
            host_list = [host.to_dict() for host in hosts]

            c = Pclient()
            for host in host_list:
                cpu_count1 = c.query_vector_by_query(f'sum(count(node_cpu_seconds_total{{instance=~"{host["ip"]}[^/]+$", job ="node_exporter",mode ="idle"}}) by (instance))')
                if cpu_count1:
                    cpu_count = cpu_count1[0]["value"][1]
                    host["cpu"] = cpu_count
                mem_count1 = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{instance=~"{host["ip"]}[^/]+$", job ="node_exporter"}})')
                if mem_count1:
                    mem_count = mem_count1[0]["value"][1]
                    host["mem"] = mem_count

            new_logger.log(
                self.username, "主机管理", "查询主机", "成功", "role", "{}:{},成功".format("查询主机", "默认")
            )

            return {
                "msg": "获取主机列表成功",
                "code": 200,
                "total": total_records,
                "data": host_list
            }

    # @deprecated_api("该接口已废弃")
    @get(_path="/v5/host/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_get_host_list(self):
        with self.session_scope() as session:
            # 首先创建基础查询
            hosts = session.query(Host).order_by(desc("created_at")).all()
            # 将结果转换为字典列表
            host_list = [host.to_dict() for host in hosts]
            return host_list

    @role_required()
    @post(_path="/v5/host/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_create(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie("username")
        host_ip = form.get("ip", "")
        cluster_id = form.get("cluster_id", "")
        ssh_user = form.get("ssh_user", "")
        ssh_password = form.get("ssh_password", "")
        host_name = form.get("name", "")
        remark = form.get("remark", "")
        node_role = form.get("node_role", "")
        
        try:
            with self.session_scope() as session:
                
                cluster = session.query(Cluster).filter(Cluster.id == cluster_id).first()
                if not cluster:
                    return {"code": 200, "msg": "集群不存在"}
                
                
                host = Host()
                host.ip = host_ip
                host.hostname = host_name
                host.name = host_name
                host.cluster_id = cluster_id
                host.pool_id = cluster.pool_id
                host.ssh_user = ssh_user
                host.ssh_password = ssh_password
                host.remark = remark
                host.is_connected = 1
                host.status = "init"
                host.host_model = "false"
                host.role = node_role
                session.add(host)
                session.commit()

            task_form = {
                "host_id": str(host.id),
                "host_ip": host_ip
            }
            # 使用分发任务执行添加节点任务
            result = create_host.apply_async(
                args=[task_form],
                queue=QUEUE_NAME
            )

        except Exception as e:
            new_logger.log(
                self.username, "主机", f"创建主机", "失败", role,f"创建主机:{host_name}失败")
            traceback.print_exc()
            return {"code": 200, "msg": "创建主机失败"}
        
        new_logger.log(
            self.username, "主机", "创建主机", "成功", role,
            f"创建主机{host_name}成功"
        )
        
        return {"code": 200, "msg": "创建主机成功"}


    @role_required()
    @post(_path="/v5/host/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_delete(self, form):
        name = form.get("name", "")
        role = self.get_cookie('role', "")
        username = self.get_cookie('username')
        
        host_id = form.get("id", "")
        try:
            with self.session_scope() as session:
                host = session.query(Host).filter(Host.id == host_id).first()
                if host.domains:
                    new_logger.log(
                        self.username, "主机", "删除主机", "失败", role, f"删除主机{name}失败"
                    )
                    return {"msg": "删除主机失败，请确认主机下是否存在虚拟机", "code": 200}
                # 检查主机和存储池的绑定关系
                pool_binding = session.query(HostStoragePoolMapping).filter(
                    HostStoragePoolMapping.host_id == host_id
                ).first()
                if pool_binding:
                    new_logger.log(
                        self.username, "主机", "删除主机", "失败", role, f"删除主机{name}失败"
                    )
                    return {"msg": "主机下存在存储资源，无法删除", "code": 200}

                # 查询所有与主机关联的存储设备
                device_mappings = session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.host_id == host_id,
                ).all()
                # 先删除存储设备映射关系
                session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.host_id == host_id
                ).delete(synchronize_session=False)
                
                # 再删除对应的本地存储设备
                session.query(StorageDevice).filter(
                    StorageDevice.device_type == "local",
                    StorageDevice.ip_mgmt == host.ip
                ).delete(synchronize_session=False)


                # 删除 host_storage_device_mapping 中关联的记录
                session.query(HostStorageDeviceMapping).filter(HostStorageDeviceMapping.host_id == host_id).delete()

                # 删除主机
                session.query(Host).filter(Host.id == host_id).delete()
                
        except Exception as e:
            new_logger.log(
                self.username, "主机", "删除主机", "失败", role, f"删除主机{name}失败"
            )
            traceback.print_exc()
            return {"status": "failed", "msg": f"删除主机{name}失败"}
        
        new_logger.log(
            self.username, "主机", "创建主机", "成功", role,
            f"创建主机{name}成功"
        )
             
        return {"msg": f"删除主机{name}成功", "code": 200}
        

    @role_required()
    @post(_path="/v5/host/name/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_update(self, form):
        role = self.get_cookie('role', "")
        host_id = form.get("id", "")
        host_name = form.get("name", "")
        remark = form.get("remark", "")
        try:
            with self.session_scope() as session:
                session.query(Host).filter(Host.id == host_id).update({Host.name: host_name, Host.remark: remark})
                
        except Exception as e:
            new_logger.log(
                self.username, "主机", "更新主机池名", "失败", role,
                "更新主机名: {},失败".format(host_name)
            ) 
            traceback.print_exc()
            return {"msg": "更新资源池失败", "code": 500}
        
        new_logger.log(
            self.username, "主机", "更新主机池名", "成功", role,
            "更新主机池名: {},成功".format(host_name)
        )
            
        
        return {"msg": "更新主机名成功", "code": 200}

    @get(_path="/v5/host/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_host_overview(self, _id):
        role = self.get_cookie('role', "")
        
        with self.session_scope() as session:
            # Initialize response structure
            data = {
                "host": {},
                "vm": {"up": 0, "down": 0, "error": 0, "warning": 0, "total": 0},
                "cpu": {"used": 0, "unused": 0, "total": 0},
                "mem": {"used": 0, "unused": 0, "total": 0},
                "storage": {"used": 0, "unused": 0, "total": 0}
            }

            host = session.query(Host).filter(Host.id == _id).first()
            if host:
                data["host"] = host.to_dict()

            # Query VMs on this host
            vms = session.query(Domain).filter(Domain.host_id == _id).all()
            
            # Calculate VM status counts and allocated resources
            vm_up_count, vm_down_count, vm_error_count = 0, 0, 0
            allocation_cpu_count = 0
            allocation_mem_count = 0
            
            for vm in vms:
                allocation_cpu_count += int(vm.vcpu)
                allocation_mem_count += int(vm.memory)
                
                if vm.status == "running":
                    vm_up_count += 1
                elif vm.status == "offline" and vm.status == "deleting":
                    vm_down_count += 1
                elif vm.status == "error":
                    vm_error_count += 1

            data["vm"]["up"] = vm_up_count
            data["vm"]["down"] = vm_down_count
            data["vm"]["error"] = vm_error_count
            data["vm"]["warning"] = 0  # Set to 0 as requested
            data["vm"]["total"] = vm_up_count + vm_down_count + vm_error_count

            # Query resource usage via Prometheus
            c = Pclient()
            ip_pattern = f"{host.ip}:9100"
            
            # CPU metrics
            all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            if all_cpu_count:
                total_cpu = int(float(all_cpu_count[0]["value"][1]))
                data["cpu"]["total"] = total_cpu
                data["cpu"]["used"] = allocation_cpu_count
                data["cpu"]["unused"] = max(0, total_cpu - allocation_cpu_count)

            # Memory metrics
            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_pattern}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_pattern}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_pattern}"}})')
            if all_mem_count:
                total_mem = int(float(all_mem_count[0]["value"][1]))
                used_mem = int(float(use_mem_count[0]["value"][1])) if use_mem_count else 0
                data["mem"]["total"] = total_mem
                data["mem"]["used"] = used_mem
                data["mem"]["unused"] = max(0, total_mem - used_mem)

            # Storage metrics
            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_pattern}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_pattern}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_pattern}"}})')
            if all_disk_count:
                total_storage = int(float(all_disk_count[0]["value"][1]))
                used_storage = int(float(use_disk_count[0]["value"][1])) if use_disk_count else 0
                data["storage"]["total"] = total_storage
                data["storage"]["used"] = used_storage
                data["storage"]["unused"] = max(0, total_storage - used_storage)

            # Add network card information to host data
            try:
                nics = []
                # Use HTTP request to get network card information
                url = f"http://{host.ip}:9178/nic/list"
                response = requests.post(url, timeout=2)  # 2 second timeout
                
                if response.status_code == 200:
                    res_data = response.json()
                    for nic in res_data:
                        ipv4s = nic.get("ipv4s", []) or []
                        if isinstance(ipv4s, list) and len(ipv4s) > 0:
                            nics.append(nic)
                else:
                    nics = []
                data["host"]["card"] = nics
            except Exception as e:
                print(f"获取主机 {host.ip} 网卡信息失败: {str(e)}")
                data["host"]["card"] = []

        return {"msg": "查询主机概览成功", "code": 200, "data": data}
