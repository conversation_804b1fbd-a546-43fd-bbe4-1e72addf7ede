# -*- coding: utf-8 -*-

from calendar import c
from hmac import new
from math import e
from unittest import result
from venv import create
import tornado.ioloop
from sqlalchemy import asc, desc

import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from db.model.hci.user_resource_quota import (
    UserClusterAssignment, UserHostAssignment, UserVmAssignment, UserStoragePool
)
from db.model.user import User
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom
from api.prometheus.client import Client as Pclient
from api.log.log import CustomLogger
from celery import Celery, chord
from util.decorators import role_required, deprecated_api


import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class PoolHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    @get(_path="/v5/pool/detail/{pool_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_pool_detail(self, pool_id):

        with self.session_scope() as session:
            #查询pool记录
            pool = session.query(Pool).filter(Pool.id == pool_id).first()
            if pool:
                data =  Pool.to_dict_merge(pool)
                return data
            

        return {"msg":"没有找到虚机", "code": 404}

    @role_required()
    @post(_path="/v5/pool/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_create(self, form):


        role = self.get_cookie('username', "")
        pool_name = form.get("name", "")
        
        
        try:
            with self.session_scope() as session:
                pool = Pool()
                pool.name = pool_name
                pool.remark = form.get("remark", "")
                session.add(pool)
                
        except Exception as e:
            new_logger.log(
                self.username, "资源池", "创建资源池", "失败", role,
                "创建资源池: {},失败".format(pool_name)
            ) 
            traceback.print_exc()
            return {"msg": "创建失败", "code": 500}
        
        new_logger.log(
            self.username, "资源池", "创建资源池", "成功", role,
            "创建资源池: {},成功".format(pool_name)
        )
            
        
        return {"msg": "创建完成", "code": 200}



    @role_required()    
    @post(_path="/v5/pool/name/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_update(self, form):
        role = self.get_cookie('role', "")
        pool_id = form.get("id", "")
        pool_name = form.get("name", "")
        re = form.get("remark", "")
        try:
            with self.session_scope() as session:
                session.query(Pool).filter(Pool.id == pool_id).update({Pool.name:pool_name, Pool.remark:re})
                
        except Exception as e:
            new_logger.log(
                self.username, "资源池", "更新资源池", "失败", role,
                "更新资源池: {},失败".format(pool_name)
            ) 
            traceback.print_exc()
            return {"msg": "更新资源池失败", "code": 500}
        
        new_logger.log(
            self.username, "资源池", "更新资源池", "成功", role,
            "更新资源池: {},成功".format(pool_name)
        )
            
        
        return {"msg": "更新资源池成功", "code": 200}
    
    @role_required()
    @post(_path="/v5/pool/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_delete(self, form):
        role = self.get_cookie('role', "")
        pool_id = form.get("id", "")

        try:
            with self.session_scope() as session:
                pool = session.query(Pool).filter(Pool.id == pool_id).first()
                if pool.clusters:
                    new_logger.log(
                        self.username, "资源池", "删除资源池", "失败", role,
                        "删除资源池: {},失败".format(pool_id))
                    return {"msg": "删除池失败，请确认池下是否存在集群", "code": 400}
                session.query(Pool).filter(Pool.id == pool_id).delete()

        except  Exception as e:
            new_logger.log(
                self.username, "资源池", "删除资源池", "失败", role,
                "删除资源池: {},失败".format(pool_id))

            traceback.print_exc()
            return {"msg": "删除资源池失败", "code": 500}
        
        new_logger.log(
            self.username, "资源池", "删除资源池", "成功", role,
            "删除资源池: {},成功".format(pool_id)
        )
        
        return {"msg": "删除资源池成功", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @get(_path="/v5/pool/tree", _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_tree(self):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        # Step 4: 构建 zNodes 树状结构
        zNodes = []
        if role == "supadm" or role == "sysadm":
        
            with self.session_scope() as session:
                # Step 1: 查询所有 Pools
                pools = session.query(Pool).all()

                # Step 2: 查询所有 Clusters
                clusters = session.query(Cluster).all()

                # Step 3: 查询所有 Hosts
                hosts = session.query(Host).all()

                # Step 4: 查询所有虚拟机
                domains = session.query(Domain).filter(Domain.domain_recycle_id == "0").all()


                # 添加 "资源节点"
                zNodes.append({"id": "1", "pid": "0", "name": "资源节点", "type": "ziyuan"})

                # 添加 Pool 节点 ("主机池")
                for pool in pools:
                    zNodes.append({
                        "id": str(pool.id),
                        "pid": "1",
                        "name": pool.name,
                        "type": "pool"
                    })

                    # 添加 Cluster 节点 ("集群")
                    for cluster in [c for c in clusters if c.pool_id == str(pool.id)]:
                        zNodes.append({
                            "id": str(cluster.id),
                            "pid": str(pool.id),
                            "name": cluster.name,
                            "type": "cluster"
                        })

                        # 添加 Host 节点 ("主机")
                        for host in [h for h in hosts if h.cluster_id == str(cluster.id)]:
                            zNodes.append({
                                "id": str(host.id),
                                "pid": str(cluster.id),
                                "ip": host.ip,
                                "name": host.name,
                                "role": host.role,
                                "type": "host"
                            })

                            # 添加 Domain 节点 ("虚拟机")
                            for domain in [d for d in domains if d.host_id == str(host.id)]:
                                zNodes.append({
                                    "id": str(domain.id),
                                    "pid": str(host.id),
                                    "name": domain.name,
                                    "type": "domain"
                                })
        if role == "operator":
            with self.session_scope() as session:
                # 获取当前用户user_id
                username = self.get_cookie("username", "")
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"msg": "用户不存在", "code": 200}
                user_id = str(user.id)
                # 用户权限
                user_cluster_ids = [uc.cluster_id for uc in session.query(UserClusterAssignment).filter_by(user_id=user_id).all()]
                user_host_ids = [uh.host_id for uh in session.query(UserHostAssignment).filter_by(user_id=user_id).all()]
                user_vm_ids = [uv.vm_id for uv in session.query(UserVmAssignment).filter_by(user_id=user_id).all()]

                # Step 1: 查询所有 Pools
                pools = session.query(Pool).all()
                # Step 2: 查询所有 Clusters
                clusters = session.query(Cluster).all()
                # Step 3: 查询所有 Hosts
                hosts = session.query(Host).all()
                # Step 4: 查询所有虚拟机
                domains = session.query(Domain).filter(Domain.domain_recycle_id == "0").all()

                # 权限过滤
                # 1. 先找出用户有权限的所有虚拟机所在的主机ID
                vm_host_ids = set(str(d.host_id) for d in domains if str(d.id) in user_vm_ids)
                
                # 1.1 找出这些主机所在的集群ID
                vm_cluster_ids = set(str(h.cluster_id) for h in hosts if str(h.id) in vm_host_ids)
                
                # 2. 只保留有权限的集群（直接分配的集群 + 分配主机所在的集群 + 分配虚拟机所在主机的集群）
                clusters = [c for c in clusters if str(c.id) in user_cluster_ids 
                          or any(str(c.id) == h.cluster_id for h in hosts if str(h.id) in user_host_ids)
                          or str(c.id) in vm_cluster_ids]
                
                # 3. 只保留有权限的主机（集群下所有主机 + 单独分配的主机 + 有权限虚拟机所在主机，去重）
                cluster_host_ids = [str(h.id) for h in hosts if str(h.cluster_id) in user_cluster_ids]
                all_host_ids = set(cluster_host_ids) | set(user_host_ids) | vm_host_ids
                hosts = [h for h in hosts if str(h.id) in all_host_ids]
                
                # 4. 只保留直接分配给用户的虚拟机
                # 注意:这里不再包含有权限主机下的所有虚拟机
                # 即使虚拟机所在的主机或集群没有权限,只要虚拟机被直接分配给用户就可以访问
                domains = [d for d in domains if str(d.id) in user_vm_ids]

                # 添加 "资源节点"
                zNodes.append({"id": "1", "pid": "0", "name": "资源节点", "type": "ziyuan"})

                # 添加 Pool 节点 ("主机池")
                for pool in pools:
                    zNodes.append({
                        "id": str(pool.id),
                        "pid": "1",
                        "name": pool.name,
                        "type": "pool"
                    })
                    # 添加 Cluster 节点 ("集群")
                    for cluster in [c for c in clusters if c.pool_id == str(pool.id)]:
                        zNodes.append({
                            "id": str(cluster.id),
                            "pid": str(pool.id),
                            "name": cluster.name,
                            "type": "cluster"
                        })
                        # 添加 Host 节点 ("主机")
                        for host in [h for h in hosts if h.cluster_id == str(cluster.id)]:
                            zNodes.append({
                                "id": str(host.id),
                                "pid": str(cluster.id),
                                "ip": host.ip,
                                "name": host.name,
                                "role": host.role,
                                "type": "host"
                            })
                            # 添加 Domain 节点 ("虚拟机")
                            for domain in [d for d in domains if d.host_id == str(host.id)]:
                                zNodes.append({
                                    "id": str(domain.id),
                                    "pid": str(host.id),
                                    "name": domain.name,
                                    "type": "domain"
                                })
            
                
        return {"msg": "查询资源树成功", "code": 200, "data": zNodes}

    @role_required(("sysadm","supadm"))
    @get(_path="/v5/resource/overview", _produces=mediatypes.APPLICATION_JSON)
    def hci_resource_overview(self):
        role = self.get_cookie('role', "")
        data = {}

        with self.session_scope() as session:
            # Step 1: 查询所有 Pools
            pool_count = session.query(Pool).count()
            data["pool_count"] = pool_count

            # Step 2: 查询所有 Clusters
            cluster_count = session.query(Cluster).count()
            data["cluster_count"] = cluster_count

            # Step 3: 查询所有 Hosts
            host_count = session.query(Host).count()
            data["host_count"] = host_count

            # Step 4: 查询所有 Host 下的所有虚拟机数量
            vms = session.query(Domain).all()
            data["vm_count"] = str(len(vms))
            logic_cpu_count = 0
            logic_mem_count = 0
            logic_disk_count = 0
            data["cpu_all_hz"] = ""
            data["cpu_use_hz"] = ""
            for vm in vms:
                logic_cpu_count += int(vm.vcpu)
                logic_mem_count += int(vm.memory)

            # Step 5: 查询所有 物理cpu，逻辑cpu，内存，磁盘
            # data["logic_cpu_count"] = str(logic_cpu_count)
            # data["logic_cpu_rate"] = str(0)
            # data["logic_mem_count"] = str(logic_mem_count)
            # data["logic_disk_count"] = str(logic_disk_count) # 存疑

            c = Pclient()
            # all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            all_cpu_hz = c.query_vector_by_query('sum (node_cpu_scaling_frequency_hertz{job=\'node_exporter\'})')
            use_cpu_hz = c.query_vector_by_query('sum((node_cpu_scaling_frequency_hertz / node_cpu_frequency_max_hertz))')
            if all_cpu_hz:
                data["cpu_all_hz"] = all_cpu_hz[0]["value"][1]
                data["cpu_use_hz"] = use_cpu_hz[0]["value"][1]

            all_mem_count = c.query_vector_by_query('sum(node_memory_MemTotal_bytes{job = "node_exporter"})')
            use_mem_count = c.query_vector_by_query('sum(node_memory_MemTotal_bytes{job = "node_exporter"}) - sum(node_memory_MemAvailable_bytes{job = "node_exporter"})')
            if all_mem_count:
                data["mem_all_count"] = all_mem_count[0]["value"][1]
                data["mem_use_count"] = use_mem_count[0]["value"][1]

            all_disk_count = c.query_vector_by_query('sum(node_filesystem_size_bytes{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/"})')
            use_disk_count = c.query_vector_by_query('sum(node_filesystem_size_bytes{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/"})- sum(node_filesystem_free_bytes{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/"})')
            if all_disk_count:
                data["disk_all_count"] = all_disk_count[0]["value"][1]
                data["disk_use_count"] = use_disk_count[0]["value"][1]

        return {"msg": "查询概览成功", "code": 200, "data": data}

    @role_required(("sysadm","supadm","operator"))
    @get(_path="/v5/pool/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_pool_overview(self, _id):
        role = self.get_cookie('role', "")
        
        with self.session_scope() as session:
            # Initialize response structure
            data = {
                "cluster": {"count": 0},
                "host": {"count": 0, "up": 0, "down": 0},
                "vm": {"count": 0, "up": 0, "down": 0, "error": 0, "warning": 0},
                "cpu": {"used": 0, "unused": 0, "total": 0},
                "mem": {"used": 0, "unused": 0, "total": 0},
                "storage": {"used": 0, "unused": 0, "total": 0}
            }

            # Step 1: Query cluster count
            cluster_count = session.query(Cluster).filter(Cluster.pool_id == _id).count()
            data["cluster"]["count"] = cluster_count

            # Step 2: Query hosts in this pool
            hosts = session.query(Host).filter(Host.pool_id == _id).all()
            data["host"]["count"] = len(hosts)
            
            host_up_count, host_down_count = 0, 0
            for host in hosts:
                if host.is_connected == "1":
                    host_up_count += 1
                elif host.is_connected == "0":
                    host_down_count += 1

            data["host"]["up"] = host_up_count
            data["host"]["down"] = host_down_count

            # Step 3: Query VMs in this pool
            vms = session.query(Domain).filter(Domain.pool_id == _id).all()
            data["vm"]["count"] = len(vms)
            
            vm_up_count, vm_down_count, vm_error_count = 0, 0, 0
            allocation_cpu_count = 0
            allocation_mem_count = 0
            
            for vm in vms:
                allocation_cpu_count += int(vm.vcpu)
                allocation_mem_count += int(vm.memory)
                
                if vm.status == "running":
                    vm_up_count += 1
                elif vm.status == "offline":
                    vm_down_count += 1
                elif vm.status == "error":
                    vm_error_count += 1

            data["vm"]["up"] = vm_up_count
            data["vm"]["down"] = vm_down_count
            data["vm"]["error"] = vm_error_count
            data["vm"]["warning"] = 0  # Set to 0 as requested

            # Step 4: Query resource usage via Prometheus
            c = Pclient()
            ip_pattern_parts = [f"{host.ip}:9100" for host in hosts]
            ip_regex = "|".join(ip_pattern_parts)
            
            # CPU metrics
            all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            if all_cpu_count:
                total_cpu = int(float(all_cpu_count[0]["value"][1]))
                data["cpu"]["total"] = total_cpu
                data["cpu"]["used"] = allocation_cpu_count
                data["cpu"]["unused"] = max(0, total_cpu - allocation_cpu_count)

            # Memory metrics
            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            if all_mem_count:
                total_mem = int(float(all_mem_count[0]["value"][1]))
                used_mem = int(float(use_mem_count[0]["value"][1])) if use_mem_count else 0
                data["mem"]["total"] = total_mem
                data["mem"]["used"] = used_mem
                data["mem"]["unused"] = max(0, total_mem - used_mem)

            # Storage metrics
            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            if all_disk_count:
                total_storage = int(float(all_disk_count[0]["value"][1]))
                used_storage = int(float(use_disk_count[0]["value"][1])) if use_disk_count else 0
                data["storage"]["total"] = total_storage
                data["storage"]["used"] = used_storage
                data["storage"]["unused"] = max(0, total_storage - used_storage)

        return {"msg": "查询池概览成功", "code": 200, "data": data}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/pool/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_list(self, form):
        # 从 form 中获取参数
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')

        with self.session_scope() as session:
            # 获取当前用户
            user = session.query(User).filter(User.username == username).first()
            if not user:
                return {"code": 200, "msg": "用户不存在", "data": []}

            # 获取用户有权限的池ID列表
            user_pool_assignments = session.query(UserStoragePool.pool_id).filter(
                UserStoragePool.user_id == user.id
            ).all()

            # 首先创建基础查询
            query = session.query(Pool)

            # 添加搜索条件
            if search_str:
                if role == "operator":
                    query = query.filter(Pool.name.ilike(f"%{search_str}%")).filter(Pool.id in user_pool_assignments)
                else:
                    query = query.filter(Pool.name.ilike(f"%{search_str}%"))

            # 添加排序
            if hasattr(Pool, order_by):
                order_column = getattr(Pool, order_by)
                if order_type == 'asc':
                    query = query.order_by(asc(order_column))
                else:
                    query = query.order_by(desc(order_column))

            # 获取总记录数
            total_records = query.count()

            # 执行分页
            pools = query.offset((page - 1) * per_page).limit(per_page).all()

        # 将结果转换为字典列表
        pool_list = [pool.to_dict() for pool in pools]

        return {
            "msg": "获取池列表成功",
            "code": 200,
            "total": total_records,
            "data": pool_list
        }
