import tornado.ioloop
from sqlalchemy import func, update
import requests

import pyrestful.rest
from pyrestful import mediatypes
from pyrestful.rest import post, get
import uuid
from datetime import datetime
import decimal

from api.log.log import CustomLogger


from db.model.hci.compute import (
    Domain, DomainDisk, Host, Cluster, Pool
)


from db.model.hci.user_resource_quota import (
    UserHostAssignment, UserClusterAssignment,
    UserQuota, UserStoragePool, UserVmAssignment,
    User
)

from db.model.hci.storage import (
    StoragePool, StorageVolume, HostStoragePoolMapping, HostStorageDeviceMapping, StorageDevice
)

from api.prometheus.client import Client as Pclient
from util.decorators import role_required, deprecated_api

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()


def decimal_to_float(obj):
    if isinstance(obj, list):
        return [decimal_to_float(i) for i in obj]
    elif isinstance(obj, dict):
        return {k: decimal_to_float(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        return float(obj)
    else:
        return obj

class UserResourceQuotaHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers",
                        "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods',
                        'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")
        
    def user_check(self, session, user_id):
        user = session.query(User).filter_by(id=user_id).first()
        if user.role_name != "operator":
            return False, {"msg":"禁止对改用户执行该操作", "code": 200}
        
        return True, {}
    
    @deprecated_api("该接口已禁用")      
    @role_required()
    @post(_path="/v5/user/assign/hosts", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def assign_hosts_to_user(self, form):
        """
        批量分配主机和集群给用户
        请求参数：
        {
            "user_id": "xxx",
            "hosts": ["host_id1", ...],
            "clusters": ["cluster_id1", ...]
        }
        """
        username = self.get_cookie("username")
        role = self.get_cookie("role")
        user_id = form.get("user_id", "")
        hosts = form.get("hosts", [])
        clusters = form.get("clusters", [])
        if not user_id or (not isinstance(hosts, list) and not isinstance(clusters, list)) or (not hosts and not clusters):
            return {"msg": "参数错误，user_id和hosts或clusters必填", "code": 400}

        host_success, host_failed = [], []
        cluster_success, cluster_failed = [], []
        with self.session_scope() as session:
            # 主机分配
            for host_id in hosts:
                exists = session.query(UserHostAssignment).filter_by(user_id=user_id, host_id=host_id).first()
                if exists:
                    host_failed.append(host_id)
                    continue
                try:
                    assignment = UserHostAssignment(id=str(uuid.uuid4()), user_id=user_id, host_id=host_id)
                    session.add(assignment)
                    host_success.append(host_id)
                except Exception:
                    host_failed.append(host_id)
            # 集群分配
            for cluster_id in clusters:
                exists = session.query(UserClusterAssignment).filter_by(user_id=user_id, cluster_id=cluster_id).first()
                if exists:
                    cluster_failed.append(cluster_id)
                    continue
                try:
                    assignment = UserClusterAssignment(id=str(uuid.uuid4()), user_id=user_id, cluster_id=cluster_id)
                    session.add(assignment)
                    cluster_success.append(cluster_id)
                except Exception:
                    cluster_failed.append(cluster_id)
            session.commit()
        return decimal_to_float({
            "msg": "ok",
            "code": 200,
            "host_success": host_success,
            "host_failed": host_failed,
            "cluster_success": cluster_success,
            "cluster_failed": cluster_failed
        })
    
    @deprecated_api("该接口已被禁用")
    @role_required()
    @post(_path="/v5/user/assign/pools", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def assign_pools_to_user(self, form):
        """
        批量分配存储池给用户
        请求参数：
        {
            "user_id": "xxx",
            "pools": ["pool_id1", "pool_id2", ...]
        }
        """
        user_id = form.get("user_id", "")
        pools = form.get("pools", [])
        if not user_id or not isinstance(pools, list) or not pools:
            return {"msg": "参数错误，user_id和pools必填", "code": 400}

        success = []
        failed = []
        with self.session_scope() as session:
            for pool_id in pools:
                # 检查存储池是否被分配
                is_assign = session.query(UserStoragePool).filter_by(pool_id=pool_id).first()
                if is_assign:
                    print(f"存储池{pool_id}已被分配")
                # 检查是否已存在分配关系，避免重复
                exists = session.query(UserStoragePool).filter_by(user_id=user_id, pool_id=pool_id).first()
                if exists:
                    failed.append(pool_id)
                    continue
                try:
                    assignment = UserStoragePool(
                        id=str(uuid.uuid4()),
                        user_id=user_id,
                        pool_id=pool_id
                    )
                    session.add(assignment)
                    success.append(pool_id)
                except Exception as e:
                    failed.append(pool_id)
            session.commit()
        return decimal_to_float({
            "msg": "分配完成",
            "code": 200,
            "success": success,
            "failed": failed
        })
    
    @role_required()
    @post(_path="/v5/user/assign/vms", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def assign_vms_to_user(self, form):
        """
        重新分配虚拟机给用户（虚拟机只能分配给一个用户）
        请求参数：
        {
            "user_id": "xxx",
            "nodes": [{"id": "vm_id1", "name": "xxx"}, ...]
        }
        """
        username = self.get_cookie("username")
        role = self.get_cookie("role")
        user_id = form.get("user_id", "")
        nodes = form.get("nodes", [])
        if not user_id or not isinstance(nodes, list):
            return {"msg": "参数错误，user_id和nodes必填", "code": 400}

        with self.session_scope() as session:
            # 当前已分配
            current_assignments = session.query(UserVmAssignment).filter_by(user_id=user_id).all()
            current_vm_ids = set(str(a.vm_id) for a in current_assignments)
            # 新分配
            new_vm_ids = set(str(vm.get("id")) for vm in nodes if vm.get("id"))
            # 需要取消分配
            to_remove_vm_ids = current_vm_ids - new_vm_ids
            # 需要新分配
            to_add_vm_ids = new_vm_ids - current_vm_ids

            # 取消分配
            removed = []
            remove_failed = []
            if to_remove_vm_ids:
                for vm_id in to_remove_vm_ids:
                    try:
                        session.query(UserVmAssignment).filter(
                            UserVmAssignment.user_id == user_id,
                            UserVmAssignment.vm_id == vm_id
                        ).delete(synchronize_session=False)
                        removed.append(vm_id)
                    except Exception as e:
                        remove_failed.append(vm_id)
                session.flush()

            # 新分配
            success = []
            add_failed = []
            for vm in nodes:
                vm_id = str(vm.get("id"))
                vm_name = vm.get("name", vm_id)
                if vm_id in to_add_vm_ids:
                    # 检查该虚拟机是否已被分配给其他用户
                    exists = session.query(UserVmAssignment).filter(
                        UserVmAssignment.vm_id == vm_id
                    ).first()
                    if exists:
                        add_failed.append(vm_name)
                        continue
                    try:
                        assignment = UserVmAssignment(
                            id=str(uuid.uuid4()),
                            user_id=user_id,
                            vm_id=vm_id
                        )
                        session.add(assignment)
                        success.append(vm_name)
                    except Exception as e:
                        add_failed.append(vm_name)
            session.commit()
        
        # 日志
        if success:
            new_logger.log(
                self.username, "虚机管理", "分配虚拟机", "成功", role,
                "{}: 用户:{},新分配虚拟机:{}".format("分配虚拟机", username, success)
            )
        if add_failed:
            new_logger.log(
                self.username, "虚机管理", "分配虚拟机", "失败", role,
                "{}: 用户:{},新分配虚拟机:{}".format("分配虚拟机", username, add_failed)
            )
        if removed:
            new_logger.log(
                self.username, "虚机管理", "取消分配虚拟机", "成功", role,
                "{}: 用户:{},取消分配虚拟机:{}".format("取消分配虚拟机", username, removed)
            )
        if remove_failed:
            new_logger.log(
                self.username, "虚机管理", "取消分配虚拟机", "失败", role,
                "{}: 用户:{},取消分配虚拟机:{}".format("取消分配虚拟机", username, remove_failed)
                )
            
        return {
            "msg": "ok",
            "data": "分配完成",
            "code": 200,
            # "success": success,
            # "add_failed": add_failed,
            # "removed": removed,
            # "remove_failed": remove_failed
        }
    
    @role_required()
    @post(_path="/v5/user/assign/quota", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def assign_quota_to_user(self, form):
        """
        分配或更新用户配额
        请求参数：
        {
            "user_id": "xxx",
            "cpu_limit": 8,
            "memory_limit": 16384,   # 单位MB
            "storage_limit": 500     # 单位GB
        }
        """
        username = self.get_cookie("username")
        role = self.get_cookie("role")
        user_id = form.get("user_id", "")
        cpu_limit = form.get("cpu_limit")
        memory_limit = form.get("memory_limit")
        storage_limit = form.get("storage_limit")
        if not user_id or cpu_limit is None or memory_limit is None or storage_limit is None:
            return {"msg": "参数错误，user_id、cpu_limit、memory_limit、storage_limit必填", "code": 400}

        try:
            with self.session_scope() as session:
                # 获取系统总资源，确保是整数类型
                cpu_total = int(session.query(func.sum(Host.cpu_cores)).scalar() or 0)
                memory_total = int(session.query(func.sum(Host.memory)).scalar() or 0)
                storage_total = int(session.query(func.sum(StorageDevice.total_capacity)).scalar() or 0)

                # 验证输入必须为正整数
                try:
                    cpu_limit = int(cpu_limit)
                    memory_limit = int(memory_limit)
                    storage_limit = int(storage_limit)
                    if cpu_limit <= 0 or memory_limit <= 0 or storage_limit <= 0:
                        return {"msg": "配额必须为正整数", "code": 400}
                except (ValueError, TypeError):
                    return {"msg": "配额必须为整数", "code": 400}

                # 检查新配额是否超过系统总量
                if cpu_limit > cpu_total:
                    return {"msg": f"CPU配额超过系统总量, 申请配额:{cpu_limit}，系统总量:{cpu_total}", "code": 400}
                if memory_limit > memory_total:
                    return {"msg": f"内存配额超过系统总量，申请配额:{memory_limit}，系统总量:{memory_total}", "code": 400}
                if storage_limit > storage_total:
                    return {"msg": f"存储配额超过系统总量，申请配额:{storage_limit}，系统总量:{storage_total}", "code": 400}

                # 查找用户账号
                user = session.query(User).filter_by(id=user_id).first()
                target_username = user.username if user else user_id
                quota = session.query(UserQuota).filter_by(user_id=user_id).first()
                if quota:
                    # 更新配额
                    old_quota = {
                        "cpu_limit": quota.cpu_limit,
                        "memory_limit": quota.memory_limit,
                        "storage_limit": quota.storage_limit
                    }
                    quota.cpu_limit = cpu_limit
                    quota.memory_limit = memory_limit
                    quota.storage_limit = storage_limit
                    quota.updated_at = func.now()
                    msg = "配额更新成功"
                    session.commit()
                    new_logger.log(
                        self.username, "配额管理", "修改配额", "成功", role,
                        f"修改配额: 用户:{target_username}, 原配额:CPU:{old_quota['cpu_limit']},内存:{old_quota['memory_limit']},存储:{old_quota['storage_limit']} -> 新配额:CPU:{cpu_limit},内存:{memory_limit},存储:{storage_limit}"
                    )
                else:
                    # 新增配额
                    quota = UserQuota(
                        id=str(uuid.uuid4()),
                        user_id=user_id,
                        cpu_limit=cpu_limit,
                        memory_limit=memory_limit,
                        storage_limit=storage_limit,
                        cpu_used=0,
                        memory_used=0,
                        storage_used=0,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(quota)
                    session.commit()
                    msg = "配额分配成功"
                    new_logger.log(
                        self.username, "配额管理", "新增配额", "成功", role,
                        f"新增配额: 用户:{target_username}, CPU:{cpu_limit}, 内存:{memory_limit}, 存储:{storage_limit}"
                    )
        except Exception as e:
            # 失败日志
            with self.session_scope() as session:
                user = session.query(User).filter_by(id=user_id).first()
                target_username = user.username if user else user_id
            new_logger.log(
                self.username, "配额管理", "配额操作", "失败", role,
                f"配额操作: 用户:{target_username}, 错误:{str(e)}"
            )
            raise
        return decimal_to_float({
            "msg": "ok",
            "code": 200,
            "data":"分配操作完成",
            # "quota": {
            #     "user_id": user_id,
            #     "cpu_limit": cpu_limit,
            #     "memory_limit": memory_limit,
            #     "storage_limit": storage_limit
            # }
        })
    
    @role_required()
    @post(_path="/v5/user/assign/clusters", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def assign_clusters_to_user(self, form):
        """
        重新分配集群给用户
        请求参数：
        {
            "user_id": "xxx",
            "nodes": [{"id": "cluster_id1", "name": "xxx"}, ...]
        }
        """
        username = self.get_cookie("username")
        role = self.get_cookie("role")
        user_id = form.get("user_id", "")
        nodes = form.get("nodes", [])
        if not user_id or not isinstance(nodes, list):
            return {"msg": "参数错误，user_id和nodes必填", "code": 400}

        with self.session_scope() as session:
            # 当前已分配
            current_assignments = session.query(UserClusterAssignment).filter_by(user_id=user_id).all()
            current_cluster_ids = set(str(a.cluster_id) for a in current_assignments)
            # 新分配
            new_cluster_ids = set(str(node.get("id")) for node in nodes if node.get("id"))
            # 需要取消分配
            to_remove_cluster_ids = current_cluster_ids - new_cluster_ids
            # 需要新分配
            to_add_cluster_ids = new_cluster_ids - current_cluster_ids

            # 取消分配
            removed = []
            remove_failed = []
            if to_remove_cluster_ids:
                for cluster_id in to_remove_cluster_ids:
                    try:
                        session.query(UserClusterAssignment).filter(
                            UserClusterAssignment.user_id == user_id,
                            UserClusterAssignment.cluster_id == cluster_id
                        ).delete(synchronize_session=False)
                        removed.append(cluster_id)
                    except Exception as e:
                        remove_failed.append(cluster_id)
                session.flush()

            # 新分配
            success = []
            add_failed = []
            for node in nodes:
                cluster_id = str(node.get("id"))
                cluster_name = node.get("name", cluster_id)
                if cluster_id in to_add_cluster_ids:
                    try:
                        assignment = UserClusterAssignment(
                            id=str(uuid.uuid4()),
                            user_id=user_id,
                            cluster_id=cluster_id
                        )
                        session.add(assignment)
                        success.append(cluster_name)
                    except Exception as e:
                        add_failed.append(cluster_name)
            session.commit()

        # 日志
        if success:
            new_logger.log(
                self.username, "集群管理", "分配集群", "成功", role,
                "{}: 用户:{},新分配集群:{}".format("分配集群", username, success)
            )
        if add_failed:
            new_logger.log(
                self.username, "集群管理", "分配集群", "失败", role,
                "{}: 用户:{},新分配集群:{}".format("分配集群", username, add_failed)
            )
        if removed:
            new_logger.log(
                self.username, "集群管理", "取消分配集群", "成功", role,
                "{}: 用户:{},取消分配集群:{}".format("取消分配集群", username, removed)
            )
        if remove_failed:
            new_logger.log(
                self.username, "集群管理", "取消分配集群", "失败", role,
                "{}: 用户:{},取消分配集群:{}".format("取消分配集群", username, remove_failed)
            )

        return {
            "msg": "ok",
            "data": "分配完成",
            "code": 200,
            # "success": success,
            # "add_failed": add_failed,
            # "removed": removed,
            # "remove_failed": remove_failed
        }
    
    @role_required()
    @get(_path="/v5/user/quota/{user_id}", _produces=mediatypes.APPLICATION_JSON)
    def get_user_quota(self, user_id):
        """
        查询用户配额
        路径参数：user_id
        返回：配额详情
        """
        with self.session_scope() as session:
            quota = session.query(UserQuota).filter_by(user_id=user_id).first()
            if not quota:
                return decimal_to_float({
                    "msg": "ok",
                    "code": 200,
                    "data": {
                        "user_id": str(user_id),
                        "cpu_limit": 0,
                        "cpu_used": 0,
                        "memory_limit": 0,
                        "memory_used": 0,
                        "storage_limit": 0,
                        "storage_used": 0
                    }
                })
            return decimal_to_float({
                "msg": "ok",
                "code": 200,
                "data": {
                    "user_id": str(quota.user_id),
                    "cpu_limit": quota.cpu_limit,
                    "cpu_used": quota.cpu_used,
                    "memory_limit": quota.memory_limit,
                    "memory_used": quota.memory_used,
                    "storage_limit": quota.storage_limit,
                    "storage_used": quota.storage_used
                }
            })
            
    @role_required(("operator"))
    @get(_path="/v5/quota/byusername", _produces=mediatypes.APPLICATION_JSON)
    def get_user_quota_by_username(self):
        """
        查询用户配额
        返回：配额详情
        """
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            user_id = user.id
            quota = session.query(UserQuota).filter_by(user_id=user_id).first()
            if not quota:
                return decimal_to_float({
                    "msg": "ok",
                    "code": 200,
                    "data": {
                        "user_id": str(user_id),
                        "cpu_limit": 0,
                        "cpu_used": 0,
                        "memory_limit": 0,
                        "memory_used": 0,
                        "storage_limit": 0,
                        "storage_used": 0
                    }
                })
            return decimal_to_float({
                "msg": "ok",
                "code": 200,
                "data": {
                    "user_id": str(quota.user_id),
                    "cpu_limit": quota.cpu_limit,
                    "cpu_used": quota.cpu_used,
                    "memory_limit": quota.memory_limit,
                    "memory_used": quota.memory_used,
                    "storage_limit": quota.storage_limit,
                    "storage_used": quota.storage_used
                }
            })
    
    @role_required()
    @get(_path="/v5/quota/summary", _produces=mediatypes.APPLICATION_JSON)
    def get_quota_summary(self):
        """
        获取所有主机的CPU、内存总和，以及所有存储池的容量总和
        返回：{
            "cpu_total": int,
            "memory_total": int,   # 单位MB
            "storage_total": int   # 单位GB
        }
        """
        with self.session_scope() as session:
            # 主机CPU、内存总和
            # 使用int()确保返回整数类型
            cpu_total = int(session.query(func.sum(Host.cpu_cores)).scalar() or 0)
            memory_total = int(session.query(func.sum(Host.memory)).scalar() or 0)
            # 存储池容量总和（假设字段为StoragePool.capacity，单位GB）
            storage_total = int(session.query(func.sum(StorageDevice.total_capacity)).scalar() or 0)
        return decimal_to_float({
            "msg": "查询成功",
            "code": 200,
            "data": {
                "cpu_total": int(cpu_total),
                "memory_total": int(memory_total),
                "storage_total": int(storage_total)
            }
            
        })
    

    
    @role_required()
    @post(_path="/v5/quota/vms", _produces=mediatypes.APPLICATION_JSON)
    def hci_post_quota_vms(self, form):
        """
        查询用户可以分配的虚拟机和已分配给该用户的虚拟机 列表
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        # Step 4: 构建 zNodes 树状结构
        zNodes = []
        user_id = form.get("user_id")
        
        with self.session_scope() as session:
            check, result = self.user_check(session, user_id)
            if not check:
                return result

            # Step 1: 查询所有 Pools
            pools = session.query(Pool).all()
            # Step 2: 查询所有 Clusters
            clusters = session.query(Cluster).all()
            # Step 3: 查询所有 Hosts
            hosts = session.query(Host).all()
            # Step 4: 查询所有虚拟机
            domains = session.query(Domain).filter(Domain.domain_recycle_id == "0").all()

            # 查询所有虚拟机分配关系
            all_vm_assignments = session.query(UserVmAssignment).all()
            # 构建 vm_id -> [user_id, ...]
            vmid_to_userids = {}
            for r in all_vm_assignments:
                key = str(r.vm_id)
                vmid_to_userids.setdefault(key, []).append(r.user_id)

            # 查询所有涉及到的用户信息
            all_user_ids = set()
            for user_ids in vmid_to_userids.values():
                all_user_ids.update(user_ids)
            users = session.query(User).filter(User.id.in_(all_user_ids)).all()
            user_dict = {str(u.id): {"user_id": str(u.id), "username": u.username, "name": u.name} for u in users}

            # 查询已分配给该用户的虚拟机ID集合
            assigned_vm_ids = set(str(r.vm_id) for r in all_vm_assignments if r.user_id == user_id)

            zNodes = []
            # 添加 "资源节点"
            zNodes.append({"id": "1", "pid": "0", "name": "资源节点", "type": "ziyuan"})

            # 添加 Pool 节点 ("主机池")
            for pool in pools:
                zNodes.append({
                    "id": str(pool.id),
                    "pid": "1",
                    "name": pool.name,
                    "type": "pool"
                })

                # 添加 Cluster 节点 ("集群")
                for cluster in [c for c in clusters if c.pool_id == str(pool.id)]:
                    cluster_id_str = str(cluster.id)
                    zNodes.append({
                        "id": cluster_id_str,
                        "pid": str(pool.id),
                        "name": cluster.name,
                        "type": "cluster"
                    })

                    # 添加 Host 节点 ("主机")
                    for host in [h for h in hosts if h.cluster_id == cluster_id_str]:
                        zNodes.append({
                            "id": str(host.id),
                            "pid": cluster_id_str,
                            "ip": host.ip,
                            "name": host.name,
                            "type": "host"
                        })

                        # 添加 Domain 节点 ("虚拟机")
                        for domain in [d for d in domains if d.host_id == str(host.id)]:
                            vm_id_str = str(domain.id)
                            assigned_user_ids = vmid_to_userids.get(vm_id_str, [])
                            # 如果虚拟机已经被分配给其他用户，就跳过
                            if assigned_user_ids and user_id not in assigned_user_ids:
                                continue
                            node = {
                                "id": vm_id_str,
                                "pid": str(host.id),
                                "name": domain.name,
                                "type": "domain",
                                "checked": user_id in assigned_user_ids,
                                "user": [user_dict[uid] for uid in assigned_user_ids if uid in user_dict]
                            }
                            zNodes.append(node)
        return {"msg": "ok", "code": 200, "data": zNodes}
    
    
    
    
    @role_required()
    @post(_path="/v5/quota/clusters", _produces=mediatypes.APPLICATION_JSON)
    def hci_post_quota_clusters(self, form):
        """
        查询用户可以分配的虚拟机和已分配给该用户的集群 列表
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        # Step 4: 构建 zNodes 树状结构
        zNodes = []
        user_id = form.get("user_id")
        
        with self.session_scope() as session:
            check, result = self.user_check(session, user_id)
            if not check:
                return result

            # Step 1: 查询所有 Pools
            pools = session.query(Pool).all()

            # Step 2: 查询所有 Clusters
            clusters = session.query(Cluster).all()

            # Step 3: 查询所有 Hosts
            hosts = session.query(Host).all()

            # Step 4: 查询所有虚拟机
            domains = session.query(Domain).filter(Domain.domain_recycle_id == "0").all()

            # 查询已分配给该用户的集群ID集合
            assigned_cluster_ids = set(str(r.cluster_id) for r in session.query(UserClusterAssignment).filter(UserClusterAssignment.user_id == user_id).all())

            # 添加 "资源节点"
            zNodes.append({"id": "1", "pid": "0", "name": "资源节点", "type": "ziyuan"})

            # 添加 Pool 节点 ("主机池")
            for pool in pools:
                zNodes.append({
                    "id": str(pool.id),
                    "pid": "1",
                    "name": pool.name,
                    "type": "pool"
                })

                # 添加 Cluster 节点 ("集群")
                for cluster in [c for c in clusters if c.pool_id == str(pool.id)]:
                    zNodes.append({
                        "id": str(cluster.id),
                        "pid": str(pool.id),
                        "name": cluster.name,
                        "type": "cluster",
                        "checked": str(cluster.id) in assigned_cluster_ids
                    })

            
        return {"msg": "ok", "code": 200, "data": zNodes}
    
    
    
    @role_required()
    @post(_path="/v5/quota/storage/pools", _produces=mediatypes.APPLICATION_JSON)
    def hci_post_quota_storage_pools(self, form):
        """
        查询用户可以分配的存储池列表（仅列表，不返回树状结构）。
        """
        user_id = form.get("user_id")
        if not user_id:
            return {"msg": "参数错误，user_id必填", "code": 400}

        with self.session_scope() as session:
            check, result = self.user_check(session, user_id)
            if not check:
                return result

            all_pools = session.query(StoragePool).all()
            all_assignments = session.query(UserStoragePool).all()
            pool_to_user_ids = {}
            for assignment in all_assignments:
                pool_id_str = str(assignment.pool_id)
                if pool_id_str not in pool_to_user_ids:
                    pool_to_user_ids[pool_id_str] = []
                pool_to_user_ids[pool_id_str].append(str(assignment.user_id))
            all_assigned_user_ids = [uid for uids in pool_to_user_ids.values() for uid in uids]
            users_info = session.query(User).filter(User.id.in_(list(set(all_assigned_user_ids)))).all()
            user_dict = {str(u.id): {"user_id": str(u.id), "username": u.username, "name": u.name} for u in users_info}

            result_pools = []
            for pool in all_pools:
                pool_id_str = str(pool.id)
                assigned_users = pool_to_user_ids.get(pool_id_str, [])
                is_assigned_to_current_user = user_id in assigned_users
                if not assigned_users or is_assigned_to_current_user:
                    pool_data = {
                        "id": pool_id_str,
                        "name": pool.name,
                        "type_code": pool.type_code,
                        "capacity": pool.capacity,
                        "allocation": pool.allocation,
                        "available": pool.available,
                        "status": pool.status,
                        "checked": is_assigned_to_current_user,
                        "users": [user_dict[uid] for uid in assigned_users if uid in user_dict]
                    }
                    result_pools.append(pool_data)

            return {"msg": "ok", "code": 200, "data": result_pools}

        # --- 以下为原树状结构写法，现已注释 ---
        # zNodes = []
        # # 根节点
        # zNodes.append({"id": "1", "pid": "0", "name": "资源节点", "type": "ziyuan", "open": True})
        # # 集群节点
        # for cluster in clusters:
        #     cluster_id_str = str(cluster.id)
        #     zNodes.append({
        #         "id": cluster_id_str,
        #         "pid": "1",
        #         "name": cluster.name,
        #         "type": "cluster"
        #     })
        #     # 主机节点
        #     for host in [h for h in hosts if h.cluster_id == cluster_id_str]:
        #         host_id_str = str(host.id)
        #         zNodes.append({
        #             "id": host_id_str,
        #             "pid": cluster_id_str,
        #             "name": host.name,
        #             "ip": host.ip,
        #             "type": "host"
        #         })
        #         # 存储池节点（只挂载属于该主机的池）
        #         for pool in [p for p in all_pools if host in p.hosts]:
        #             pool_id_str = str(pool.id)
        #             assigned_users = pool_to_user_ids.get(pool_id_str, [])
        #             is_assigned_to_current_user = user_id in assigned_users
        #             # 只返回未分配或分配给当前用户的存储池
        #             if not assigned_users or is_assigned_to_current_user:
        #                 pool_node = {
        #                     "id": pool_id_str,
        #                     "pid": host_id_str,
        #                     "name": pool.name,
        #                     "type": "storage_pool",
        #                     "checked": is_assigned_to_current_user,
        #                     "users": [user_dict[uid] for uid in assigned_users if uid in user_dict]
        #                 }
        #                 zNodes.append(pool_node)
        # return {"msg": "ok", "code": 200, "data": zNodes}
    
    @role_required()
    @post(_path="/v5/user/assign/storage_pools", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def assign_storage_pools_to_user(self, form):
        """
        重新分配存储池给用户（存储池只能分配给一个用户）
        {
            "user_id": "xxx",
            "nodes": [{"id": "pool_id1", "name": "xxx"}, ...]
        }
        # 如需支持多用户分配，将“已被分配给其他用户则不能分配”这段逻辑去掉即可。
        """
        username = self.get_cookie("username")
        role = self.get_cookie("role")
        user_id = form.get("user_id", "")
        nodes = form.get("nodes", [])
        if not user_id or not isinstance(nodes, list):
            return {"msg": "参数错误，user_id和nodes必填", "code": 400}

        with self.session_scope() as session:
            # 当前已分配
            current_assignments = session.query(UserStoragePool).filter_by(user_id=user_id).all()
            current_pool_ids = set(str(a.pool_id) for a in current_assignments)
            # 新分配
            new_pool_ids = set(str(node.get("id")) for node in nodes if node.get("id"))
            # 需要取消分配
            to_remove_pool_ids = current_pool_ids - new_pool_ids
            # 需要新分配
            to_add_pool_ids = new_pool_ids - current_pool_ids

            # 取消分配
            removed = []
            remove_failed = []
            if to_remove_pool_ids:
                for pool_id in to_remove_pool_ids:
                    try:
                        session.query(UserStoragePool).filter(
                            UserStoragePool.user_id == user_id,
                            UserStoragePool.pool_id == pool_id
                        ).delete(synchronize_session=False)
                        removed.append(pool_id)
                    except Exception as e:
                        remove_failed.append(pool_id)
                session.flush()

            # 新分配
            success = []
            add_failed = []
            for node in nodes:
                pool_id = str(node.get("id"))
                pool_name = node.get("name", pool_id)
                if pool_id in to_add_pool_ids:
                    # 检查该存储池是否已被分配给其他用户，注释这段代码后，就可以支持多用户分配了
                    exists = session.query(UserStoragePool).filter(
                        UserStoragePool.pool_id == pool_id
                    ).first()
                    if exists:
                        add_failed.append(pool_name)
                        continue
                    try:
                        assignment = UserStoragePool(
                            id=str(uuid.uuid4()),
                            user_id=user_id,
                            pool_id=pool_id
                        )
                        session.add(assignment)
                        success.append(pool_name)
                    except Exception as e:
                        add_failed.append(pool_name)
            session.commit()

        # 日志
        if success:
            new_logger.log(
                self.username, "存储池管理", "分配存储池", "成功", role,
                "{}: 用户:{},新分配存储池:{}".format("分配存储池", username, success)
            )
        if add_failed:
            new_logger.log(
                self.username, "存储池管理", "分配存储池", "失败", role,
                "{}: 用户:{},新分配存储池:{}".format("分配存储池", username, add_failed)
            )
        if removed:
            new_logger.log(
                self.username, "存储池管理", "取消分配存储池", "成功", role,
                "{}: 用户:{},取消分配存储池:{}".format("取消分配存储池", username, removed)
            )
        if remove_failed:
            new_logger.log(
                self.username, "存储池管理", "取消分配存储池", "失败", role,
                "{}: 用户:{},取消分配存储池:{}".format("取消分配存储池", username, remove_failed)
            )

        return {
            "msg": "ok",
            "data": "分配完成",
            "code": 200,
            # "success": success,
            # "add_failed": add_failed,
            # "removed": removed,
            # "remove_failed": remove_failed
        }
    
    
    
    
    