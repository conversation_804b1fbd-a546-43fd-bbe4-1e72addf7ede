# -*- coding: utf-8 -*-

from calendar import c
from hmac import new
from math import e
from unittest import result
from venv import create
import tornado.ioloop
from sqlalchemy import asc, desc

import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom
from api.prometheus.client import Client as Pclient
from api.log.log import CustomLogger
from celery import Celery, chord


import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class PoolCtlHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    
    @get(_path="/v5/ctl/pool/tree", _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_tree(self):
        role = self.get_cookie('role', "")
        
        with self.session_scope() as session:
            # 查询所有数据
            pools = session.query(Pool).all()
            clusters = session.query(Cluster).all()
            hosts = session.query(Host).all()
            domains = session.query(Domain).filter(Domain.domain_recycle_id == "0").all()

            # 创建嵌套结构
            result = {
                "id": "1",
                "name": "资源节点",
                "type": "ziyuan",
                "children": []
            }
            
            # 构建池节点及其子节点
            for pool in pools:
                pool_node = {
                    "id": str(pool.id),
                    "name": pool.name,
                    "type": "pool",
                    "children": []
                }
                
                # 添加集群节点到池节点
                for cluster in [c for c in clusters if c.pool_id == str(pool.id)]:
                    cluster_node = {
                        "id": str(cluster.id),
                        "name": cluster.name,
                        "type": "cluster",
                        "children": []
                    }
                    
                    # 添加主机节点到集群节点
                    for host in [h for h in hosts if h.cluster_id == str(cluster.id)]:
                        host_node = {
                            "id": str(host.id),
                            "name": host.name,
                            "ip": host.ip,
                            "type": "host",
                            "children": []
                        }
                        
                        # 添加虚拟机节点到主机节点
                        for domain in [d for d in domains if d.host_id == str(host.id)]:
                            domain_node = {
                                "id": str(domain.id),
                                "name": domain.name,
                                "type": "domain"
                                # 虚拟机节点没有子节点，所以不需要children字段
                            }
                            host_node["children"].append(domain_node)
                        
                        cluster_node["children"].append(host_node)
                    
                    pool_node["children"].append(cluster_node)
                
                result["children"].append(pool_node)
        
        return {"msg": "查询资源树成功", "code": 200, "data": result}