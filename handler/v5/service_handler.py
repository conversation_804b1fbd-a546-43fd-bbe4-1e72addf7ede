# -*- coding: utf-8 -*-
from sqlalchemy import desc

import pyrestful.rest
from api.prometheus.client import Client as Pclient
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool

from api.log.log import CustomLogger
from celery import Celery, chord

import traceback
import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class ServiceHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    # @get(_path="/v5/prometheus/service", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    # def hci_get_host_list(self):
    #     with self.session_scope() as session:
    #         # 假设这是你的端口列表
    #         EXPORT_PORTS = [9100]
    #         # 首先创建基础查询
    #         hosts = session.query(Host).order_by(desc("created_at")).all()

    #         # 构建符合 Prometheus 服务发现格式的数据
    #         prometheus_targets = []
    #         for host in hosts:
    #             for port in EXPORT_PORTS:
    #                 target = f"{host.ip}:{port}"
    #                 labels = {
    #                     "job": "export",
    #                     "__address__": target,
    #                     "instance": host.name,
    #                     "host_id": str(host.id)
    #                 }
    #                 prometheus_targets.append({"labels": labels})

    #         return prometheus_targets