# -*- coding: utf-8 -*-
from util.decorators import role_required
import tornado.ioloop
import pyrestful.rest
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from util.cov import todict
from db.model.hci.compute import Domain, Host, DomainDisk, DomainXml, DomainInterface,Cluster
from db.model.hci.storage import StorageVolume, StoragePool
from db.model.hci.backup import DomainBackup, BackupVolumeMapping
from db.model.hci.network import Switch, SwitchPorts, SwitchPortGroups
from util.tools import generate_unique_port_name
import random
import string
from config import settings
import uuid
import datetime
import json

from app.agents.vm_tasks import (
    create_vm_external_snapshot,
    delete_vm_external_snapshot,
    backup_create,
    backup_restore
)

from app.tasks.vm_tasks import (
    backup_create_callback,
    backup_delete_callback,
    distach_op_backup_del,
    backup_restore_callback
)

from util.decorators import deprecated_api, role_required
from settings import QUEUE_NAME

from api.log.log import CustomLogger
import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()

import uuid
import datetime
import os


def generate_unique_volume_name(session, base_name, suffix="restored", length=6):
    """生成唯一的存储卷名称"""
    while True:
        rand_str = ''.join(random.choices(
            string.ascii_lowercase + string.digits, k=length))
        new_name = f"{base_name}_{suffix}_{rand_str}"
        exists = session.query(StorageVolume).filter(
            StorageVolume.name == new_name).first()
        if not exists:
            return new_name


def get_new_volume_path(storage_pool_path, new_name, fmt="qcow2"):
    """生成新的卷路径"""
    clean_path = storage_pool_path.rstrip('/')
    return f"{clean_path}/{new_name}.{fmt}"


def generate_random_mac():
    """生成随机MAC地址"""
    mac_bytes = [0x52, 0x54, 0x00, 
                random.randint(0x00, 0xff),
                random.randint(0x00, 0xff), 
                random.randint(0x00, 0xff)]
    return ':'.join([f'{b:02x}' for b in mac_bytes])


class BackupHandler(pyrestful.rest.RestHandler):
    """虚拟机备份处理器"""
    
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "x-requested-with")
        self.set_header("Access-Control-Allow-Methods", "POST, GET, PUT, DELETE")

    def _get_vm_info(self, session: Session, vm_id):
        """
        获取虚拟机详细信息，包括主机和磁盘信息
        """
        # 查询虚拟机基本信息和主机信息
        vm_info = (
            session.query(Domain, Host)
            .join(Host, Domain.host_id == Host.id)
            .filter(Domain.id == vm_id)
            .first()
        )
        
        if not vm_info:
            return None
            
        domain, host = vm_info
        
        # 查询虚拟机磁盘信息
        disk_info = (
            session.query(DomainDisk, StorageVolume)
            .outerjoin(StorageVolume, StorageVolume.id == DomainDisk.storage_vol_id)
            .filter(DomainDisk.domain_id == vm_id)
            .all()
        )
        
        disks = []
        for disk, volume in disk_info:
            disk_data = disk.to_dict()
            if volume:
                disk_data['volume'] = volume.to_dict()
            disks.append(disk_data)
        
        return {
            'domain': domain.to_dict(),
            'host': host.to_dict(),
            'disks': disks
        }

    @deprecated_api()
    @post(_path="/v5/backup/snapshot", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @role_required
    def hci_post_backup_snapshot_create(self, form):
        """
        创建虚拟机外部快照
        
        ⚠️ 此接口已废弃，将在后续版本中移除
        ⚠️ 请使用新的备份管理接口替代
        
        请求参数:
        {
            "vm_id": "虚拟机ID",
            "vm_name": "虚拟机名称", 
            "snapshot_name": "快照名称",
            "description": "快照描述"
        }
        """
        try:
            vm_id = form.get('vm_id')
            vm_name = form.get('vm_name')
            snapshot_name = form.get('snapshot_name')
            description = form.get('description', '')
            
            if not vm_id or not snapshot_name:
                return {"msg": "缺少必要参数", "code": 400}
            
            with self.session_scope() as session:
                # 获取虚拟机信息
                vm_info = self._get_vm_info(session, vm_id)
                if not vm_info:
                    return {"msg": "虚拟机不存在", "code": 404}
                
                # 准备异步任务参数
                task_form = {
                    'vm_id': vm_id,
                    'vm_name': vm_name or vm_info['domain']['name'],
                    'snapshot_name': snapshot_name,
                    'description': description,
                    'host_ip': vm_info['host']['ip'],
                    'disks': vm_info['disks']
                }
                

                
                return {
                    "msg": "ok",
                    "code": 200,
                    "data": {

                        "snapshot_name": snapshot_name,
                        "status": "pending"
                    }
                }
                
        except Exception as e:
            return {"msg": f"创建快照失败: {str(e)}", "code": 500}

    @deprecated_api()
    @delete(_path="/v5/backup/snapshot", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @role_required  
    def hci_delete_backup_snapshot(self, form):
        """
        删除虚拟机快照
        
        ⚠️ 此接口已废弃，将在后续版本中移除
        ⚠️ 请使用新的备份管理接口替代
        
        请求参数:
        {
            "vm_id": "虚拟机ID",
            "snapshot_name": "快照名称"
        }
        """
        try:
            vm_id = form.get('vm_id')
            snapshot_name = form.get('snapshot_name')
            
            if not vm_id or not snapshot_name:
                return {"msg": "缺少必要参数", "code": 400}
            
            with self.session_scope() as session:
                # 获取虚拟机信息
                vm_info = self._get_vm_info(session, vm_id)
                if not vm_info:
                    return {"msg": "虚拟机不存在", "code": 404}
                
                # 准备异步任务参数
                task_form = {
                    'vm_id': vm_id,
                    'vm_name': vm_info['domain']['name'],
                    'snapshot_name': snapshot_name,
                    'host_ip': vm_info['host']['ip']
                }
                

                return {
                    "msg": "快照删除任务已提交",
                    "code": 200,
                    "data": {
                        "status": "pending"
                    }
                }
                
        except Exception as e:
            return {"msg": f"删除快照失败: {str(e)}", "code": 500}

    @deprecated_api()
    @post(_path="/v5/backup/restore/snapshot", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @role_required
    def hci_post_backup_restore(self, form):
        """
        从快照恢复虚拟机
        
        ⚠️ 此接口已废弃，将在后续版本中移除
        ⚠️ 请使用新的备份管理接口替代
        
        请求参数:
        {
            "vm_id": "虚拟机ID",
            "snapshot_name": "快照名称",
            "restore_type": "恢复类型: full/incremental"
        }
        """
        try:
            vm_id = form.get('vm_id')
            snapshot_name = form.get('snapshot_name')
            restore_type = form.get('restore_type', 'full')
            
            if not vm_id or not snapshot_name:
                return {"msg": "缺少必要参数", "code": 400}
            
            with self.session_scope() as session:
                # 获取虚拟机信息
                vm_info = self._get_vm_info(session, vm_id)
                if not vm_info:
                    return {"msg": "虚拟机不存在", "code": 404}
                
                # 准备异步任务参数
                task_form = {
                    'vm_id': vm_id,
                    'vm_name': vm_info['domain']['name'],
                    'snapshot_name': snapshot_name,
                    'restore_type': restore_type,
                    'host_ip': vm_info['host']['ip'],
                    'disks': vm_info['disks']
                }
                

                return {
                    "msg": "ok",
                    "code": 200,
                    "data": {
                        "restore_type": restore_type,
                        "status": "pending"
                    }
                }
                
        except Exception as e:
            return {"msg": f"恢复快照失败: {str(e)}", "code": 500}

    @deprecated_api()
    @post(_path="/v5/backup/export", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @role_required
    def hci_post_backup_export(self, form):
        """
        导出虚拟机备份
        
        ⚠️ 此接口已废弃，将在后续版本中移除
        ⚠️ 请使用新的备份管理接口替代
        
        请求参数:
        {
            "vm_id": "虚拟机ID",
            "snapshot_name": "快照名称",
            "export_path": "导出路径",
            "export_format": "导出格式: qcow2/raw/vmdk"
        }
        """
        try:
            vm_id = form.get('vm_id')
            snapshot_name = form.get('snapshot_name')
            export_path = form.get('export_path')
            export_format = form.get('export_format', 'qcow2')
            
            if not vm_id or not snapshot_name or not export_path:
                return {"msg": "缺少必要参数", "code": 400}
            
            with self.session_scope() as session:
                # 获取虚拟机信息
                vm_info = self._get_vm_info(session, vm_id)
                if not vm_info:
                    return {"msg": "虚拟机不存在", "code": 404}
                
                # 准备异步任务参数
                task_form = {
                    'vm_id': vm_id,
                    'vm_name': vm_info['domain']['name'],
                    'snapshot_name': snapshot_name,
                    'export_path': export_path,
                    'export_format': export_format,
                    'host_ip': vm_info['host']['ip'],
                    'disks': vm_info['disks']
                }

                
                return {
                    "msg": "备份导出任务已提交",
                    "code": 200,
                    "data": {
                        "export_path": export_path,
                        "export_format": export_format,
                        "status": "pending"
                    }
                }
                
        except Exception as e:
            return {"msg": f"导出备份失败: {str(e)}", "code": 500}

    @deprecated_api()
    @get(_path="/v5/backup/snapshot/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @role_required
    def hci_get_backup_snapshot_list(self, vm_id=None):
        """
        获取虚拟机快照列表
        
        ⚠️ 此接口已废弃，将在后续版本中移除
        ⚠️ 请使用新的备份管理接口替代
        
        查询参数:
        vm_id: 虚拟机ID
        """
        try:
            if not vm_id:
                return {"msg": "缺少虚拟机ID参数", "code": 400}
            
            with self.session_scope() as session:
                # 获取虚拟机信息
                vm_info = self._get_vm_info(session, vm_id)
                if not vm_info:
                    return {"msg": "虚拟机不存在", "code": 404}
                
                # 准备异步任务参数
                task_form = {
                    'vm_id': vm_id,
                    'vm_name': vm_info['domain']['name'],
                    'host_ip': vm_info['host']['ip']
                }
                

                return {
                    "msg": "获取快照列表成功",
                    "code": 200,
                    "data": {

                    }
                }
                
        except Exception as e:
            return {"msg": f"获取快照列表失败: {str(e)}", "code": 500}


    # ==================== 新版备份管理接口 ====================

    @role_required()
    @post(_path="/v5/backup/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_backup_create(self, form):
        """
        创建虚拟机备份
        
        请求参数:
        {
            "vm_id": "虚拟机ID",
            "backup_name": "备份名称",
            "description": "备份描述",
            "backup_type": "备份类型: external/internal/full",
            "retention_days": "保留天数",
            "is_auto_backup": "是否自动备份"
        }
        """
        try:
            username = self.get_cookie("username")
            role = self.get_cookie("role")


            vm_id = form.get('vm_id')
            backup_name = form.get('backup_name')
            description = form.get('description', '')
            backup_type = form.get('backup_type', 'external')
            retention_days = form.get('retention_days')
            is_auto_backup = form.get('is_auto_backup', False)
            
            if not vm_id:
                return {"msg": "缺少虚拟机ID参数", "code": 400}
            
            # 如果没有提供备份名称，生成默认名称
            if not backup_name:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            with self.session_scope() as session:
                # 查询虚拟机信息和主机信息
                vm_info = (
                    session.query(Domain, Host)
                    .join(Host, Domain.host_id == Host.id)
                    .filter(Domain.id == vm_id)
                    .first()
                )
                
                if not vm_info:
                    return {"msg": "虚拟机不存在", "code": 200}
                
                domain, host = vm_info
                
                # 获取虚拟机的XML配置
                domain_xml_record = (
                    session.query(DomainXml)
                    .filter(
                        DomainXml.domain_id == vm_id,
                        DomainXml.xml_type == 'domain',
                        DomainXml.is_active == True
                    )
                    .order_by(DomainXml.version.desc())
                    .first()
                )
                
                domain_xml = domain_xml_record.xml_content if domain_xml_record else None
                
                # 检查该虚拟机是否有正在进行的备份任务
                existing_backup = (
                    session.query(DomainBackup)
                    .filter(
                        DomainBackup.domain_id == vm_id,
                        DomainBackup.backup_status == 'creating'
                    )
                    .order_by(DomainBackup.created_at.desc())
                    .first()
                )
                
                if existing_backup:
                    return {
                        "msg": "该虚拟机有正在进行的备份任务，请等待完成后再创建新备份",
                        "code": 200,
                        "data": {
                            "existing_backup_id": str(existing_backup.id),  # 将UUID转换为字符串
                            "existing_backup_name": existing_backup.backup_name,
                            "backup_start_time": existing_backup.backup_start_time.isoformat() if existing_backup.backup_start_time else None
                        }
                    }
                
                # 检查是否存在同名的备份
                existing_backup = session.query(DomainBackup).filter(
                    DomainBackup.domain_id == vm_id,
                    DomainBackup.backup_name == backup_name,
                    DomainBackup.backup_status != 'deleted'  # 排除已删除的备份
                ).first()
                
                if existing_backup:
                    return {
                        "msg": f"备份名称 '{backup_name}' 已存在",
                        "code": 400,
                        "data": {
                            "existing_backup_id": str(existing_backup.id),  # 将UUID转换为字符串
                            "existing_backup_status": existing_backup.backup_status,
                            "created_at": existing_backup.created_at.isoformat() if existing_backup.created_at else None
                        }
                    }
                
                # 查询虚拟机的存储卷信息，包括存储池信息
                disk_volumes = (
                    session.query(DomainDisk, StorageVolume, StoragePool)
                    .join(StorageVolume, StorageVolume.id == DomainDisk.storage_vol_id)
                    .join(StoragePool, StoragePool.id == StorageVolume.storage_pool_id)
                    .filter(DomainDisk.domain_id == vm_id)
                    .all()
                )
                
                # 过滤存储卷：跳过Ceph类型的存储池和raw格式的存储卷
                valid_volumes = []
                skipped_volumes = []
                
                for disk, volume, pool in disk_volumes:
                    # 跳过Ceph类型的存储池
                    if pool.type_code and pool.type_code.lower() == 'ceph':
                        skipped_volumes.append({
                            'volume_name': volume.name,
                            'reason': 'Ceph存储池不支持备份'
                        })
                        continue
                    
                    # 跳过raw格式的存储卷
                    if volume.volume_type and volume.volume_type.lower() == 'raw':
                        skipped_volumes.append({
                            'volume_name': volume.name,
                            'reason': 'Raw格式存储卷不支持备份'
                        })
                        continue
                    
                    valid_volumes.append({
                        'disk': disk.to_dict() if hasattr(disk, 'to_dict') else disk.__dict__,
                        'volume': volume.to_dict() if hasattr(volume, 'to_dict') else volume.__dict__,
                        'pool': pool.to_dict() if hasattr(pool, 'to_dict') else pool.__dict__
                    })
                
                if not valid_volumes:
                    return {
                        "msg": "没有可备份的存储卷",
                        "code": 400,
                        "data": {
                            "skipped_volumes": skipped_volumes
                        }
                    }
                
                # 创建备份记录
                backup_id = str(uuid.uuid4())
                backup_record = DomainBackup(
                    id=backup_id,
                    domain_id=vm_id,
                    backup_name=backup_name,
                    backup_type=backup_type,
                    backup_status='creating',
                    backup_xml=domain_xml,
                    description=description,
                    backup_reason='manual',
                    host_id=host.id,
                    host_ip=host.ip,
                    disk_count=len(valid_volumes),
                    retention_days=retention_days,
                    is_auto_backup=is_auto_backup,
                    created_by=username,
                    created_by_role=role,
                    backup_start_time=datetime.datetime.now()
                )
                
                session.add(backup_record)
                session.flush()  # 获取生成的ID
                
                # 准备异步任务参数
                task_form = {
                    'backup_id': backup_id,
                    'vm_id': vm_id,
                    'vm_name': domain.name,
                    'backup_name': backup_name,
                    'backup_type': backup_type,
                    'description': description,
                    'host_ip': host.ip,
                    'host_id': str(host.id),
                    'valid_volumes': valid_volumes,
                    'created_by': username,
                    'created_by_role': role
                }
                
                # 提交异步任务，设置回调函数
                queue = "queue_" + host.ip
                result = backup_create.apply_async(
                    args=[task_form],
                    queue=queue,
                    link=backup_create_callback.s().set(queue=QUEUE_NAME)
                )
                
                session.commit()
                
                return {
                    "code": 200,
                    "msg": "ok",
                    "data": {
                        "message": "开始创建备份",
                        "backup_id": backup_id,
                        "backup_name": backup_name,
                        "valid_volumes_count": len(valid_volumes),
                        "skipped_volumes": skipped_volumes
                    }
                }
                
        except Exception as e:
            return {"msg": f"创建备份失败: {str(e)}", "code": 500}

    @role_required()
    @post(_path="/v5/backup/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_get_backup_list(self, form):
        """
        获取备份列表
        
        查询参数:
        vm_id: 虚拟机ID（可选）
        page: 页码，默认1
        pagecount: 每页数量，默认20
        order_type: 排序类型，默认desc（降序）
        order_by: 排序字段，默认created_at
        search_str: 搜索关键词（可选）
        """
        try:
            vm_id = form.get('vm_id')
            page = int(form.get("page", 1))
            pagecount = int(form.get("pagecount", 20))
            order_type = form.get("order_type", "desc")
            order_by = form.get("order_by", "created_at") or "created_at"  # 确保不为空
            search_str = form.get("search_str")
            
            # 参数验证
            page = max(int(page), 1)
            pagecount = min(max(int(pagecount), 1), 100)  # 限制最大每页100条
            
            # 验证排序字段
            valid_order_fields = ['created_at', 'updated_at', 'backup_name', 'backup_start_time']
            if order_by not in valid_order_fields:
                order_by = 'created_at'
            
            with self.session_scope() as session:
                # 构建查询条件
                query = session.query(DomainBackup).join(Domain)
                
                # 按虚拟机ID过滤
                if vm_id:
                    query = query.filter(DomainBackup.domain_id == vm_id)
                
                # 只查询未删除的备份
                query = query.filter(DomainBackup.backup_status != 'deleted')
                
                # 按创建时间倒序排列
                if order_type == 'desc':
                    query = query.order_by(getattr(DomainBackup, order_by).desc())
                else:
                    query = query.order_by(getattr(DomainBackup, order_by).asc())
                
                # 计算总数
                total_count = query.count()
                
                # 分页查询
                offset = (page - 1) * pagecount
                backups = query.offset(offset).limit(pagecount).all()
                
                # 收集所有备份ID和虚拟机ID
                backup_ids = [str(backup.id) for backup in backups]
                domain_ids = [str(backup.domain_id) for backup in backups]
                
                # 批量查询虚拟机信息
                domains = {}
                if domain_ids:
                    domain_records = session.query(Domain).filter(Domain.id.in_(domain_ids)).all()
                    domains = {str(d.id): d for d in domain_records}
                
                # 批量查询备份卷映射
                volume_mappings = {}
                if backup_ids:
                    mappings = session.query(BackupVolumeMapping).filter(
                        BackupVolumeMapping.vm_backup_id.in_(backup_ids)
                    ).all()
                    
                    # 收集存储卷ID
                    volume_ids = [str(m.storage_volume_id) for m in mappings]
                    
                    # 批量查询存储卷
                    volumes = {}
                    if volume_ids:
                        volume_records = session.query(StorageVolume).filter(
                            StorageVolume.id.in_(volume_ids)
                        ).all()
                        volumes = {str(v.id): v for v in volume_records}
                    
                    # 组织映射关系
                    for mapping in mappings:
                        backup_id = str(mapping.vm_backup_id)
                        if backup_id not in volume_mappings:
                            volume_mappings[backup_id] = []
                        
                        volume = volumes.get(str(mapping.storage_volume_id))
                        if volume:
                            volume_mappings[backup_id].append(volume)
                
                # 构建返回数据
                backup_list = []
                for backup in backups:
                    # 获取虚拟机名称
                    domain = domains.get(str(backup.domain_id))
                    domain_name = domain.name if domain else None
                    
                    # 获取备份关联的存储卷信息
                    backup_volumes = []
                    total_volume_size = 0
                    
                    for volume in volume_mappings.get(str(backup.id), []):
                        volume_info = {
                            "volume_id": str(volume.id),
                            "volume_name": volume.name,
                            "volume_path": volume.path,
                            "volume_type": volume.volume_type,
                            "protocol_type": volume.protocol_type,
                            "capacity": volume.capacity or 0,
                            "allocation": volume.allocation or 0,
                            "use_type": volume.use_type,
                            "status": volume.status,
                            "created_at": volume.created_at.isoformat() if volume.created_at else None
                        }
                        backup_volumes.append(volume_info)
                        total_volume_size += volume.capacity or 0
                    
                    # 构建备份信息
                    backup_info = {
                        "id": str(backup.id),
                        "backup_name": backup.backup_name,
                        "backup_type": backup.backup_type,
                        "backup_status": backup.backup_status,
                        "description": backup.description,
                        "backup_reason": backup.backup_reason,
                        
                        # 虚拟机信息
                        "domain_id": str(backup.domain_id),
                        "domain_name": domain_name,
                        
                        # 主机信息
                        "host_id": str(backup.host_id) if backup.host_id else None,
                        "host_ip": backup.host_ip,
                        
                        # 大小统计
                        "total_size": backup.total_size or 0,
                        "disk_count": backup.disk_count or 0,
                        "volume_count": len(backup_volumes),
                        "total_volume_size": total_volume_size,
                        
                        # 时间信息
                        "backup_start_time": backup.backup_start_time.isoformat() if backup.backup_start_time else None,
                        "backup_end_time": backup.backup_end_time.isoformat() if backup.backup_end_time else None,
                        "created_at": backup.created_at.isoformat() if backup.created_at else None,
                        "updated_at": backup.updated_at.isoformat() if backup.updated_at else None,
                        
                        # 用户信息
                        "created_by": backup.created_by,
                        "created_by_role": backup.created_by_role,
                        
                        # 备份策略信息
                        "retention_days": backup.retention_days,
                        "is_auto_backup": backup.is_auto_backup,
                        
                        # 错误信息
                        "error_message": backup.error_message,
                        
                        # 备份卷信息
                        "backup_volumes": backup_volumes,
                        
                        # 元数据（可选，包含详细信息）
                        "backup_metadata": backup.backup_metadata
                    }
                    
                    backup_list.append(backup_info)
                
                # 计算分页信息
                total_pages = (total_count + pagecount - 1) // pagecount
                has_next = page < total_pages
                has_prev = page > 1
                
                return {
                    "msg": "ok",
                    "code": 200,
                    "total": total_count,
                    "data": backup_list,
                    "pagination": {
                        "page": page,
                        "page_size": pagecount,
                        "total_pages": total_pages,
                        "has_next": has_next,
                        "has_prev": has_prev
                    }
                }
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {"msg": f"获取备份列表失败: {str(e)}", "code": 500}

    @role_required()
    @delete(_path="/v5/backup/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_backup(self, form):
        """
        删除虚拟机备份
        
        请求参数:
        {
            "ids": ["备份ID列表"] (可选),
            "names": ["备份名称列表"] (可选),
            "force_delete": "是否强制删除（删除磁盘文件）" (可选，默认false)
        }
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        ids = form.get('ids', [])
        names = form.get('names', [])
        force_delete = form.get('force_delete', True)
        
        # 确保参数为列表格式
        if isinstance(ids, str):
            ids = [ids]
        if isinstance(names, str):
            names = [names]
        
        if not ids and not names:
            return {"msg": "必须提供备份ID或备份名称", "code": 400}
        
        try:
            with self.session_scope() as session:
                # 查询要删除的备份记录
                query = session.query(DomainBackup)
                
                # 构建查询条件
                conditions = []
                if ids:
                    conditions.append(DomainBackup.id.in_(ids))
                # if names:
                #     conditions.append(DomainBackup.backup_name.in_(names))
                
                if len(conditions) > 1:
                    query = query.filter(or_(*conditions))
                else:
                    query = query.filter(conditions[0])
                
                # 排除已删除的备份
                query = query.filter(DomainBackup.backup_status != 'deleted')
                
                backups_to_delete = query.all()
                
                if not backups_to_delete:
                    return {"msg": "没有找到符合条件的备份", "code": 404}
                
                # 批量更新备份状态为 deleting
                backup_ids_to_update = [backup.id for backup in backups_to_delete]
                session.query(DomainBackup).filter(DomainBackup.id.in_(backup_ids_to_update)).update(
                    {DomainBackup.backup_status: "deleting"},
                    synchronize_session=False
                )
                session.commit()
                
                # 重新查询获取完整信息 - 手动关联查询
                backups = session.query(DomainBackup).filter(DomainBackup.id.in_(backup_ids_to_update)).all()
                
                # 收集所有domain_id和host_id用于批量查询
                domain_ids = [backup.domain_id for backup in backups]
                host_ids = [backup.host_id for backup in backups]
                
                # 批量查询domain和host信息
                domains = session.query(Domain).filter(Domain.id.in_(domain_ids)).all()
                hosts = session.query(Host).filter(Host.id.in_(host_ids)).all()
                
                # 创建映射字典
                domain_map = {domain.id: domain for domain in domains}
                host_map = {host.id: host for host in hosts}
                
                # 为每个备份添加关联信息
                for backup in backups:
                    backup._domain = domain_map.get(backup.domain_id)
                    backup._host = host_map.get(backup.host_id)
            
            # 转换为字典格式，添加用户信息
            backups_data = []
            for backup in backups:
                backup_dict = backup.to_dict()
                backup_dict['role'] = role
                backup_dict['username'] = username
                backup_dict['force_delete'] = force_delete
                
                # 添加虚拟机和主机信息
                if hasattr(backup, '_domain') and backup._domain:
                    backup_dict['domain'] = backup._domain.to_dict()
                if hasattr(backup, '_host') and backup._host:
                    backup_dict['host'] = backup._host.to_dict()
                    
                backups_data.append(backup_dict)
            
            try:
                # 异步调用分发任务
                distach_op_backup_del.apply_async(args=[backups_data], queue=QUEUE_NAME)
            except Exception as e:
                import traceback
                traceback.print_exc()
                return {"msg": f"备份删除操作失败: {e}", "code": 500}
            
            return {"msg": "ok", "code": 200, "data": "备份删除操作开始"}
            
        except Exception as e:
            print(f"删除备份异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"msg": f"删除备份失败: {str(e)}", "code": 500}

    @role_required()
    @post(_path="/v5/backup/restore", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_backup_restore_new(self, form):
        """
        从备份恢复虚拟机（创建新虚拟机）
        
        请求参数:
        {
            "backup_id": "备份ID",
            "new_vm_name": "新虚拟机名称（可选，默认使用备份名称）"
        }
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        backup_id = form.get('backup_id')
        new_vm_name = form.get('new_vm_name', '')
        
        if not backup_id:
            return {"msg": "必须提供备份ID", "code": 400}
        
        try:
            with self.session_scope() as session:
                # 1. 查询备份信息
                backup = session.query(DomainBackup).filter(DomainBackup.id == backup_id).first()
                
                if not backup:
                    return {"msg": "备份不存在", "code": 404}
                
                if backup.backup_status != 'completed':
                    return {"msg": f"备份状态不正确，当前状态: {backup.backup_status}", "code": 400}
                
                # 2. 获取原虚拟机信息 - 手动查询
                original_vm = session.query(Domain).filter(Domain.id == backup.domain_id).first()
                if not original_vm:
                    return {"msg": "备份关联的虚拟机不存在", "code": 404}
                
                # 获取主机信息
                host = session.query(Host).filter(Host.id == backup.host_id).first()
                if not host:
                    return {"msg": "备份关联的主机不存在", "code": 404}
                
                # 获取集群和存储池信息
                cluster = session.query(Cluster).filter(Cluster.id == original_vm.cluster_id).first()
                pool = session.query(StoragePool).filter(StoragePool.id == original_vm.pool_id).first()
                
                # 3. 确定新虚拟机名称（处理重复名称）
                if not new_vm_name:
                    new_vm_name = f"{backup.backup_name}_restored"

                # 6. 生成新虚拟机ID
                new_vm_id = str(uuid.uuid4())

                # 检查并处理虚拟机名称重复
                base_name = new_vm_name
                counter = 1
                while True:
                    existing_vm = session.query(Domain).filter(Domain.name == new_vm_name).first()
                    if not existing_vm:
                        break
                    new_vm_name = f"{base_name}_{counter}"
                    counter += 1
                
                # 4. 查询备份关联的存储卷信息并创建新卷
                backup_volumes = []
                new_volumes = []
                volume_mappings = session.query(BackupVolumeMapping).filter(
                    BackupVolumeMapping.vm_backup_id == backup_id
                ).all()
                
                for mapping in volume_mappings:
                    volume = session.query(StorageVolume).filter(
                        StorageVolume.id == mapping.storage_volume_id
                    ).first()
                    
                    if volume:
                        # 查询存储池信息
                        storage_pool = session.query(StoragePool).filter(
                            StoragePool.id == volume.storage_pool_id
                        ).first()
                        
                        # 生成新卷名称和路径
                        new_volume_name = generate_unique_volume_name(
                            session, volume.name, "restored"
                        )
                        
                        if storage_pool:
                            new_volume_path = get_new_volume_path(
                                storage_pool.storage_local_dir, 
                                new_volume_name, 
                                volume.volume_type or "qcow2"
                            )
                        else:
                            # 如果存储池不存在，使用默认路径
                            new_volume_path = f"/var/lib/libvirt/images/{new_volume_name}.{volume.volume_type or 'qcow2'}"
                        
                        # 创建新的存储卷记录
                        new_volume = StorageVolume(
                            name=new_volume_name,
                            storage_pool_id=volume.storage_pool_id,
                            path=new_volume_path,
                            volume_type=volume.volume_type,
                            protocol_type=volume.protocol_type,
                            join_type=volume.join_type,
                            encrypt=volume.encrypt,
                            status=1,  # 新卷状态为正常
                            capacity=volume.capacity,
                            allocation=volume.allocation,
                            preallocation=volume.preallocation,
                            use_type="data",  # 新卷用途为数据卷
                            created_at=datetime.datetime.now(),
                            updated_at=datetime.datetime.now(),
                            remark=f"从备份 {backup.backup_name} 还原的卷",
                        )
                        session.add(new_volume)
                        session.flush()  # 获取新卷ID
                        
                        backup_volumes.append({
                            "original_volume_id": str(volume.id),
                            "original_volume_name": volume.name,
                            "original_path": volume.path,
                            "new_volume_id": str(new_volume.id),
                            "new_volume_name": new_volume_name,
                            "new_path": new_volume_path,
                            "size": volume.capacity,
                            "capacity": volume.capacity,
                            "storage_pool_id": str(volume.storage_pool_id),
                            "volume_type": volume.volume_type,
                            "use_type": new_volume.use_type,
                            "remark": new_volume.remark
                        })
                        
                        new_volumes.append(new_volume)
                
                # 5. 处理网络接口信息
                network_interfaces = []
                
                # 检查原虚拟机是否仍然存在
                current_vm = session.query(Domain).filter(Domain.id == original_vm.id).first()
                
                if current_vm and current_vm.status != 'deleted':
                    # 原虚拟机存在，查询其网络接口和交换机信息
                    vm_interfaces = session.query(DomainInterface).filter(
                        DomainInterface.domain_id == original_vm.id
                    ).all()
                    
                    for interface in vm_interfaces:
                        interface_info = {
                            "mac_address": interface.mac,
                            "ip_address": interface.ip,
                            "switch_info": None,
                            "port_info": None,
                            "port_group_info": None
                        }
                        
                        # 查询交换机端口信息
                        if interface.switch_port_id:
                            switch_port = session.query(SwitchPorts).filter(
                                SwitchPorts.id == interface.switch_port_id
                            ).first()
                            
                            if switch_port:
                                interface_info["port_info"] = {
                                    "port_id": str(switch_port.id),
                                    "port_name": switch_port.name,
                                    "ip": switch_port.ip,
                                    "netmask": switch_port.netmask,
                                    "gateway": switch_port.gate_way,
                                    "port_type": switch_port.port_type,
                                    "interface": switch_port.interface
                                }
                                
                                # 查询交换机信息
                                switch = session.query(Switch).filter(
                                    Switch.id == switch_port.switchs_id
                                ).first()
                                
                                if switch:
                                    interface_info["switch_info"] = {
                                        "switch_id": str(switch.id),
                                        "switch_name": switch.name,
                                        "ovs_name": switch.ovs_name,
                                        "vswitch_type": switch.vswitch_type,
                                        "switch_type": switch.vswitch_type,
                                        "host_id": str(switch.host_id)
                                    }
                                
                                # 查询端口组信息
                                if switch_port.switch_port_group_id:
                                    port_group = session.query(SwitchPortGroups).filter(
                                        SwitchPortGroups.id == switch_port.switch_port_group_id
                                    ).first()
                                    
                                    if port_group:
                                        interface_info["port_group_info"] = {
                                            "group_id": str(port_group.id),
                                            "group_name": port_group.name,
                                            "vlan_id": port_group.vlan_id,
                                        }
                        
                        network_interfaces.append(interface_info)
                        
                    # 为新虚拟机创建新的网络端口
                    new_network_ports = []
                    new_interfaces = []
                    for interface in network_interfaces:
                        if interface.get("port_info") and interface.get("switch_info"):
                            # 创建新的交换机端口
                            old_port_info = interface["port_info"]
                            switch_info = interface["switch_info"]
                            
                            new_port_name = generate_unique_port_name(switch_info["switch_name"])
                            new_switch_port = SwitchPorts(
                                id=str(uuid.uuid4()),
                                name=new_port_name,
                                switchs_id=switch_info["switch_id"],
                                switch_port_group_id=interface.get("port_group_info", {}).get("group_id") if interface.get("port_group_info") else None,
                                gate_way=old_port_info.get("gateway"),
                                ip=old_port_info.get("ip"),
                                netmask=old_port_info.get("netmask"),
                                port_type=old_port_info.get("port_type"),
                                interface=old_port_info.get("interface"),
                                status="active",
                                created_at=datetime.datetime.now(),
                                updated_at=datetime.datetime.now(),
                            )
                            session.add(new_switch_port)
                            session.flush()  # 获取新端口ID
                            
                            # 生成新的MAC地址
                            new_mac = generate_random_mac()
                            
                            # 创建新的虚拟机网络接口
                            new_domain_interface = DomainInterface(
                                id=str(uuid.uuid4()),
                                domain_id=new_vm_id,
                                switch_port_id=new_switch_port.id,
                                type_code='bridge',
                                mac=new_mac,
                                model='virtio',
                            )
                            session.add(new_domain_interface)
                            
                            new_network_ports.append({
                                "new_port_id": str(new_switch_port.id),
                                "new_port_name": new_port_name,
                                "original_port_info": old_port_info,
                                "switch_info": switch_info,
                                "port_group_info": interface.get("port_group_info"),
                                "interface_config": {
                                    "interface_name": interface["port_info"]["port_name"],
                                    "interface_type": "bridge",
                                    "bridge_name": interface['switch_info']["ovs_name"],
                                    "mac_address": new_mac,
                                    "ip_address": interface.get("ip_address")
                                }
                            })
                            
                            new_interfaces.append({
                                "interface_name": interface["port_info"]["port_name"],
                                "mac": new_mac,
                                "ip": interface.get("ip_address"),
                                "type_code": "bridge",
                                "bridge": interface['switch_info']["ovs_name"],
                                "switch_port_id": str(new_switch_port.id),
                                "switch_port_name": new_port_name,
                                "network": switch_info.get("ovs_name"),
                                "port_name": new_port_name,
                                "switch_id": switch_info["switch_id"],
                                "vswitch_type": switch_info.get("vswitch_type"),
                                "switch_type": switch_info.get("switch_type")
                            })
                
                else:
                    # 原虚拟机不存在，使用备份时保存的网络配置
                    try:
                        # 解析备份元数据中的网络配置
                        if backup.backup_metadata:
                            try:
                                metadata = json.loads(backup.backup_metadata)
                                network_interfaces = metadata.get('vm_info', {}).get('network_interfaces', [])
                            except (json.JSONDecodeError, KeyError):
                                network_interfaces = []
                        else:
                            network_interfaces = []
                        
                        if network_interfaces:
                            # 根据备份元数据创建网络配置
                            new_interfaces = []
                            for interface in network_interfaces:
                                # 生成新的MAC地址
                                new_mac = generate_random_mac()
                                
                                # 从备份元数据中提取网络配置
                                interface_name = interface.get('interface_name', 'eth0')
                                bridge_name = interface.get('bridge_name', 'virbr0')
                                ip_address = interface.get('ip_address')
                                
                                new_interfaces.append({
                                    "interface_name": interface_name,
                                    "mac": new_mac,
                                    "ip": ip_address,
                                    "type_code": "bridge",
                                    "bridge": bridge_name,
                                    "network": bridge_name,
                                    "switch_id": interface.get('switch_id', ''),
                                    "switch_port_id": interface.get('switch_port_id', ''),
                                    "port_name": interface.get('port_name', ''),
                                    "vswitch_type": interface.get('vswitch_type', ''),
                                    "switch_type": interface.get('switch_type', '')
                                })
                            
                            # 如果没有网络接口，创建默认的
                            if not new_interfaces:
                                new_interfaces = [{
                                    "interface_name": "eth0",
                                    "mac": generate_random_mac(),
                                    "ip": None,
                                    "type_code": "bridge",
                                    "bridge": "virbr0",
                                    "network": "virbr0",
                                    "switch_id": "",
                                    "switch_port_id": "",
                                    "port_name": "",
                                    "vswitch_type": "",
                                    "switch_type": ""
                                }]
                    except Exception as e:
                        # 网络配置恢复失败，记录日志但不中断恢复流程
                        logger.error(f"网络配置恢复失败: {str(e)}")
                        # 创建最小化的默认网络配置
                        try:
                            new_interfaces = [{
                                "interface_name": "eth0",
                                "mac": generate_random_mac(),
                                "ip": None,
                                "type_code": "bridge",
                                "bridge": "virbr0",
                                "network": "virbr0",
                                "switch_id": "",
                                "switch_port_id": "",
                                "port_name": "",
                                "vswitch_type": "",
                                "switch_type": ""
                            }]
                        except Exception as fallback_error:
                            logger.error(f"默认网络配置创建失败: {str(fallback_error)}")
                
                # 验证和确保所有网络接口都有有效的MAC地址
                if 'new_interfaces' not in locals():
                    new_interfaces = []
                
                # 为每个网络接口确保有MAC地址
                for interface in new_interfaces:
                    if not interface.get('mac') or interface['mac'].strip() == '':
                        interface['mac'] = generate_random_mac()
                    
                    # 确保bridge字段存在
                    if not interface.get('bridge'):
                        interface['bridge'] = 'virbr0'
                
                # 7. 创建新虚拟机的数据库记录
                new_domain = Domain(
                    id=new_vm_id,
                    name=new_vm_name,
                    uuid=str(uuid.uuid4()),
                    domainname=new_vm_name,
                    vcpu=original_vm.vcpu,
                    memory=original_vm.memory,
                    hmemory=original_vm.memory,
                    os_type=original_vm.os_type,
                    os_version=original_vm.os_version,
                    cpu_arch=original_vm.cpu_arch,
                    host_id=original_vm.host_id,
                    cluster_id=original_vm.cluster_id,
                    pool_id=original_vm.pool_id,
                    status="creating",
                    vnc_port="0000",
                    spice_port="0000",
                    bind_ip_list="dhcp",
                    is_persistence=1,
                    secret_id="0",
                    secret_alg="0",
                    domain_recycle_id="0",
                    safe_status=0,
                    is_hide=0,
                    defense_status=0,
                    is_ignore=0,
                    is_ha=0,
                    auto_migrate=0,
                    is_losing_contact=0,
                    remark=f"从备份 {backup.backup_name} 还原",
                    created_at=datetime.datetime.now(),
                )
                session.add(new_domain)
                
                # 8. 创建新虚拟机的磁盘关联记录
                for i, backup_volume in enumerate(backup_volumes):
                    new_domain_disk = DomainDisk(
                        domain_id=new_vm_id,
                        host_id=original_vm.host_id,
                        storage_pool_id=backup_volume["storage_pool_id"],
                        storage_pool_type=5,  # DIR类型
                        storage_vol_id=backup_volume["new_volume_id"],
                        type_code="file",
                        device="disk",
                        dev=f"vd{chr(ord('a') + i)}",  # vda, vdb, vdc...
                        bus="virtio",
                        qemu_type=backup_volume["volume_type"] or "qcow2",
                        boot_order=1 if i == 0 else 0,  # 第一个磁盘为启动盘
                        created_at=datetime.datetime.now(),
                        updated_at=datetime.datetime.now(),
                    )
                    session.add(new_domain_disk)
                
                session.commit()  # 提交数据库更改
                
                # 9. 构建还原任务数据
                restore_data = {
                    "new_vm_id": new_vm_id,
                    "new_vm_name": new_vm_name,
                    "backup_id": backup_id,
                    "backup_name": backup.backup_name,
                    "backup_xml": backup.backup_xml,  # 添加备份时保存的虚拟机XML
                    "original_vm": {
                        "id": str(original_vm.id),
                        "name": original_vm.name,
                        "vcpu": original_vm.vcpu,
                        "memory": original_vm.memory,
                        "os_type": original_vm.os_type,
                        "os_version": original_vm.os_version,
                        "cpu_arch": original_vm.cpu_arch,
                        "remark": original_vm.remark,
                        "exists": current_vm and current_vm.status != 'deleted'
                    },
                    "host": {
                        "id": str(host.id),
                        "ip": host.ip,
                        "name": host.name
                    } if host else None,
                    "cluster": {
                        "id": str(cluster.id),
                        "name": cluster.name
                    } if cluster else None,
                    "pool": {
                        "id": str(pool.id),
                        "name": pool.name
                    } if pool else None,
                    "backup_volumes": backup_volumes,
                    "network_interfaces": network_interfaces,
                    "new_network_ports": new_network_ports if 'new_network_ports' in locals() else [],
                    "new_interfaces": new_interfaces if 'new_interfaces' in locals() else [],
                    "username": username,
                    "role": role,
                    "created_at": datetime.datetime.now().isoformat()
                }
                
                # 10. 调用异步还原任务
                host_ip = original_vm.host.ip if original_vm.host else ""
                queue_name = f"queue_{host_ip}" if host_ip else "default"
                
                task_result = backup_restore.apply_async(
                    args=[restore_data], 
                    queue=queue_name,
                    link=backup_restore_callback.s().set(queue=QUEUE_NAME),
                )
                
                return {
                    "msg": "ok",
                    "code": 200,
                    "data": {
                        "task_id": task_result.id,
                        "new_vm_id": new_vm_id,
                        "new_vm_name": new_vm_name,
                        "backup_name": backup.backup_name,
                        "host_ip": host_ip,
                        "message": "备份还原任务已启动"
                    }
                }
        
        except Exception as e:
            print(f"备份还原异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"msg": f"备份还原失败: {str(e)}", "code": 500}
