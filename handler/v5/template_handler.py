# -*- coding: utf-8 -*-
import uuid

import pyrestful.rest
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from db.model.hci.template import Template
from sqlalchemy import desc, asc
from api.libvirt.client import Client
from util.decorators import error_decorator
time_format = '%Y-%m-%d %H:%M:%S'


class TemplateHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')

    @post(_path="/v5/template/add", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_template_add(self, form):
        """
        添加模板
        """
        name = form.get("name", "")
        vcpu = form.get("vcpu")
        cpu_arch = form.get("cpu_arch")
        memory = form.get("memory")
        memory_unit = form.get("memory_unit")
        disk_path = form.get("disk_path")
        disk_name = form.get("disk_name")
        disk_type = form.get("disk_type")
        disk_type_code = form.get("disk_type_code", "local")
        network = form.get("network")

        with self.session_scope() as session:
            new_template = Template()
            new_template.name=name
            new_template.vcpu=vcpu
            new_template.cpu_arch=cpu_arch
            new_template.memory=memory
            new_template.memory_unit=memory_unit
            new_template.disk_path=disk_path
            new_template.disk_name=disk_name
            new_template.disk_type=disk_type
            new_template.disk_type_code=disk_type_code
            new_template.network=network

            session.add(new_template)
            session.commit()

        return {"code": 200, "msg": "ok"}

    @post(_path="/v5/template/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_template_list(self, form):
        """
        获取模板列表
        """
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)
        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "created_at")
        search_str = form.get("search_str", "")

        with self.session_scope() as session:
            query = session.query(Template)

            if search_str:
                query = query.filter(Template.name.like(f'%{search_str}%'))

            total_count = query.count()

            if order_by:
                if order_type == "desc":
                    query = query.order_by(desc(getattr(Template, order_by)))
                else:
                    query = query.order_by(asc(getattr(Template, order_by)))

            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            templates = query.all()

        data = [template.to_dict() for template in templates]
        return {
            "code": 200,
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": int(total_count),
            "data": data
        }

    @error_decorator("/v5/template/put", ob="本地模板", action="修改本地模板", desc="修改本地模板")
    @put(_path="/v5/template/put", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_template_pool_put(self, form):
        """
        编辑本地模板
        支持修改名称、CPU、内存、磁盘和网络等字段
        """
        id = form.get("id", "")
        vcpu = form.get("vcpu")
        cpu_arch = form.get("cpu_arch")
        memory = form.get("memory")
        memory_unit = form.get("memory_unit")
        disk_path = form.get("disk_path")
        disk_name = form.get("disk_name")
        disk_type = form.get("disk_type")
        disk_type_code = form.get("disk_type_code")
        network = form.get("network")

        with self.session_scope() as session:
            template = session.query(Template).filter(Template.id == id).first()
            if template:
                template.name = form.get("name")
                if vcpu is not None:
                    template.vcpu = vcpu
                if cpu_arch is not None:
                    template.cpu_arch = cpu_arch
                if memory is not None:
                    template.memory = memory
                if disk_path is not None:
                    template.disk_path = disk_path
                if disk_name is not None:
                    template.disk_name = disk_name
                if disk_type is not None:
                    template.disk_type = disk_type
                if disk_type_code is not None:
                    template.disk_type_code = disk_type_code
                if memory_unit is not None:
                    template.memory_unit = memory_unit
                if network is not None:
                    template.network = network
                session.commit()
            else:
                return {"code": 404, "msg": "模板不存在"}

        return {"code": 200, "msg": "ok"}

    @error_decorator(error_message="/v5/template/delete", ob="本地模板", action="删除本地模板",
                     desc="删除本地模板")
    @delete(_path="/v5/template/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_template_pool_delete(self, form):
        """
        删除本地模板
        """
        ids = form.get("ids", [])
        names = form.get("names", [])

        if not ids and not names:
            return {"code": 400, "msg": "未提供要删除的模板"}

        # 检查ids和names长度是否一致
        if len(ids) != len(names):
            return {"code": 400, "msg": "模板ID和名称数量不匹配"}

        with self.session_scope() as session:
            # 按ID批量删除
            deleted_ids = session.query(Template).filter(Template.id.in_(ids)).delete(synchronize_session=False)
            session.commit()

        # 日志记录
        for name in names:
            print(f"删除模板: {name}")

        return {
            "code": 200,
            "msg": f"成功删除 {deleted_ids} 个模板"
        }
