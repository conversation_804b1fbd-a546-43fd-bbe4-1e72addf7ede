# -*- coding: utf-8 -*-

from calendar import c
from unittest import result
from venv import create
import tornado.ioloop
import pyrestful.rest
from math import ceil
from sqlalchemy import asc, desc
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host
from db.model.hci.network import Switch, Router, RouterPort, RouterTable
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom

from api.log.log import CustomLogger
from celery import Celery, chord
from util.decorators import deprecated_api, role_required


import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class ClusterVmHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    
    #这个接口是从一个集群上创建虚机
    @deprecated_api()    
    @post(_path="/v5/vm/create/to/cluster", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_create(self, form):
        
        """_summary_ 从一个集群上创建虚机
        流程：
        1. 从 form 中获取虚机信息
        2. 从调度器中获取一个合适的主机
        3. 创建虚机空壳
        4. 由 celery 任务来创建并启动虚机
        
        """
        
        
        #查出host信息 创建虚机空壳 但是不启动 由celery任务来启动
        with self.session_scope() as session:
            domain = Domain()
            
        
        
        
        
        # 异步调用多任务集合点来完成
        # tasks = [create_network.s(vm_name, form),create_cdrom.s(vm_name, form), create_disk.s(vm_name, form)]
        # result = chord(tasks)(create_vm_callback.s())
        
        # 异步调用单个任务
        # result_vm = create_vm.apply_async((vm_name, form),link=create_vm_callback.s())

        # 获取任务结果 这里会阻塞
        #print(result_vm.get())        
        
        return {"msg": "已开始创建虚机", "code": 200}


    @post(_path="/v5/cluster/vm/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_vm_list(self, form):
        
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        cluster_filter = form.get('cluster_id', '')

        with self.session_scope() as session:
            query = session.query(Domain)
            
            # 搜索功能，根据指定字段进行搜索（这里假设搜索的字段为 name）
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%"))
            
            # 过滤 host
            if cluster_filter:
                query = query.filter(Domain.cluster_id == cluster_filter)

            # 排序功能
            if order_by:
                if order_type == 'asc':
                    query = query.order_by(asc(getattr(Domain, order_by)))
                else:
                    query = query.order_by(desc(getattr(Domain, order_by)))

            # 计算总记录数
            total_records = query.count()

            # 分页
            domains = query.limit(per_page).offset((page - 1) * per_page).all()

            # 将结果转换为字典列表
            domain_list = [Domain.to_dict(domain) for domain in domains]

            # 计算总页数
            total_pages = ceil(total_records / per_page)

            # 返回分页信息和数据
            return {
                "msg": "获取虚机列表成功",
                "code": 200,
                "total": total_records,
                "data": domain_list
            }
            
        return {"msg": "获取虚机列表失败", "code": 500}