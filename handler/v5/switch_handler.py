# -*- coding: utf-8 -*-

import tornado.ioloop
from sqlalchemy import func, update
import requests

import pyrestful.rest
from db.model.hci.user_resource_quota import UserClusterAssignment
from db.model.user import User

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from api.ovn.client import Client
from settings import QUEUE_NAME
from util.cov import todict
from db.model.vm import VmGroup, Vms, Volumes
from api.libvirt.client import Client as LClient
from api.ovs.client import Client as OvsClient
from sqlalchemy.orm import joinedload
from api.log.log import CustomLogger

import traceback

import logging
from db.model.hci.network import (
    Switch, SwitchPorts, Router, 
    RouterPort, RouterTable, SwitchPortGroups,
    HostSwitchMapping,
)
from db.model.hci.compute import Host, Cluster, Domain, DomainInterface
from app.tasks.network_tasks import (
    create_bridge_call_back,
    del_bridge_call_back,
    create_distributed_bridge_callback,
    del_distributed_bridge_callback,
    switch_unbind_physical_interface_callback,
    switch_bind_physical_interface_callback,
)

from app.agents.network_tasks import (
    create_bridge, 
    del_bridge,
    create_distributed_bridge,
    get_physical_interfaces_task,
    switch_bind_physical_interface,
    switch_unbind_physical_interface,
)
from celery import chord, group

from util.decorators import role_required

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class SwitchHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @role_required()
    @post(_path="/v5/host/switch/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_local_switch_add(self, form):
        role = self.get_cookie('username', "")

        switch_name = form.get("name", "")
        host_ip = form.get("host_ip", "")
        try:
            # TODO 创建本地虚拟交换机
            client = Client(host_ip)
            ovs_is_ok = client.ovs_create_local_switch(client, switch_name)
            if not ovs_is_ok:
                new_logger.log(
                    self.username, "虚拟交换机", "创建本地虚拟交换机", "失败", role,
                    "创建本地虚拟交换机: {},失败,OVS创建失败".format(switch_name)
                )
                return {"msg": "创建本地虚拟交换机失败", "code": 500}
            lclient = LClient(host_ip)
            libvirt_is_ok = lclient.create_libvirt_network(lclient, form)
            if not libvirt_is_ok:
                new_logger.log(
                    self.username, "虚拟交换机", "创建本地虚拟交换机", "失败", role,
                    "创建本地虚拟交换机: {},失败,Libvirt网络创建失败".format(switch_name)
                )
                return {"msg": "创建本地虚拟交换机失败", "code": 500}

            # 写入数据库
            with self.session_scope() as session:
                switch = Switch(
                    name=form.get('name'),
                    vswitch_type=form.get('tag'),  # 假设 tag 表示 vswitch_type
                    vlan_ids=form.get('vlan_id'),
                    status='active',  # 根据业务逻辑设置适当的状态
                )
                session.add(switch)
                session.flush()  # 提交以获取 switch 的 ID

                # 创建并保存 SwitchPorts 对象
                pnic_info = form.get('pnic_info', {})
                port_group = form.get('port_group', {})

                switch_port = SwitchPorts(
                    switchs_id=switch.id,  # 关联到上面创建的 switch
                    name=port_group.get('port_group_name'),
                    gate_way=pnic_info.get('gateway'),
                    ip=pnic_info.get('ip'),
                    ip_type=pnic_info.get('ip_type'),
                    netmask=pnic_info.get('netmask'),
                    pnics=pnic_info.get('nic'),
                    ports_num=port_group.get('trunk_port'),
                    status='active',  # 根据业务逻辑设置适当的状态
                )
                session.add(switch_port)

            new_logger.log(
                self.username, "虚拟交换机", "创建本地虚拟交换机", "成功", role,
                "创建本地虚拟交换机: {},成功".format(switch_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟交换机", "创建本地虚拟交换机", "失败", role,
                "创建本地虚拟交换机: {},失败: {}".format(switch_name, str(e))
            )
            traceback.print_exc()
            return {"msg": "创建本地虚拟交换机失败", "code": 500}

        return {"msg": "创建本地虚拟交换机成功", "code": 200}

    @role_required()
    @post(_path="/v5/host/nic/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_nic_list(self, form):
        role = self.get_cookie('username', "")
        host_ip = form.get("host_ip", "")

        self.redirect("http://%s:9178/nic/list" % host_ip, status=302)

    @role_required()
    @post(_path="/v5/nic/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_nic_list(self, form):
        """
        获取所有主机的物理网卡信息，按集群组织
        """
        role = self.get_cookie('username', "")
        host_ip = form.get("host_ip", "")
        host_id = form.get("host_id", "")
        cluster_id = form.get("cluster_id", "")
        
        with self.session_scope() as session:
            # 获取所有集群
            clusters = session.query(Cluster).all()
            result = []
            
            for cluster in clusters:
                if cluster_id and str(cluster.id) != cluster_id:
                        continue
                cluster_info = {
                    "cluster_id": str(cluster.id),
                    "cluster_name": cluster.name,
                    "type":"cluster",
                    "hosts": []
                }
                
                # 获取集群下的所有主机
                for host in cluster.hosts:
                    if host_id and str(host.id) != host_id:
                        continue
                    host_info = {
                        "host_id": str(host.id),
                        "host_name": host.name,
                        "host_ip": host.ip,
                        "type": "host",
                        "nics": []
                    }
                    
                    try:
                        # 使用 HTTP 请求获取网卡信息
                        url = f"http://{host.ip}:9178/nic/list"
                        response = requests.post(url, timeout=2)  # 2秒超时
                        
                        if response.status_code == 200:
                            data = response.json()
                            nics = []
                            if isinstance(data, list):
                                for nic in data:
                                    nic["type"] = "card"
                                    ipv4s = nic.get("ipv4s", [])
                                    if len(ipv4s) == 0:
                                        nics.append(nic)
                            else:
                                print(f"主机 {host.ip} 返回的网卡数据不是列表")
                            host_info["nics"] = nics
                           
                        else:
                            print(f"获取主机 {host.ip} 网卡信息失败: HTTP {response.status_code}")
                            
                    except requests.exceptions.Timeout:
                        print(f"获取主机 {host.ip} 网卡信息失败: 请求超时")
                    except requests.exceptions.RequestException as e:
                        print(f"获取主机 {host.ip} 网卡信息失败: {str(e)}")
                    except Exception as e:
                        print(f"获取主机 {host.ip} 网卡信息失败: {str(e)}")
                    
                    # 无论是否成功都添加主机信息
                    if len(host_info["nics"])  == 0:
                        continue
                        
                    cluster_info["hosts"].append(host_info)
                if len(cluster_info["hosts"]) == 0:
                    continue
                result.append(cluster_info)

        return {"code": 200, "msg": "ok", "data": result}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/host/switch/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_get_host_switch_list(self, form):
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        host_id = form.get('host_id', "")

        if not host_id:
            return {"code": 400, "msg": "主机ID不能为空", "data": []}

        with self.session_scope() as session:
            # sysadm 和 supadm 角色无权限限制，operator 角色需要权限检查
            if role == "operator":
                # 获取用户信息
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"code": 404, "msg": "用户不存在", "data": []}

                # 检查用户是否有权限访问该主机
                authorized_host = session.query(Host.id).join(
                    Cluster, Host.cluster_id == Cluster.id
                ).join(
                    UserClusterAssignment, Cluster.id == UserClusterAssignment.cluster_id
                ).filter(
                    UserClusterAssignment.user_id == user.id,
                    Host.id == host_id
                ).first()

                if not authorized_host:
                    return {"code": 403, "msg": "主机无权限", "data": []}

            elif role in ("sysadm", "supadm"):
                # sysadm 和 supadm 角色无权限限制，直接检查主机是否存在
                host_exists = session.query(Host.id).filter(Host.id == host_id).first()
                if not host_exists:
                    return {"code": 404, "msg": "主机不存在", "data": []}

            else:
                # 未知角色
                return {"code": 403, "msg": "权限不足", "data": []}

            # 获取主机下的交换机端口信息，使用一次查询获取所有需要的数据
            switch_ports = session.query(SwitchPorts).join(
                Switch, SwitchPorts.switchs_id == Switch.id
            ).filter(
                Switch.host_id == host_id
            ).options(
                joinedload(SwitchPorts.switch)
            ).all()

            # 数据转换
            results = self._format_switch_port_data(switch_ports)

            return {"code": 200, "msg": "ok", "total": len(results), "data": results}

    def _format_switch_port_data(self, switch_ports):
        """格式化交换机端口数据"""
        results = []
        for port in switch_ports:
            port_data = {
                "port_id": str(port.id),
                "port_name": port.name,
                "gateway": port.gate_way,
                "next_hop": port.next_hop,
                "ip": port.ip,
                "ip_type": port.ip_type,
                "netmask": port.netmask,
                "mode": port.mode,
                "pnics": port.pnics,
                "ports_num": port.ports_num,
                "status": port.status,
                "switch_id": str(port.switch.id),
                "switch_name": port.switch.name,
                "vswitch_type": port.switch.vswitch_type,
                "vlan_ids": port.switch.vlan_ids,
                "switch_status": port.switch.status,
            }

            # 安全地处理时间字段
            if port.created_at:
                port_data["created_at"] = port.created_at.strftime("%Y-%m-%d %H:%M:%S")
            if port.updated_at:
                port_data["updated_at"] = port.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            if port.switch.created_at:
                port_data["switch_created_at"] = port.switch.created_at.strftime("%Y-%m-%d %H:%M:%S")
            if port.switch.updated_at:
                port_data["switch_updated_at"] = port.switch.updated_at.strftime("%Y-%m-%d %H:%M:%S")

            results.append(port_data)

        return results

    def _get_user_authorized_host_ids(self, session, username):
        """获取用户有权限的主机ID列表"""
        user = session.query(User).filter(User.username == username).first()
        if not user:
            return None, None

        authorized_host_ids = session.query(Host.id).join(
            Cluster, Host.cluster_id == Cluster.id
        ).join(
            UserClusterAssignment, Cluster.id == UserClusterAssignment.cluster_id
        ).filter(
            UserClusterAssignment.user_id == user.id
        ).all()

        return user, [h[0] for h in authorized_host_ids]

    @role_required()
    @post(_path="/v5/local/switch/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_local_switch_delete(self, form):
        role = self.get_cookie('username', "")

        switch_id = form.get("id", "")
        switch_name = form.get("name", "")
        host_ip = form.get("host_ip", "")
        try:
            client = Client(host_ip)
            client.ovs_delete_local_switch(client, switch_name)

            with self.session_scope() as session:
                # 创建switch表
                switch = session.query(Switch).filter(Switch.id == switch_id).delete()

            new_logger.log(
                self.username, "虚拟交换机", "删除本地虚拟交换机", "成功", role,
                "删除本地虚拟交换机: {},成功".format(switch_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟交换机", "删除本地虚拟交换机", "失败", role,
                "删除本地虚拟交换机: {},失败: {}".format(switch_name, str(e))
            )
            traceback.print_exc()
            return {"msg": "删除本地虚拟交换机失败", "code": 500}

        return {"msg": "删除本地虚拟交换机成功", "code": 200}

    # ================================分布式交换机端口============================================
    @role_required()
    @post(_path="/v5/distributed/switch/add", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_distributed_switch_add(self, form):
        role = self.get_cookie('username', "")
        username = self.get_cookie('role', "")

        # 参数处理
        switch_name = form.get("name", "")
        net_type = form.get("net_type", "")  # 网络类型
        net_data = form.get("net_data", [])  # 网络配置数据
        port_group_name = form.get("port_group_name", "")  # 端口组名称
        vlan_id = form.get("vlan_id", "")  # VLAN ID
        switch_type = "distributed"  # 交换机类型：distributed

        try:
            # 写入数据库
            with self.session_scope() as session:
                # 获取所有涉及的集群ID
                cluster_ids = list(set([data["cluster_id"] for data in net_data]))
                
                # 创建交换机记录
                switch = Switch()
                switch.name = switch_name
                switch.ovs_name = switch_name  # OVS 网桥名与交换机名相同
                switch.vswitch_type = switch_type
                switch.status = "building"
                session.add(switch)
                session.flush()  # 刷新会话以获取switch.id
    
                switch_id = switch.id

                # 如果提供了端口组名称，创建端口组
                if port_group_name:
                    # 检查 vlan_id 是否已存在
                    existing_port_group = session.query(SwitchPortGroups).filter(
                        SwitchPortGroups.vlan_id == vlan_id
                    ).first()
                    
                    if existing_port_group:
                        raise Exception(f"VLAN ID {vlan_id} 已被使用，不能重复")
                        
                    port_group = SwitchPortGroups(
                        switchs_id=switch_id,
                        name=port_group_name,
                        vlan_id=vlan_id,
                    )
                    session.add(port_group)
                    session.flush()

                # 为每个主机创建映射关系
                for host_data in net_data:
                    host_id = host_data.get("host_id")
                    if not host_id:
                        raise Exception(f"Missing host_id in net_data for host {host_data.get('host_ip')}")
                    
                    # 创建主机与交换机的映射
                    host_switch_mapping = HostSwitchMapping(
                        host_id=host_id,
                        switch_id=switch_id,
                    )
                    session.add(host_switch_mapping)
                
                session.flush()

                # 准备任务列表
                tasks = []
                # 为每个主机创建任务
                for host_data in net_data:
                    current_host_ip = host_data["host_ip"]
                    # 获取其他所有主机的IP作为远程IP
                    remote_ips = [h["host_ip"] for h in net_data if h["host_ip"] != current_host_ip]
                    
                    # 准备网络接口信息
                    net_interfaces = [net["name"] for net in host_data.get("netName", [])]
                    
                    # 为每个远程IP创建一个任务
                    for remote_ip in remote_ips:
                        form = {
                            "switch_name": switch_name,
                            "host_ip": current_host_ip,
                            "remote_ip": remote_ip,
                            "net_interfaces": net_interfaces,
                            "switch_id": switch_id,
                            "link_type": host_data.get("link", ""),
                            "mask": host_data.get("mask", ""),
                            "vlan_id": vlan_id,
                            # 添加日志所需的用户信息
                            "username": self.username,
                            "role": role
                        }
                        
                        # 为每个主机创建队列任务
                        queue = "queue_" + current_host_ip
                        tasks.append(create_distributed_bridge.s(form).set(queue=queue))

                # 使用chord来确保所有任务完成后执行回调
                callback = create_distributed_bridge_callback.s(switch_id).set(queue=QUEUE_NAME)
                header = group(tasks)
                result = chord(header)(callback)
                           
            new_logger.log(
                self.username, "虚拟交换机", "创建虚拟交换机", "成功", role,
                "创建分布式虚拟交换机: {},成功".format(switch_name)
            )
            return {"msg": "ok","data":"创建虚拟交换机成功", "code": 200}
            
        except Exception as e:
            self.set_status(502)
            print("创建分布式交换机报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "创建虚拟交换机", "失败", role,
                "创建分布式虚拟交换机: {},失败: {}".format(switch_name, str(e))
            )
            traceback.print_exc()
            return {"msg": f"创建虚拟交换机失败: {str(e)}", "code": 200}

    @role_required()
    @post(_path="/v5/distributed/switch/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_distributed_switch_list(self, form):
        page = int(form.get('page', '1'))
        page_size = int(form.get('pagecount', '10'))
        order_by = form.get("order_by", "desc")
        order_type = form.get("order_type", "")
        search_str = form.get("search_str", "")
        cluster_id = form.get('cluster_id', "")

        try:
            with self.session_scope() as session:
                query = session.query(Switch).filter(Switch.vswitch_type != "local")\
                    .options(
                        joinedload(Switch.switch_port_groups),
                        joinedload(Switch.host_mappings).joinedload(HostSwitchMapping.host)
                    )
                
                if cluster_id != "":
                    # 1. 先获取集群下的所有主机ID
                    host_ids = session.query(Host.id).filter(Host.cluster_id == cluster_id).all()
                    host_ids = [h[0] for h in host_ids]
                    
                    if host_ids:
                        # 2. 通过映射表查询这些主机相关的交换机
                        query = query.join(
                            HostSwitchMapping,
                            Switch.id == HostSwitchMapping.switch_id
                        ).filter(HostSwitchMapping.host_id.in_(host_ids))

                if order_by and order_type:
                    if order_type.lower() == 'asc':
                        query = query.order_by(getattr(Switch, order_by).asc())
                    elif order_type.lower() == 'desc':
                        query = query.order_by(getattr(Switch, order_by).desc())

                if search_str:
                    query = query.filter(
                        Switch.name.like(f"%{search_str}%") |
                        Switch.vswitch_type.like(f"%{search_str}%") |
                        Switch.vlan_ids.like(f"%{search_str}%") |
                        Switch.status.like(f"%{search_str}%")
                    )

                total_count_query = query.with_labels().statement.with_only_columns([func.count()]).order_by(None)
                total_count = session.execute(total_count_query).scalar()
                switches = query.offset((page - 1) * page_size).limit(page_size).all()

                switch_list = []
                for switch in switches:
                    switch_data = switch.to_dict_merge()
                    # 添加关联的主机信息
                    hosts_info = []
                    for mapping in switch.host_mappings:
                        if mapping.host:
                            host_info = {
                                "host_id": str(mapping.host.id),
                                "host_name": mapping.host.name,
                                "host_ip": mapping.host.ip,
                                "status": mapping.host.status if hasattr(mapping.host, 'status') else None,
                                "mapping_id": str(mapping.id),
                                "created_at": mapping.created_at.strftime("%Y-%m-%d %H:%M:%S") if mapping.created_at else None
                            }
                            hosts_info.append(host_info)
                    switch_data["hosts"] = hosts_info
                    switch_data["id"] = str(switch.id)  # 将交换机 ID 转为字符串
                    switch_list.append(switch_data)

            return {
                "msg": "查询虚拟交换机成功",
                "code": 200,
                "data": switch_list,
                "total": total_count,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            self.set_status(502)
            print("查询分布式交换机报错：", e)
            traceback.print_exc()
            return {"msg": f"查询虚拟交换机失败: {str(e)}", "code": 500}

    # ================================交换机通用接口==============================================

    @role_required()
    @post(_path="/v5/switch/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_switch_add(self, form):
        role = self.get_cookie('username', "")
        username = self.get_cookie('role', "")

        # 参数处理
        mtu = form.get("mtu", "1500")  # 网桥的mtu设置， 默认 1500 单位：字节
        switch_name = form.get("name", "")
        status = form.get("status", "building")
        vlan_id = form.get("vlan_id", "")
        switch_port_group_name = form.get("port_group_name")
        host_id = form.get("host_id", "")
        cluster_id = form.get("cluster_id", "")
        card = form.get("card", "")

        try:
            # 写入数据库
            with self.session_scope() as session:
                # 1. 创建交换机记录
                switch = Switch()
                switch.name = switch_name
                switch.ovs_name = switch_name  # 使用相同的名称作为 OVS 网桥名
                switch.mtu = mtu
                switch.status = status
                # switch.vlan_ids = vlan_id

                if host_id != "":
                    # 本地交换机
                    switch.vswitch_type = "local"
                    # 查询主机信息
                    host = session.query(Host).filter(Host.id == host_id).first()
                    if host is None:
                        return {"msg": f"创建失败，未找到主机：{host_id}", "code": 400}
                    
                    # 设置集群ID（因为主机属于集群）
                    # switch.cluster_id = host.cluster_id
                    session.add(switch)
                    session.flush()  # 获取 switch.id
                    
                    # 2. 创建主机-交换机映射
                    host_mapping = HostSwitchMapping(
                        host_id=host_id,
                        switch_id=switch.id,
                        remark=f"Local switch for host {host.name}"
                    )
                    session.add(host_mapping)
                    
                elif cluster_id != "":
                    # 分布式交换机暂不处理映射关系
                    switch.vswitch_type = "distributed"
                    switch.cluster_id = cluster_id
                    # 检查集群是否存在
                    cluster = session.query(Cluster).filter(Cluster.id == cluster_id).first()
                    if cluster is None:
                        return {"msg": f"创建失败，未找到集群：{cluster_id}", "code": 400}
                    session.add(switch)
                    session.flush()

                # 3. 创建端口组（如果需要）
                if switch_port_group_name:
                     # 检查 vlan_id 是否已存在
                    existing_port_group = session.query(SwitchPortGroups).filter(
                        SwitchPortGroups.vlan_id == vlan_id
                    ).first()
                    
                    if existing_port_group:
                        raise Exception(f"VLAN ID {vlan_id} 已被使用，不能重复")
                    port_group = SwitchPortGroups(
                        switchs_id=switch.id,
                        name=switch_port_group_name,
                        vlan_id=vlan_id
                    )
                    session.add(port_group)
                
                # 4. 提交事务以保存所有更改
                session.flush()
                
                # 5. 创建异步任务
                if host_id != "":  # 只有本地交换机需要创建异步任务
                    task_form = {
                        "switch_id": str(switch.id),
                        "host_ip": host.ip,
                        "name": switch_name,  # OVS 网桥名称
                        "mapping_id": str(host_mapping.id),  # 添加映射ID
                        "card": card,
                        # 添加日志所需的用户信息
                        "username": self.username,
                        "role": role
                    }
                    queue = "queue_" + host.ip
                    result = create_bridge.apply_async(
                        args=[task_form],
                        queue=queue,
                        link=create_bridge_call_back.s().set(queue=QUEUE_NAME)
                    )

            new_logger.log(
                self.username, "虚拟交换机", "创建虚拟交换机", "成功", role,
                f"创建{'本地' if host_id else '分布式'}虚拟交换机: {switch_name}"
            )
            return {"msg": "ok", "code": 200, "data":"创建虚拟交换机成功"}

        except Exception as e:
            print(e)
            self.set_status(502)
            new_logger.log(
                self.username, "虚拟交换机", "创建虚拟交换机", "失败", role,
                f"创建{'本地' if host_id else '分布式'}虚拟交换机: {switch_name}"
            )
            traceback.print_exc()
            return {"msg": f"创建虚拟交换机任务失败: {str(e)}", "code": 200}

    @role_required()
    @delete(_path="/v5/switch/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_switch(self, form):
        role = self.get_cookie('username', "")
        username = self.get_cookie('role', "")
        ids = form.get("ids", [])
        names = form.get("names", [])
        if len(ids) == 0:
            return {"msg": "删除成功", "code": 200}

        try:
            with self.session_scope() as session:
                # 1. 批量更新状态为 'deleting'
                update_stmt = (
                    update(Switch).
                    where(Switch.id.in_(ids)).
                    values(status='deleting')
                )
                result = session.execute(update_stmt)
                session.commit()

                # 2. 处理每个交换机的删除
                for switch_id in ids:
                    # 加载交换机信息，包括端口组、端口和主机映射
                    switch = session.query(Switch).options(
                        joinedload(Switch.switch_port_groups).joinedload(SwitchPortGroups.switch_ports),
                        joinedload(Switch.host_mappings).joinedload(HostSwitchMapping.host)
                    ).filter(Switch.id == switch_id).first()
                    
                    if not switch:
                        print(f"Switch {switch_id} not found")
                        continue
                    
                    # 检查端口组下是否有端口
                    can_delete_switch = True
                    for port_group in switch.switch_port_groups:
                        if len(port_group.switch_ports) > 0:
                            print(f"端口组 {port_group.name} 下存在端口，无法删除交换机{switch.name}")
                            can_delete_switch = False
                            break
                    
                    if not can_delete_switch:
                        continue
                    
                    # 获取主机信息（从映射表）
                    if switch.vswitch_type == "local":
                        # 本地交换机只有一个主机映射
                        host_mapping = next((mapping for mapping in switch.host_mappings), None)
                        if not host_mapping or not host_mapping.host:
                            print(f"未找到交换机 {switch.name} 的主机映射信息")
                            continue
                            
                        host = host_mapping.host
                        queue = "queue_" + host.ip
                        form = {
                            "name": switch.ovs_name,
                            "switch_id": str(switch.id),
                            "mapping_id": str(host_mapping.id),  # 添加映射ID用于后续清理
                            "host_ip": host.ip,
                            # 添加日志所需的用户信息
                            "username": self.username,
                            "role": role
                        }
                        result = del_bridge.apply_async(
                            args=[form],
                            queue=queue,
                            link=del_bridge_call_back.s().set(queue=QUEUE_NAME)
                        )
                        print(f"已创建删除任务：交换机 {switch.name}，主机 {host.ip}")
                    
                    elif switch.vswitch_type == "distributed":
                        # 分布式交换机的删除逻辑保持不变
                        print(f"分布式交换机 {switch.name} 的删除请使用分布式交换机删除接口")
                        continue

            new_logger.log(
                self.username, "虚拟交换机", "删除虚拟交换机", "成功", role,
                "删除虚拟交换机: {},开始删除".format(",".join(names))
            )
            return {
                "msg": "开始删除虚拟交换机, 端口组下存在端口时删除不会完成",
                "code": 200
            }

        except Exception as e:
            self.set_status(502)
            print("删除交换机报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "删除虚拟交换机", "失败", role,
                "删除虚拟交换机: {},失败: {}".format(",".join(names), str(e))
            )
            traceback.print_exc()
            return {
                "msg": f"删除虚拟交换机失败: {str(e)}",
                "code": 500
            }

    @role_required()
    @put(_path="/v5/switch/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_put_switch_update(self, form):
        role = self.get_cookie('username', "")
        username = self.get_cookie('role', "")
        switch_id = form.get("id", "")
        if not switch_id:
            new_logger.log(
                self.username, "虚拟交换机", "修改虚拟交换机", "失败", role,
                "修改虚拟交换机: {},失败,交换机ID缺失".format(switch_id)
            )
            return {"msg": "交换机ID缺失，无法修改", "code": 400}

        switch_name = form.get("name", "")
        switch_type = form.get("type", "")
        mtu = form.get("mtu", "")
        status = form.get("status", "")

        try:
            with self.session_scope() as session:
                # 检查交换机是否存在
                switch = session.query(Switch).filter(Switch.id == switch_id).first()
                if not switch:
                    new_logger.log(
                        self.username, "虚拟交换机", "修改虚拟交换机", "失败", role,
                        "修改虚拟交换机: {},失败,交换机不存在".format(switch_id)
                    )
                    return {"msg": "交换机不存在", "code": 404}

                old_name = switch.name
                # 构建更新字典
                update_data = {}
                if switch_name != "" and switch_name is not None:
                    update_data[Switch.name] = switch_name

                if mtu != "" and mtu is not None:
                    update_data[Switch.mtu] = mtu

                if status != "" and status is not None:
                    update_data[Switch.status] = status

                # 如果有数据需要更新，则执行更新
                if update_data:
                    session.query(Switch).filter(Switch.id == switch_id).update(update_data)

            new_logger.log(
                self.username, "虚拟交换机", "修改虚拟交换机", "成功", role,
                "修改虚拟交换机: {} -> {},成功".format(old_name, switch_name if switch_name else old_name)
            )
        except Exception as e:
            self.set_status(502)
            print("修改交换机报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "修改虚拟交换机", "失败", role,
                "修改虚拟交换机: {},失败: {}".format(switch_id, str(e))
            )
            traceback.print_exc()
            return {"msg": "修改虚拟交换机失败", "code": 500}

        return {"msg": "修改虚拟交换机成功", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/switch/list", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_switch_list(self, form):
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        page = int(form.get('page', '1'))
        page_size = int(form.get('pagecount', '10'))
        order_by = form.get("order_by", "desc")
        order_type = form.get("order_type", "")
        search_str = form.get("search_str", "")
        cluster_id = form.get('cluster_id', "")
        host_id = form.get('host_id', "")

        try:
            with self.session_scope() as session:
                # 如果是 operator 角色，需要进行权限过滤
                authorized_host_ids = None
                if role == "operator":
                    user, authorized_host_ids = self._get_user_authorized_host_ids(session, username)
                    if not user:
                        return {"code": 404, "msg": "用户不存在", "data": [], "total": 0}

                    # 如果用户没有任何权限，直接返回空结果
                    if not authorized_host_ids:
                        return {
                            "msg": "查询虚拟交换机成功",
                            "code": 200,
                            "data": [],
                            "total": 0,
                            "page": page,
                            "page_size": page_size
                        }

                # 基础查询，预加载关系表
                query = session.query(Switch).options(
                    joinedload(Switch.switch_port_groups),
                    joinedload(Switch.host_mappings).joinedload(HostSwitchMapping.host)
                )

                # 处理过滤条件
                if host_id:
                    # 如果是 operator 角色，检查是否有权限访问该主机
                    if role == "operator" and host_id not in authorized_host_ids:
                        return {
                            "msg": "查询虚拟交换机成功",
                            "code": 200,
                            "data": [],
                            "total": 0,
                            "page": page,
                            "page_size": page_size
                        }

                    # 直接通过映射表查询指定主机的交换机
                    query = query.join(
                        HostSwitchMapping,
                        Switch.id == HostSwitchMapping.switch_id
                    ).filter(HostSwitchMapping.host_id == host_id)

                elif cluster_id:
                    # 1. 先获取集群下的所有主机ID
                    cluster_host_ids = session.query(Host.id).filter(Host.cluster_id == cluster_id).all()
                    cluster_host_ids = [h[0] for h in cluster_host_ids]

                    # 如果是 operator 角色，只保留有权限的主机
                    if role == "operator":
                        cluster_host_ids = [hid for hid in cluster_host_ids if hid in authorized_host_ids]

                    if cluster_host_ids:
                        # 2. 通过映射表查询这些主机相关的交换机
                        query = query.join(
                            HostSwitchMapping,
                            Switch.id == HostSwitchMapping.switch_id
                        ).filter(HostSwitchMapping.host_id.in_(cluster_host_ids))
                    else:
                        # 没有权限的主机，返回空结果
                        return {
                            "msg": "查询虚拟交换机成功",
                            "code": 200,
                            "data": [],
                            "total": 0,
                            "page": page,
                            "page_size": page_size
                        }

                else:
                    # 如果没有指定 host_id 或 cluster_id，但是是 operator 角色，需要限制只能看到有权限的交换机
                    if role == "operator":
                        query = query.join(
                            HostSwitchMapping,
                            Switch.id == HostSwitchMapping.switch_id
                        ).filter(HostSwitchMapping.host_id.in_(authorized_host_ids))

                # 添加排序条件
                if order_by and order_type:
                    if order_type.lower() == 'asc':
                        query = query.order_by(getattr(Switch, order_by).asc())
                    elif order_type.lower() == 'desc':
                        query = query.order_by(getattr(Switch, order_by).desc())

                # 添加搜索条件
                if search_str:
                    query = query.filter(
                        Switch.name.like(f"%{search_str}%") |
                        Switch.vswitch_type.like(f"%{search_str}%") |
                        Switch.vlan_ids.like(f"%{search_str}%") |
                        Switch.status.like(f"%{search_str}%")
                    )

                # 去重，因为可能通过关联查询产生重复记录
                query = query.distinct()

                # 获取总数
                total_count_query = query.with_labels().statement.with_only_columns([func.count()]).order_by(None)
                total_count = session.execute(total_count_query).scalar()

                # 分页查询
                switches = query.offset((page - 1) * page_size).limit(page_size).all()

                # 处理返回数据
                switch_list = []
                for switch in switches:
                    switch_data = switch.to_dict_merge()
                    # 添加关联的主机信息
                    hosts_info = []
                    for mapping in switch.host_mappings:
                        if mapping.host:
                            hosts_info.append({
                                "host_id": str(mapping.host.id),
                                "host_name": mapping.host.name,
                                "host_ip": mapping.host.ip,
                                "mapping_id": str(mapping.id),
                                "created_at": mapping.created_at.strftime("%Y-%m-%d %H:%M:%S") if mapping.created_at else None
                            })
                    switch_data["hosts"] = hosts_info
                    switch_data["id"] = str(switch.id)
                    switch_list.append(switch_data)

            return {
                "msg": "查询虚拟交换机成功",
                "code": 200,
                "data": switch_list,
                "total": total_count,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            self.set_status(502)
            print("查询交换机报错：", e)
            traceback.print_exc()
            return {"msg": f"查询虚拟交换机失败: {str(e)}", "code": 500}

    @role_required()
    @delete(_path="/v5/distributed/switch/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_distributed_switch(self, form):
        """删除分布式交换机"""
        role = self.get_cookie('username', "")
        ids = form.get("ids", [])
        names = form.get("names", [])
        
        if len(ids) == 0:
            return {"msg": "删除成功", "code": 200}

        try:
            with self.session_scope() as session:
                # 获取要删除的交换机信息，同时预加载主机映射关系
                switches = session.query(Switch)\
                    .options(joinedload(Switch.host_mappings).joinedload(HostSwitchMapping.host))\
                    .filter(Switch.id.in_(ids))\
                    .all()
                
                for switch in switches:
                    if switch.vswitch_type != "distributed":
                        continue
                    
                    # 更新交换机状态为删除中
                    switch.status = 'deleting'
                    session.add(switch)
                    
                    # 获取所有关联的主机信息
                    tasks = []
                    processed_hosts = set()  # 用于记录已处理的主机，避免重复
                    
                    for mapping in switch.host_mappings:
                        host = mapping.host
                        if not host or host.ip in processed_hosts:
                            continue
                            
                        processed_hosts.add(host.ip)
                        form = {
                            "switch_name": switch.ovs_name,
                            "switch_id": str(switch.id),
                            "host_ip": host.ip,
                            "mapping_id": str(mapping.id),  # 添加映射ID，用于后续清理
                            # 添加日志所需的用户信息
                            "username": self.username,
                            "role": role
                        }
                        queue = "queue_" + host.ip
                        tasks.append(del_bridge.s(form).set(queue=queue))
                
                    if tasks:  # 只有当有任务时才创建 chord
                        # 使用 chord 确保所有删除任务完成后执行回调
                        # 为分布式交换机使用新的回调函数，并传入 switch.id
                        callback = del_distributed_bridge_callback.s(str(switch.id)).set(queue=QUEUE_NAME)
                        header = group(tasks)
                        result = chord(header)(callback)
                        print(f"已启动删除分布式交换机任务，switch_id: {switch.id}")
                    else:
                        # 如果没有关联的主机，直接删除交换机记录
                        session.delete(switch)

                session.commit()
                
            new_logger.log(
                self.username, "虚拟交换机", "删除分布式交换机", "成功", role,
                "删除分布式交换机: {}".format(",".join(names))
            )
            return {"msg": "开始删除分布式交换机", "code": 200}
            
        except Exception as e:
            self.set_status(502)
            print("删除分布式交换机报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "删除分布式交换机", "失败", role,
                "删除分布式交换机: {}".format(",".join(names))
            )
            traceback.print_exc()
            return {"msg": f"删除分布式交换机失败: {str(e)}", "code": 500}

    @role_required()
    @put(_path="/v5/distributed/switch/update", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_put_distributed_switch_update(self, form):
        """更新分布式交换机名称"""
        role = self.get_cookie('username', "")
        switch_id = form.get("id", "")
        new_name = form.get("name", "")

        if not switch_id or not new_name:
            new_logger.log(
                self.username, "虚拟交换机", "更新分布式交换机", "失败", role,
                "更新分布式交换机: {},失败,交换机ID或新名称缺失".format(switch_id)
            )
            return {"msg": "交换机ID或新名称缺失", "code": 400}

        try:
            with self.session_scope() as session:
                # 检查交换机是否存在且是分布式类型
                switch = session.query(Switch).filter(
                    Switch.id == switch_id,
                    Switch.vswitch_type == "distributed"
                ).first()
                
                if not switch:
                    new_logger.log(
                        self.username, "虚拟交换机", "更新分布式交换机", "失败", role,
                        "更新分布式交换机: {},失败,交换机不存在或不是分布式交换机".format(switch_id)
                    )
                    return {"msg": "交换机不存在或不是分布式交换机", "code": 404}

                old_name = switch.name

                # 检查新名称是否已存在
                exists = session.query(Switch).filter(
                    Switch.name == new_name,
                    Switch.id != switch_id
                ).first()

                if exists:
                    new_logger.log(
                        self.username, "虚拟交换机", "更新分布式交换机", "失败", role,
                        "更新分布式交换机: {} -> {},失败,交换机名称已存在".format(old_name, new_name)
                    )
                    return {"msg": "交换机名称已存在", "code": 400}

                # 更新交换机名称
                switch.name = new_name
                session.add(switch)
                session.commit()

            new_logger.log(
                self.username, "虚拟交换机", "更新分布式交换机", "成功", role,
                "更新分布式交换机: {} -> {},成功".format(old_name, new_name)
            )
        except Exception as e:
            self.set_status(502)
            print("更新分布式交换机报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "更新分布式交换机", "失败", role,
                "更新分布式交换机: {},失败: {}".format(switch_id, str(e))
            )
            traceback.print_exc()
            return {"msg": "更新分布式交换机失败", "code": 500}

        return {"msg": "更新分布式交换机成功", "code": 200}


    @role_required()
    @post(_path="/v5/switch/bind/nic", _consumes=mediatypes.APPLICATION_JSON,)
    def hci_post_switch_bind_nic(self, form):
        """
        绑定网卡到交换机端口
        """
        role = self.get_cookie('username', "")
        switch_id = form.get("switch_id", "")
        switch_port_group_id = form.get("switch_port_group_id", "00000000-0000-0000-0000-000000000000")
        nic_name = form.get("nic_name", "")
        host_id = form.get("host_id", "")

        if not switch_id or not nic_name:
            return {"msg": "缺少必要参数", "code": 200}

        try:
            with self.session_scope() as session:
                # 主机信息
                host_info = session.query(Host).filter(Host.id == host_id).first()
                # 检查交换机和端口是否存在
                switch = session.query(Switch).filter(Switch.id == switch_id).first()
                if not switch:
                    return {"msg": "交换机不存在", "code": 200}

                # existing_port = session.query(SwitchPorts).join(Switch).join(HostSwitchMapping) \
                # .filter(
                #     SwitchPorts.is_physical == 1,
                #     SwitchPorts.name == nic_name,
                #     Switch.id == switch_id,
                #     HostSwitchMapping.switch_id == switch_id,
                #     HostSwitchMapping.host_id == host_id
                # ).first()

                # if existing_port:
                #     return {"msg": "该网卡已被绑定到主机：{} 的网桥：{} 上".format(host_info.name, existing_port.switch.name), "code": 200}

                task_form = {
                    "switch_name": switch.ovs_name,
                    "host_ip": host_info.ip,
                    "nic_name": nic_name,
                    "switch_id": switch_id,
                    "switch_port_group_id": switch_port_group_id,  # 新增：端口组ID
                    # 添加日志所需的用户信息
                    "username": self.username,
                    "role": role
                }
                queue = "queue_" + host_info.ip
                result = switch_bind_physical_interface.apply_async(
                    args=[task_form],
                    queue=queue,
                    link=switch_bind_physical_interface_callback.s().set(queue=QUEUE_NAME)
                )
                # client = OvsClient(host_info.ip)
                # client.bind_physical_interface(client, switch.ovs_name, nic_name)

                # port = SwitchPorts(
                #     switchs_id=switch_id,
                #     name=nic_name,
                #     is_physical=1,
                #     switch_port_group_id=switch_port_group_id if switch_port_group_id else None  # 新增：端口组ID
                # )
                # session.add(port)
                # session.commit()

            new_logger.log(
                self.username, "虚拟交换机", "绑定网卡到交换机端口", "成功", role,
                f"绑定网卡 {nic_name} 到网桥 {switch.name}上"
            )
            return {"msg": "ok", "code": 200, "data": "开始绑定网卡"}
            
        except Exception as e:
            self.set_status(502)
            print("绑定网卡到交换机端口报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "绑定网卡到交换机端口", "失败", role,
                f"绑定网卡 {nic_name} 到网桥 {switch.name} 失败: {str(e)}"
            )
            return {"msg": "绑定网卡失败", "code": 200}

    @role_required()
    @post(_path="/v5/switch/unbind/nic", _consumes=mediatypes.APPLICATION_JSON,)
    def hci_post_switch_unbind_nic(self, form):
        """
        解绑网卡从交换机端口
        """
        role = self.get_cookie('username', "")
        switch_id = form.get("switch_id", "")
        nic_name = form.get("nic_name", "")
        host_id = form.get("host_id", "")

        if not switch_id or not nic_name or not host_id:
            return {"msg": "缺少必要参数（switch_id, nic_name, host_id）", "code": 200}

        try:
            with self.session_scope() as session:
                # 1. 检查交换机是否存在
                switch = session.query(Switch).filter(Switch.id == switch_id).first()
                if not switch:
                    return {"msg": "交换机不存在", "code": 200}

                # 2. 检查网卡是否已绑定到该主机的交换机
                host_info = session.query(Host).filter(Host.id == host_id).first()
                existing_port = session.query(SwitchPorts).join(Switch).join(HostSwitchMapping) \
                    .filter(
                        SwitchPorts.is_physical == 1,
                        SwitchPorts.name == nic_name,
                        Switch.id == switch_id,
                        HostSwitchMapping.switch_id == switch_id,
                        HostSwitchMapping.host_id == host_id
                    ).first()

                if not existing_port:
                    return {"msg": f"网卡 {nic_name} 未绑定到主机 {host_info.name} 的交换机 {switch.name}", "code": 200}


                task_form = {
                    "switch_name": switch.ovs_name,
                    "host_ip": host_info.ip,
                    "nic_name": nic_name,
                    "switch_id": switch_id,
                    # 添加日志所需的用户信息
                    "username": self.username,
                    "role": role
                }
                
                
                queue = "queue_" + host_info.ip
                result = switch_unbind_physical_interface.apply_async(
                    args=[task_form],
                    queue=queue,
                    link=switch_unbind_physical_interface_callback.s().set(queue=QUEUE_NAME)
                )
                # client = OvsClient(host_info.ip)
                # client.unbind_physical_interface(client, switch.ovs_name, nic_name)
                # # 3. 执行解绑操作（删除 SwitchPorts 记录）
                # session.delete(existing_port)
                # session.commit()

                # 4. 记录日志
                new_logger.log(
                    self.username, "虚拟交换机", "解绑网卡从交换机端口", "成功", role,
                    f"解绑网卡 {nic_name} 从交换机 {switch.name}（主机ID: {host_id}）"
                )
                return {"msg": "ok", "code": 200, "data": "开始解绑网卡"}
                
        except Exception as e:
            self.set_status(502)
            print("解绑网卡从交换机端口报错：", e)
            new_logger.log(
                self.username, "虚拟交换机", "解绑网卡从交换机端口", "失败", role,
                f"解绑网卡 {nic_name} 从交换机 {switch.name if 'switch' in locals() else '未知'}（主机ID: {host_id}）失败: {str(e)}"
            )
            return {"msg": "解绑网卡失败", "code": 200}


    @role_required()
    @post(_path="/v5/switch/vms", _consumes=mediatypes.APPLICATION_JSON,)
    def hci_post_switch_bind_vms(self, form):
        username = self.get_cookie('username', "")
        rolename = self.get_cookie('role_name')
        switch_id = form.get("switch_id")
        if not switch_id:
            return {"msg": "缺少网桥ID", "code": 200}

        # 分页、排序、搜索参数
        page = int(form.get('page', 1))
        pagecount = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'asc')
        order_by = form.get('order_by', 'id')
        search_str = form.get('search_str', '').strip()

        try:
            with self.session_scope() as session:
                # 1. 查询所有端口
                ports = session.query(SwitchPorts).filter(SwitchPorts.switchs_id == switch_id).all()
                vms_result = []
                for port in ports:
                    # 2. 查询端口组
                    port_group = port.switch_port_group
                    port_group_info = None
                    port_group_name = ''
                    if port_group:
                        port_group_info = {
                            "id": str(port_group.id),
                            "name": port_group.name,
                            "vlan_id": port_group.vlan_id
                        }
                        port_group_name = port_group.name or ''
                    # 3. 查询与端口绑定的虚拟机（DomainInterface）
                    domain_interfaces = getattr(port, "domain_interfaces", [])
                    for di in domain_interfaces:
                        # 4. 查询虚拟机信息
                        domain = session.query(Domain).filter(Domain.id == di.domain_id).first()
                        if not domain:
                            continue
                        vm_info = {
                            "id": str(domain.id),
                            "name": domain.name,
                            "status": domain.status,
                            # ... 其它虚拟机字段 ...
                            "port": {
                                "id": str(port.id),
                                "name": port.name,
                                "mac": di.mac,
                                "ip": di.ip,
                                # ... 其它端口字段 ...
                                "port_group": port_group_info
                            },
                            "_sort_vm_name": domain.name or '',
                            "_sort_port_group_name": port_group_name
                        }
                        vms_result.append(vm_info)

                # 4. 搜索过滤（虚拟机名、端口组名）
                if search_str:
                    vms_result = [item for item in vms_result if search_str in item["name"] or (item["port"]["port_group"] and search_str in item["port"]["port_group"]["name"])]

                # 5. 排序
                if order_by == 'port_group_name':
                    vms_result.sort(key=lambda x: x['_sort_port_group_name'], reverse=(order_type=='desc'))
                elif order_by == 'vm_name' or order_by == 'name':
                    vms_result.sort(key=lambda x: x['_sort_vm_name'], reverse=(order_type=='desc'))
                else:
                    vms_result.sort(key=lambda x: x.get(order_by, ''), reverse=(order_type=='desc'))

                # 6. 总数
                total = len(vms_result)

                # 7. 分页
                start = (page - 1) * pagecount
                end = start + pagecount
                vms_result_page = vms_result[start:end]

                # 8. 移除临时排序字段
                for item in vms_result_page:
                    item.pop('_sort_vm_name', None)
                    item.pop('_sort_port_group_name', None)

                return {
                    "msg": "查询成功",
                    "code": 200,
                    "data": vms_result_page,
                    "total": total
                }
        except Exception as e:
            self.set_status(502)
            print("查询虚拟机/端口/端口组信息报错：", e)
            return {"msg": f"查询失败：{e}", "code": 500}


    @role_required()
    @post(_path="/v5/switch/nic", _consumes=mediatypes.APPLICATION_JSON,)
    def hci_post_switch_bound_nic(self, form):
        username = self.get_cookie('username', "")
        rolename = self.get_cookie('role_name')
        switch_id = form.get("switch_id")
        if not switch_id:
            return {"msg": "缺少网桥ID", "code": 200}

        try:
            with self.session_scope() as session:
                # 1. 查询交换机
                switch = session.query(Switch).filter(Switch.id == switch_id).first()
                if not switch:
                    return {"msg": "未找到对应的交换机", "code": 200}

                ovs_name = switch.ovs_name

                # 2. 获取主机列表
                hosts = []
                if switch.vswitch_type == "local":
                    # 优先用 switch.host
                    if switch.host:
                        hosts.append(switch.host)
                    else:
                        # 如果为空，则通过 HostSwitchMapping 查找
                        for mapping in switch.host_mappings:
                            if mapping.host:
                                hosts.append(mapping.host)
                elif switch.vswitch_type == "distributed":
                    for mapping in switch.host_mappings:
                        if mapping.host:
                            hosts.append(mapping.host)

                # 3. 查询每个主机的网卡信息，按网卡为单位返回
                result = []
                for host in hosts:
                    try:
                        url = f"http://{host.ip}:9178/nic/by_ovs_bridge"
                        resp = requests.post(url, json={"name": ovs_name}, timeout=2)
                        nics = resp.json() if resp.status_code == 200 and isinstance(resp.json(), list) else []
                    except Exception as e:
                        print(f"请求主机{host.ip}网卡信息失败: {e}")
                        nics = []

                    for nic in nics:
                        # 每个网卡信息加上host前缀的主机信息
                        nic["host_id"] = str(host.id)
                        nic["host_name"] = host.name
                        nic["host_ip"] = host.ip
                        result.append(nic)

                return {
                    "msg": "查询成功",
                    "code": 200,
                    "data": result
                }
        except Exception as e:
            self.set_status(502)
            print("查询交换机/主机/网卡信息报错：", e)
            return {"msg": f"查询失败：{e}", "code": 500}
        