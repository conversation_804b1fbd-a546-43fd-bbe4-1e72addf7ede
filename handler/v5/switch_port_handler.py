# -*- coding: utf-8 -*-
from datetime import datetime

import tornado.ioloop
from sqlalchemy import func

import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from api.ovs.client import Client
from util.cov import todict
from db.model.vm import VmGroup, Vms, Volumes

from sqlalchemy.orm import joinedload
from api.log.log import CustomLogger

import traceback

import logging
from db.model.hci.network import (Switch, SwitchPorts, SwitchPortGroups,
                                  SwitchPortGroupRules, Router, RouterPort,
                                  RouterTable, PortGroups, PortGroupRules, Port)

from db.model.hci.compute import Host
from util.decorators import deprecated_api, role_required

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class SwitchPortHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @role_required()
    @post(_path="/v5/switch/port/create", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_switch_port_add(self, form):
        role = self.get_cookie('username', "")

        switch_name = form.get("name", "")
        # 获取用户信息
        username = self.get_cookie('username', "")
        role = self.get_cookie('role', "")

        # 参数提取与初步验证
        switch_id = form.get("switch_id", "").strip()
        port_name = form.get("name", "").strip()
        ip = form.get("ip", "").strip()
        netmask = form.get("netmask", "").strip()
        gate_way = form.get("gate_way", "").strip()
        port_type = form.get("port_type", "access")  # 默认为访问模式
        status = form.get("status", "active")
        switch_port_group_id = form.get("switch_port_group_id", "").strip()

        try:
            # TODO 创建交换机端口 判断交换机是否是集群类型
            # client = Client(host_ip)
            # client.ovs_create_local_switch(client, switch_name)

            # 写入数据库
            with self.session_scope() as session:
                switch = session.query(Switch).filter(Switch.id == switch_id).first()
                if switch is None:
                    return {"msg": f"未找到指定的交换机：{switch_id}", "code": 400}

                # 检查端口组是否存在（如果提供了）
                if switch_port_group_id:
                    port_group = session.query(SwitchPortGroups).filter(
                        SwitchPortGroups.id == switch_port_group_id).first()
                    if port_group is None:
                        return {"msg": f"未找到指定的端口组：{switch_port_group_id}", "code": 400}

                # 创建新的端口实例
                switch_port = SwitchPorts(
                    switchs_id=switch_id,
                    name=port_name,
                    ip=ip,
                    netmask=netmask,
                    gate_way=gate_way,
                    port_type=port_type,
                    status=status,
                    switch_port_group_id=switch_port_group_id if switch_port_group_id else None
                )

                session.add(switch_port)

            new_logger.log(
                self.username, "虚拟交换机端口", "创建本地虚拟交换机端口", "成功", role,
                "创建虚拟交换机端口: {},成功".format(switch_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟交换机端口", "创建本地虚拟交换机端口", "失败", role,
                "创建虚拟交换机端口: {},成功".format(switch_name)
            )
            traceback.print_exc()
            return {"msg": f"创建虚拟交换机端口失败：{e}", "code": 500}

        return {"msg": "创建虚拟交换机端口成功", "code": 200}

    @deprecated_api()
    @post(_path="/v5/switch/port/add/test", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_switch_port_add_by_port_group(self, form):
        role = self.get_cookie('username', "")

        # 获取用户信息
        username = self.get_cookie('username', "")
        role = self.get_cookie('role', "")
        port_name = form.get("port_name", "")

        # 参数提取与初步验证
        host_id = form.get("host_id", "")
        switch_port_group_id = form.get("switch_port_group_id", "").strip()

        try:

            # 写入数据库
            with self.session_scope() as session:
                result = session.query(Switch, SwitchPortGroups) \
                    .join(SwitchPortGroups, Switch.id == SwitchPortGroups.switchs_id) \
                    .filter(SwitchPortGroups.id == switch_port_group_id) \
                    .first()
                if result:
                    switch_info, switch_port_group_info = result

                host = session.query(Host).filter(Host.id == host_id).first()
                host_ip = host.ip
                client = Client(host_ip)

                form["bridge_name"] = switch_info.name
                form["tap_name"] = port_name
                form["mtu"] = switch_info.mtu
                form["vlan_tag"] = switch_port_group_info.vlan_id
                client.create_tap_interface(client, form)

                # 创建新的端口实例
                switch_port = SwitchPorts(
                    switchs_id=switch_info.id,
                    name=port_name,
                    switch_port_group_id=switch_port_group_id
                )

                session.add(switch_port)
        except  Exception as e:
            traceback.print_exc()
            return {"msg": f"创建虚拟交换机端口失败：{e}", "code": 500}

        return {"msg": "创建虚拟交换机端口成功", "code": 200}

    @role_required()
    @post(_path="/v5/switch/port/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_switch_port_list(self, form):
        switch_id = form.get("id", "")
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        page = int(form.get('page', '1'))
        page_size = int(form.get('page_size', '10'))

        try:
            with self.session_scope() as session:
                query = session.query(SwitchPorts)
                if switch_id != "":
                    query = query.filter(SwitchPorts.switchs_id == switch_id)

                total_count_query = query.with_labels().statement.with_only_columns([func.count()]).order_by(
                    None)
                total_count = session.execute(total_count_query).scalar()
                ports = query.offset((page - 1) * page_size).limit(page_size).all()

                data = [port.to_dict_merge() for port in ports]

            return {
                "msg": "查询成功",
                "code": 200,
                "data": data,
                "total": total_count,
                "page": page,
                "page_size": page_size
            }
        except Exception as e:
            traceback.print_exc()
            self.set_status(500)
            return {"msg": f"查询端口失败: {e}", "code": 500}

    @role_required()
    @delete(_path="/v5/switch/port/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_switch_port_delete(self, form):
        role = self.get_cookie('username', "")

        switch_port_id = form.get("id", "")
        switch_name = form.get("name", "")
        host_ip = form.get("host_ip", "")
        try:
            # client = Client(host_ip)
            # client.ovs_delete_local_switch(client, switch_name)

            with self.session_scope() as session:
                # 创建switch表
                switch = session.query(SwitchPorts).filter(SwitchPorts.id == switch_port_id).delete()

            new_logger.log(
                self.username, "虚拟交换机端口", "删除本地虚拟交换机端口", "成功", role,
                "删除本地虚拟交换机端口: {},成功".format(switch_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟交换机端口", "删除本地虚拟交换机端口", "失败", role,
                "删除本地虚拟交换机端口: {},成功".format(switch_name)
            )
            traceback.print_exc()
            return {"msg": "删除本地虚拟交换机端口失败", "code": 500}

        return {"msg": "删除本地虚拟交换机端口成功", "code": 200}

    @role_required()
    @put(_path="/v5/switch/port/update", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_post_switch_port_update(self, form):
        role = self.get_cookie('username', "")

        switch_name = form.get("name", "")
        host_ip = form.get("host_ip", "")
        try:
            # TODO 创建本地虚拟交换机
            # client = Client(host_ip)
            # client.ovs_create_local_switch(client, switch_name)

            # 写入数据库
            with self.session_scope() as session:

                # 创建并保存 SwitchPorts 对象
                pnic_info = form.get('port_group_name', {})
                port_group = form.get('port_group', {})

                switch_port = SwitchPorts(
                    switchs_id=form.get('id'),  # 关联到上面创建的 switch
                    name=form.get('name'),
                    gate_way=form.get('gateway'),
                    ip=form.get('ip'),
                    ip_type=form.get('ip_type'),
                    netmask=form.get('netmask'),
                    pnics=form.get('nic'),
                    ports_num=form.get('trunk_port'),
                    status='active',  # 根据业务逻辑设置适当的状态
                    switch_port_group_id=form.get('switch_port_group_id')  # 新增字段处理
                )
                session.add(switch_port)

            new_logger.log(
                self.username, "虚拟交换机端口", "创建本地虚拟交换机端口", "成功", role,
                "创建本地虚拟交换机端口: {},成功".format(switch_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟交换机端口", "创建本地虚拟交换机端口", "失败", role,
                "创建本地虚拟交换机端口: {},成功".format(switch_name)
            )
            traceback.print_exc()
            return {"msg": "创建本地虚拟交换机端口失败", "code": 500}

        return {"msg": "创建本地虚拟交换机端口成功", "code": 200}

    # ======================================== 端口组 ====================================================
    @role_required()
    @post(_path="/v5/port/group/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_add(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        switch_id = form.get('switch_id', "")
        if switch_id == "":
            return {"msg": "请选择交换机", "code": "500"}
        name = form.get('name', '')
        remark = form.get('remark', '')
        vlan_id = form.get('vlan_id', '')
        try:
            with self.session_scope() as session:
                port_group = SwitchPortGroups()
                port_group.switchs_id = switch_id
                port_group.name = name
                port_group.vlan_id = vlan_id
                port_group.remark = remark
                session.add(port_group)
            new_logger.log(
                self.username, "端口组", "添加端口组", "成功", role,
                "添加端口组: {},成功".format(port_group.id)
            )
        except Exception as e:

            new_logger.log(
                self.username, "端口组", "添加端口组", "失败", role,
                "添加端口组: {},失败".format(port_group.id)
            )
            traceback.print_exc()
            return {"msg": f"添加端口组失败:{e}", "code": 500}
        return {"msg": "添加端口组成功", "code": 200}

    @role_required()
    @delete(_path="/v5/port/group/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_delete(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])
        names = form.get("names", [])
        if len(ids) == 0:
            return {"msg": "ok", "code": 200}

        try:
            with self.session_scope() as session:
                for group_id in ids:
                    session.query(SwitchPortGroups).filter(SwitchPortGroups.id == group_id).delete()
        except Exception as e:
            traceback.print_exc()
            new_logger.log(
                self.username, "端口组", "删除端口组", "失败", role,
                "删除端口组: {},失败".format(names)
            )
            return {"msg": f"删除端口组失败:{e}", "code": 500}
        new_logger.log(
            self.username, "端口组", "删除端口组", "成功", role,
            "删除端口组: {},成功".format(names)
        )
        return {"msg": "ok", "code": 200}

    @role_required()
    @put(_path="/v5/port/group/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_update(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        name = form.get('name', "")
        remark = form.get('remark', "")
        group_id = form.get("id", "")
        try:
            with self.session_scope() as session:

                # 检查交换机是否存在
                exists = session.query(SwitchPortGroups).filter(SwitchPortGroups.id == group_id).scalar() is not None
                if not exists:
                    new_logger.log(
                        self.username, "端口组", "更新端口组", "失败", role,
                        "更新端口组: {},失败".format(name)
                    )
                    return {"msg": "端口组不存在", "code": 404}
                # 构建更新字典
                update_data = {}

                if name != '':
                    update_data['name'] = name
                if remark != "":
                    update_data['remark'] = remark

                # 如果有数据需要更新，则执行更新
                if update_data:
                    session.query(SwitchPortGroups).filter(SwitchPortGroups.id == group_id).update(update_data)

            new_logger.log(
                self.username, "端口组", "更新端口组", "成功", role,
                "更新端口组: {},成功".format(group_id)
            )
        except Exception as e:
            new_logger.log(
                self.username, "端口组", "更新端口组", "失败", role,
                "更新端口组: {},失败".format(group_id)
            )
            traceback.print_exc()
            return {"msg": f"更新端口组失败 {e}", "code": 500}
        return {"msg": "更新端口组成功", "code": 200}

    @role_required(("sysadm", "supadm", "operator"))
    @post(_path="/v5/port/group/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_list(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        page = int(form.get('page', '1'))
        page_size = int(form.get('page_size', '10'))
        switch_id = form.get("id")

        try:
            with self.session_scope() as session:
                groups_query = session.query(SwitchPortGroups).filter(SwitchPortGroups.switchs_id == switch_id)
                total_count = groups_query.count()
                port_groups = groups_query.offset((page - 1) * page_size).limit(page_size).all()

                data = [port_group.to_dict_merge() for port_group in port_groups]

            return {
                "msg": "查询虚拟交换机成功",
                "code": 200,
                "data": data,
                "total": total_count,
                "page": page,
                "page_size": page_size
            }
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"列出端口组失败: {e}", "code": 500}

    # ======================================== 端口组规则 ====================================================

    @role_required()
    @post(_path="/v5/port/group/rule/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_rule_add(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        switch_port_group_id = form.get('id', ""),
        direction = form.get('direction', ""),
        ethertype = form.get('ethertype', ""),
        protocol = form.get('protocol', ""),
        port_range_min = form.get('port_range_min', ""),
        port_range_max = form.get('port_range_max', ""),
        remote_ip_prefix = form.get('remote_ip_prefix', ""),
        remark = form.get('remark', "")

        try:
            with self.session_scope() as session:
                rule = SwitchPortGroupRules()
                rule.switch_port_group_id = switch_port_group_id
                rule.direction = direction
                rule.ethertype = ethertype
                rule.protocol = protocol
                rule.port_range_min = port_range_min
                rule.port_range_max = port_range_max
                rule.remote_ip_prefix = remote_ip_prefix
                rule.remark = remark
                session.add(rule)
            new_logger.log(
                self.username, "端口组规则", "添加端口组规则", "成功", role,
                "添加端口组规则: {},成功".format(rule.id)
            )
        except Exception as e:
            new_logger.log(
                self.username, "端口组规则", "添加端口组规则", "失败", role,
                "添加端口组规则: {},失败".format(rule.id)
            )
            traceback.print_exc()
            return {"msg": f"添加端口组规则失败: {e}", "code": 500}
        return {"msg": "添加端口组规则成功", "code": 200}

    @role_required()
    @delete(_path="/v5/port/group/rule/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_rule_delete(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        rule_id = form.get("id", "")
        try:
            with self.session_scope() as session:
                session.query(SwitchPortGroupRules).filter(SwitchPortGroupRules.id == rule_id).delete()
            new_logger.log(
                self.username, "端口组规则", "删除端口组规则", "成功", role,
                "删除端口组规则: {},成功".format(rule_id)
            )
        except Exception as e:
            new_logger.log(
                self.username, "端口组规则", "删除端口组规则", "失败", role,
                "删除端口组规则: {},失败".format(rule_id)
            )
            traceback.print_exc()
            return {"msg": f"删除端口组规则失败: {e}", "code": 500}
        return {"msg": "删除端口组规则成功", "code": 200}

    @role_required()
    @put(_path="/v5/port/group/rule/update", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_rule_update(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        rule_id = form.get("id", "")
        try:
            with self.session_scope() as session:
                # 检查规则是否存在
                exists = session.query(SwitchPortGroupRules).filter(
                    SwitchPortGroupRules.id == rule_id).scalar() is not None
                if not exists:
                    new_logger.log(
                        self.username, "端口组规则", "更新端口组规则", "失败", role,
                        "更新端口组规则: {},失败".format(rule_id)
                    )
                    return {"msg": "端口组规则不存在", "code": 404}

                # 构建更新数据字典，仅包含实际变化的字段
                update_data = {
                    'direction': form.get('direction'),
                    'ethertype': form.get('ethertype'),
                    'protocol': form.get('protocol'),
                    'port_range_min': form.get('port_range_min'),
                    'port_range_max': form.get('port_range_max'),
                    'remote_ip_prefix': form.get('remote_ip_prefix'),
                    'remark': form.get('remark')
                }

                # 过滤掉没有改变或未提供的值 (None 或者与数据库中的现有值相同)
                current_rule = session.query(SwitchPortGroupRules).filter(SwitchPortGroupRules.id == rule_id).first()
                for key, value in list(update_data.items()):
                    if value is None or getattr(current_rule, key) == value:
                        del update_data[key]

                # 如果有需要更新的数据，则执行更新
                if update_data:
                    session.query(SwitchPortGroupRules).filter(SwitchPortGroupRules.id == rule_id).update(update_data)

            new_logger.log(
                self.username, "端口组规则", "更新端口组规则", "成功", role,
                "更新端口组规则: {},成功".format(rule_id)
            )
        except Exception as e:
            new_logger.log(
                self.username, "端口组规则", "更新端口组规则", "失败", role,
                "更新端口组规则: {},失败".format(rule_id)
            )
            traceback.print_exc()
            return {"msg": f"更新端口组规则失败: {e}", "code": 500}
        return {"msg": "更新端口组规则成功", "code": 200}

    @role_required()
    @post(_path="/v5/port/group/rule/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_port_group_rule_list(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        page = int(form.get('page', '1'))
        page_size = int(form.get('page_size', '10'))

        try:

            with self.session_scope() as session:
                query = session.query(SwitchPortGroupRules)
                # 筛选条件

                total_count = query.count()
                rules = query.offset((page - 1) * page_size).limit(page_size).all()

                data = [rule.to_dict_merge() for rule in rules]
            return {
                "msg": "查询虚拟交换机成功",
                "code": 200,
                "data": data,
                "total": total_count,
                "page": page,
                "page_size": page_size
            }
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"列出端口组规则失败：{e}", "code": 500}
