# -*- coding: utf-8 -*-

from calendar import c
from multiprocessing import pool
from unittest import result
from venv import create
import tornado.ioloop
from sqlalchemy import asc, desc, func, case

import pyrestful.rest
from api.prometheus.client import Client as Pclient
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom

from api.log.log import CustomLogger
from celery import Celery, chord
from util.decorators import role_required, deprecated_api


import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class ClusterHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v5/cluster/detail/{cluster_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_cluster_detail(self, cluster_id):

        with self.session_scope() as session:
            #查询cluster记录
            cluster = session.query(Cluster).filter(Cluster.id == cluster_id).first()
            if cluster:
                data =  Cluster.to_dict_merge(cluster)
                return data

        return {"msg":"没有找到虚机", "code": 404}

    @role_required()
    @post(_path="/v5/cluster/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_create(self, form):
        role = self.get_cookie('role', "")
        cluster_name = form.get("name", "")
        pool_id = form.get("pool_id", "")
        remark = form.get("remark", "")
        
        try:
            with self.session_scope() as session:
                cluster = Cluster()
                cluster.name = cluster_name
                cluster.pool_id = pool_id
                cluster.remark = remark
                session.add(cluster)
                
        except Exception as e:
            new_logger.log(
                self.username, "集群", "创建集群", "失败", role,
                "创建集群失败: %s" % str(e)
            )
            return {"status": "error", "message": "创建集群失败: %s" % str(e)}
        
        new_logger.log(
            self.username, "集群", "创建集群", "成功", role,
            "创建集群成功"
        )
        return {"msg": "创建集群完成", "code": 200}

    @role_required()
    @post(_path="/v5/cluster/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_delete(self, form):
        name = form.get("name", "")
        role = self.get_cookie('role', "")
        
        cluster_id = form.get("id", "")
        try:
            with self.session_scope() as session:
                cluster = session.query(Cluster).filter(Cluster.id == cluster_id).first()
                if cluster.hosts:
                    new_logger.log(
                        self.username, "集群", "删除集群", "失败", role, "删除集群{name}失败"
                    )
                    return {"msg": "删除集群失败，请确认集群下是否存在主机", "code": 400}
                session.query(Cluster).filter(Cluster.id == cluster_id).delete()

        except Exception as e:
            new_logger.log(
                self.username, "集群", "删除集群", "失败", role, f"删除集群{name}失败"
            )
            traceback.print_exc()
            return {"code": 400, "msg": f"删除集群{name}失败"}
        
        new_logger.log(
            self.username, "集群", "删除集群", "成功", role,
            f"删除集群{name}成功"
        )
             
        return {"msg": "删除集群完成", "code": 200}
    
    
    # @log("集群", "删除集群")
    # @post(_path="/v5/cluster/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    # def hci_post_cluster_delete(self, form):
    #     name = form.get("name", "")
    #     role = self.get_cookie('role', "")
        
    #     cluster_id = form.get("id", "")

    #     with self.session_scope() as session:
    #         cluster = session.query(Cluster).filter(Cluster.id == cluster_id).delete()
                

             
    #     return {"msg": "删除集群完成", "code": 200}

    @role_required()
    @post(_path="/v5/cluster/name/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_update(self, form):
        role = self.get_cookie('role', "")
        cluster_id = form.get("id", "")
        cluster_name = form.get("name", "")
        remark = form.get("remark", "")
        ha_level = form.get("ha_level", None)
        try:
            with self.session_scope() as session:
                # Build update dictionary dynamically - only update fields that are provided
                update_data = {}
                if cluster_name is not None and cluster_name != "":
                    update_data[Cluster.name] = cluster_name
                if remark is not None:
                    update_data[Cluster.remark] = remark
                if ha_level is not None:
                    update_data[Cluster.ha_level] = int(ha_level)
                
                # Only perform update if there are fields to update
                if update_data:
                    session.query(Cluster).filter(Cluster.id == cluster_id).update(update_data)

        except Exception as e:
            new_logger.log(
                self.username, "集群", "更新集群", "失败", role,
                "更新集群: {},失败".format(cluster_name)
            )
            traceback.print_exc()
            return {"msg": "更新集群失败", "code": 500}

        new_logger.log(
            self.username, "集群", "更新集群", "成功", role,
            "更新集群: {},成功".format(cluster_name)
        )

        return {"msg": "ok", "code": 200}

    @role_required()
    @post(_path="/v5/cluster/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_list(self, form):
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        type = form.get('type', 'recource')
        _id = form.get('_id', '')

        with self.session_scope() as session:
            # 创建基础查询,同时关联主机信息
            query = session.query(Cluster, func.count(Host.id).label('host_count'),
                                func.sum(case([(Host.is_connected == "1", 1)], else_=0)).label('host_up_count'),
                                func.sum(case([(Host.is_connected == "0", 1)], else_=0)).label('host_down_count'),
                                func.sum(case([(Host.is_connected == "2", 1)], else_=0)).label('host_error_count'))\
                          .outerjoin(Host, Host.cluster_id == Cluster.id)\
                          .group_by(Cluster.id)

            # 根据不同类型进行过滤
            if type == 'recource':
                query = query.join(Pool)
            elif type == 'pool':
                query = query.join(Pool).filter(Pool.id == _id)

            # 添加搜索条件
            if search_str:
                query = query.filter(Cluster.name.ilike(f"%{search_str}%"))

            # 添加排序
            if hasattr(Cluster, order_by):
                order_column = getattr(Cluster, order_by)
                if order_type == 'asc':
                    query = query.order_by(asc(order_column))
                else:
                    query = query.order_by(desc(order_column))

            # 获取总记录数
            total_records = query.count()

            # 执行分页
            results = query.offset((page - 1) * per_page).limit(per_page).all()

            # 将结果转换为字典列表
            cluster_list = []
            for cluster, host_count, host_up, host_down, host_error in results:
                cluster_data = cluster.to_dict_merge()
                cluster_data.update({
                    'host_count': int(host_count or 0),
                    'host_up_count': int(host_up or 0),
                    'host_down_count': int(host_down or 0),
                    'host_error_count': int(host_error or 0)
                })
                # 获取该集群下的所有主机
                hosts = session.query(Host).filter(Host.cluster_id == cluster.id).all()
                cluster_data['hosts'] = [host.to_dict() for host in hosts]
                cluster_list.append(cluster_data)

            return {
                "msg": "获取集群列表成功",
                "code": 200,
                "total": total_records,
                "data": cluster_list
            }

    @deprecated_api("该接口已废弃")
    @get(_path="/v5/cluster/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_get_cluster_list(self):
        with self.session_scope() as session:
            # 首先创建基础查询
            hosts = session.query(Cluster).all()
            host_list = [host.to_dict() for host in hosts]

            return host_list


    @get(_path="/v5/cluster/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_cluster_overview(self, _id):
        role = self.get_cookie('role', "")
        
        with self.session_scope() as session:
            # Initialize response structure
            data = {
                "host": {
                    "count": 0,
                    "cpu_cores_of_physical_num": 0,
                    "cpu_of_physical_num": 0,
                    "cpu_of_runing_num": 0,
                    "cpu_of_alloc_num": 0
                },
                "vm": {
                    "count": 0
                },
                "cpu": {"used": 0, "unused": 0, "total": 0},
                "mem": {"used": 0, "unused": 0, "total": 0},
                "storage": {"used": 0, "unused": 0, "total": 0}
            }

            cluster = session.query(Cluster).filter(Cluster.id == _id).first()

            # Step 1: Query hosts in this cluster
            hosts = session.query(Host).filter(Host.cluster_id == _id).all()
            data["host"]["count"] = len(hosts)

            # Step 2: Query VMs in this cluster
            vms = session.query(Domain).filter(Domain.cluster_id == _id).all()
            data["vm"]["count"] = len(vms)
            
            # Calculate allocated resources
            allocation_cpu_count = 0
            allocation_mem_count = 0
            
            for vm in vms:
                allocation_cpu_count += int(vm.vcpu)
                allocation_mem_count += int(vm.memory)

            data["host"]["cpu_of_alloc_num"] = allocation_cpu_count

            # Step 3: Query resource usage via Prometheus
            c = Pclient()
            ip_pattern_parts = [f"{host.ip}:9100" for host in hosts]
            ip_regex = "|".join(ip_pattern_parts)
            
            # CPU metrics
            cpu_all_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            if cpu_all_count:
                total_cpu_cores = int(float(cpu_all_count[0]["value"][1]))
                data["host"]["cpu_cores_of_physical_num"] = total_cpu_cores
                data["host"]["cpu_of_physical_num"] = total_cpu_cores  # Same as cores for now
                data["cpu"]["total"] = total_cpu_cores
                data["cpu"]["used"] = allocation_cpu_count
                data["cpu"]["unused"] = max(0, total_cpu_cores - allocation_cpu_count)

            # CPU usage (running)
            cpu_usage_query = c.query_vector_by_query(f'sum(100 - (avg by (instance) (rate(node_cpu_seconds_total{{job="node_exporter", mode="idle", instance=~"{ip_regex}"}}[5m])) * 100))')
            if cpu_usage_query:
                cpu_usage_percent = float(cpu_usage_query[0]["value"][1])
                # Calculate running CPU based on usage percentage
                data["host"]["cpu_of_runing_num"] = int((cpu_usage_percent / 100) * total_cpu_cores) if cpu_all_count else 0

            # Memory metrics
            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            if all_mem_count:
                total_mem = int(float(all_mem_count[0]["value"][1]))
                used_mem = int(float(use_mem_count[0]["value"][1])) if use_mem_count else 0
                data["mem"]["total"] = total_mem
                data["mem"]["used"] = used_mem
                data["mem"]["unused"] = max(0, total_mem - used_mem)

            # Storage metrics
            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            if all_disk_count:
                total_storage = int(float(all_disk_count[0]["value"][1]))
                used_storage = int(float(use_disk_count[0]["value"][1])) if use_disk_count else 0
                data["storage"]["total"] = total_storage
                data["storage"]["used"] = used_storage
                data["storage"]["unused"] = max(0, total_storage - used_storage)

        return {"msg": "查询集群概览成功", "code": 200, "data": data}