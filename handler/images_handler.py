# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import <PERSON><PERSON><PERSON><PERSON>

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.images import ImageCreateFrom,ImageEditFrom,ImageDeleteFrom
from model.instances import Clusters, Instances
from util.cov import todict
from api.openstack.client import Client

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()

class ImagesV2Handler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        #self.set_header("Access-Control-Allow-Origin", "http://192.168.2.178:8008")
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    @get(_path="/v2/images", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_images(self):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 查询全部镜像
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
        
        client = Client()
        res = client.ImageClient.openstack_get_all_list_v2(client)
        return res  


    @post(_path="/v2/images/create", _consumes=mediatypes.APPLICATION_FILE, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_image_v2(self, imagecreateform):
        role = self.get_cookie('role', "")
        try:
            """
            image_from = {}
            image_from["disk_format"] = self.get_body_argument("disk_format")
            image_from["name"] = self.get_body_argument("name")
            """
            
            client = Client()
            image_meta = client.ImageClient.openstack_create_image_v2(client, imagecreateform)
            image_id = image_meta["id"]
            data = self.request.body
            res = client.ImageClient.openstack_upload_image(client, image_id, data)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建镜像",
            #                  "object": imagecreateform.get("name", ""),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "镜像管理", "创建镜像", "成功", role, "{} {},成功".format("创建镜像", imagecreateform.get("name", ""))
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "创建镜像",
                #              "object": imagecreateform.get("name", ""),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })

                new_logger.log(
                    self.username, "镜像管理", "创建镜像", "失败", role, "{} {},失败".format("创建镜像", imagecreateform.get("name", ""))
                )
                self.set_status(502)
                return {"msg": "error"}
        return res
    
    @put(_path="/v2/images/update/ostype",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_image_update_os_type(self, imageform):
        role = self.get_cookie('role', "")
        try:
            image_id = imageform.get("id")
            image_name = imageform.get("name")
            os_type= imageform.get("os_type")
            client = Client()            
            res = client.ImageClient.openstack_update_image_os_type(client, image_id, os_type)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "切换镜像类型",
            #                  "object": imageform.get(image_id, ""),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "镜像管理", "切换镜像类型", "成功", role, "{} {}为{},成功".format("切换镜像类型", image_name, os_type)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "切换镜像类型",
                #              "object": imageform.get(image_id, ""),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
            new_logger.log(
                self.username, "镜像管理", "切换镜像类型", "失败", role, "{} {}为{},失败".format("切换镜像类型", image_name, imageform.get("os_type", ""))
            )
            return {"msg":"error"}
        return {"msg":"ok"}

class ImagesHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        #self.set_header("Access-Control-Allow-Origin", "http://192.168.2.178:8008")
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/images", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_images(self):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 查询全部镜像
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
        
        client = Client()
        res = client.ImageClient.openstack_get_all_list(client)
        return res  
    
    @delete(_path="/v1/images/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_image(self, imagedeleteform):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 删除镜像  
        responses:
          '201':
            description: 删除成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ImageDeleteModel'
        """
        role = self.get_cookie('role', "")
        try:
            client = Client()
            id = imagedeleteform.get("id", "")
            res = client.ImageClient.openstack_delete_image(client, id)
            if res["msg"] != "ok":
                new_logger.log(
                    self.username, "镜像管理", "删除镜像", "失败", role, "{} {},失败".format("删除镜像", imagedeleteform.get("name", ""))
                )
                return res

        except Exception as e:
            new_logger.log(
                self.username, "镜像管理", "删除镜像", "失败", role, "{} {},失败".format("删除镜像", imagedeleteform.get("name", ""))
            )
            return {"msg":"error"}  
        
        new_logger.log(
            self.username, "镜像管理", "删除镜像", "成功", role, "{} {},成功".format("删除镜像", imagedeleteform.get("name", ""))
        )        
        return res
    
    @post(_path="/v1/images/create",_types=[ImageCreateFrom],_consumes=mediatypes.APPLICATION_FILE, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_image(self, imagecreateform):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 创建镜像  参数 1.镜像名称 2.镜像格式
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/CreateImageModel'
        """
        role = self.get_cookie('role', "")
        try:
            image_from = ImageCreateFrom()
            image_from.disk_format = self.get_body_argument("disk_format")
            image_from.name = self.get_body_argument("name")
            
            client = Client()
            image_meta = client.ImageClient.openstack_create_image(client, image_from)
            image_id = image_meta["id"]
            data = self.request.body
            res = client.ImageClient.openstack_upload_image(client, image_id, data)
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建镜像",
                             "object": imagecreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            new_logger.log(
                self.username, "镜像管理", "创建镜像", "成功", role, "{} {},成功".format("创建镜像", imagecreateform.name)
            )
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建镜像",
                             "object": imagecreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "镜像管理", "创建镜像", "失败", role, "{} {},失败".format("创建镜像", imagecreateform.name)
                )
                return {"msg":"error"}
        return res



    
    @put(_path="/v1/images/edit",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_edit_image(self, imageeditform):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 编辑镜像  
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ImageEditModel'
        """
        role = self.get_cookie('role', "")
        try:
            client = Client()
            id = imageeditform.get("id", "")
            name = imageeditform.get("name", "")
            res = client.ImageClient.openstack_edit_image(client, id, name)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "重命名镜像",
            #                  "object": imageeditform.name,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "镜像管理", "重命名镜像", "成功", role, "{} {},成功".format("重命名镜像", name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "重命名镜像",
                #              "object": imageeditform.name,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
            new_logger.log(
                self.username, "镜像管理", "重命名镜像", "失败", role, "{} {},失败".format("重命名镜像", imageeditform.get("name", ""))
            )
            self.set_status(502)
            return {"msg": "error"}
        return res
    
    @get(_path="/v1/images/list", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_images_only(self):
        
        client = Client()
        res = client.ImageClient.openstack_get_all_image_list(client)
        return res  
