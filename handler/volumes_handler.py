# -*- coding: utf-8 -*-
import traceback

import tornado.ioloop
import pyrestful.rest
from api.log.log import Custom<PERSON>ogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
import json

from model.volumes import *
from util.cov import todict
from api.openstack.client import Client
from api.prometheus.client import Client as Pclient
from Page.Pagenation import  Pagenation
from db.model.user import User
from db.model.vm import Volumes, Snapshot
from db.model.user import User
from api.model.volumes import VolumeCreateFrom, VolumeCreateFromV2
from string import Template

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()

class SnapshotHandler(pyrestful.rest.RestHandler):
    
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")
        
    #@post(_path="/v1/volumes/snapshot/create",_types=[VolumeActionFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/volumes/snapshot/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_snapshot_create(self, form):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘快照
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeSnapshotModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username==username).first()
        
        op = "创建快照" 

        try:
            client = Client()
            snapshot_id = client.VolumeClient.openstack_volume_snapshot_create(client,form)

            if role == "operator":
                snapshot = Snapshot()
                snapshot.snapshot_id = snapshot_id
                snapshot.user_id = user.id
                with self.session_scope() as session:
                    session.add(snapshot)
                    session.commit()
                
                
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": op,
            #                  "object": snapshot_id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "快照管理", op, "成功", role, "{}:{},成功".format(op, form.get("snapname", ""))
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": op,
                #              "object": form.get("id", ""),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "快照管理", op, "失败", role, "{}:{},失败".format(op, form.get("snapname", ""))
                )
                self.set_status(502)
                return {"msg": "error"}
        
        return {"msg":"ok"} 

    @post(_path="/v1/volumes/snapshot/to/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_volume_to_snapshot_type(self, volumecreateform):
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()

        try:
            client = Client()
            res = client.VolumeClient.openstack_create_blank_volume_v3(client,volumecreateform)
            update_bootable = client.VolumeClient.openstack_update_volume_bootable(client, res["id"])

            if role == "operator":
                my_volume = Volumes()
                my_volume.user_id = user.id
                my_volume.volume_id = res["id"]
                with self.session_scope() as session:
                    session.add(my_volume)
                    session.commit()

            new_logger.log(
                self.username, "存储管理", "创建云硬盘", "成功", role,
                "{}:{},成功".format("创建云硬盘", volumecreateform.get("name", ""))
            )
        except Exception as e:
                new_logger.log(
                    self.username, "存储管理", "创建云硬盘", "失败", role,
                    "{}:{},失败".format("创建云硬盘", volumecreateform.get("name", ""))
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return res

    @post(_path="/v1/volumes/snapshot/backup",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_snapshot_backup(self, volumeform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘快照
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeSnapshotModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """

        op = "创建备份"
        role = self.get_cookie("role")
        try:
            client = Client()
            
            res = client.VolumeClient.openstack_volume_snapshot_backup(client,volumeform)
            
            

            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": op,
            #                  "object": volumeform.id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "快照管理", op, "成功", role, "{}:{},成功".format(op, volumeform.get("name", ""))
            )

        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": op,
                #              "object": volumeform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "快照管理", op, "成功", role, "{}:{},成功".format(op, volumeform.get("name", ""))
                )
                self.set_status(502)
                return {"msg": "error"}
        
        return res

    @post(_path="/v1/volumes/snapshot/edit",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_snapshot_edit(self, form):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘快照
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeSnapshotModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """


        op = "编辑快照"
        role = self.get_cookie("role")
        try:
            client = Client()
            
            res = client.VolumeClient.openstack_volume_snapshot_edit(client,form)
            
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": op,
            #                  "object": form["id"],
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "快照管理", op, "成功", role, "{}:{},成功".format(op, form.get("name", ""))
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": op,
                #              "object": form["id"],
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "快照管理", op, "失败", role, "{}:{},失败".format(op, form.get("name", ""))
                )
                self.set_status(502)
                return {"msg": "error"}
        
        return res

    @delete(_path="/v1/volumes/snapshot/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_volume_snapshot_delete(self, form):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘快照
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeSnapshotModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """

        op = "删除快照"
        role = self.get_cookie("role")
        snapshot_ids = form.get("ids", [])
        snapshot_names = form.get("names", [])

        client = Client()
        ins_vols = client.VolumeClient.openstack_get_all_list(client)

        try:
            for i in range(len(snapshot_ids)):
                snapshot_id = snapshot_ids[i]
                snapshot_name = snapshot_names[i]
                for j in range(len(ins_vols)):
                    ins_vol = ins_vols[j]
                    if snapshot_id == ins_vol["snapshot_id"]:
                        msg = "删除快照失败，快照使用中。"
                        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s",
                        #             {"username": self.get_cookie('username', ""),
                        #              "op": op,
                        #              "object": snapshot_id,
                        #              "role": self.get_cookie('role', ""),
                        #              "result": "失败",
                        #              })
                        new_logger.log(
                            self.username, "快照管理", op, "失败", role, "{}:{},失败".format(op, snapshot_name)
                        )
                        return {"msg": msg}

                res = client.VolumeClient.openstack_volume_snapshot_delete(client,snapshot_id)
                with self.session_scope() as session:
                    session.query(Snapshot).filter(Snapshot.snapshot_id==snapshot_id).delete()
                    session.commit()
                
                # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #                 {"username": self.get_cookie('username', ""),
                #                  "op": op,
                #                  "object": snapshot_id,
                #                  "role": self.get_cookie('role', ""),
                #                  "result": "成功",
                #                  })
                new_logger.log(
                    self.username, "快照管理", op, "成功", role, "{}:{},成功".format(op, snapshot_name)
                )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": op,
                #              "object": snapshot_id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "快照管理", op, "失败", role, "{}:{},失败".format(op, snapshot_name)
                )
                self.set_status(502)
                return {"msg": "error"}
        
        return res


    @post(_path="/v1/volumes/snapshot/list",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_snapshot_list(self, form):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘快照
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeSnapshotModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        
        with self.session_scope() as session:
            user = session.query(User).filter(User.username==username).first()
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
        
        
        r = {}
        
        '''

                for snap in snaplist:
                    detail = client.VolumeClient.openstack_get_volume_detail(client,snap["volume_id"])
        '''
        
        try:
            client = Client()
            result = client.VolumeClient.openstack_volume_snapshot_list(client)
            for snap in result:
                detail = client.VolumeClient.openstack_get_volume_detail(client,snap["volume_id"])
                if detail["name"]:
                    snap["volume_name"] = detail["name"]
                else:
                    snap["volume_name"] = snap["volume_id"][:8]
            res = []
            my_snapshot_ids = []
            if role == "operator":
                with self.session_scope() as session:
                    my_snapshots = session.query(Snapshot).filter(Snapshot.user_id==user.id).all()
                for mys in my_snapshots:
                    my_snapshot_ids.append(mys.snapshot_id)

                for snap in result:
                    if snap["id"] in my_snapshot_ids:
                        res.append(snap)                
                
                
            else:
                res = result
            
            data = []
            if search_str:
                for d in res:
                    if search_str in d["name"]:
                        data.append(d)
            else:
                data = res

            if order_type == "desc" and order_by:
                data = sorted(data, key = lambda x:x[order_by], reverse=True)
            if order_type == "asc" and order_by:
                data = sorted(data, key = lambda x:x[order_by], reverse=False)
            
            obj = Pagenation(data, form.get("page", 1), form.get("pagecount", 10))
            volume_list = obj.show()
          
            r = {
                "total": len(data),
                "pages": obj.total(),
                "data": volume_list,
                "msg": "ok"
                }
            
            
        except Exception as e:

           
                return {"msg":"error"} 
        
        return r 


class VolumesHandler(pyrestful.rest.RestHandler):
    
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")
        
    @post(_path="/v1/volumes"  , _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_volumes(self,form):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 查询云硬盘列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumesListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayVolumesDetailModel'
        """
        
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")

        client = Client()
        result = client.VolumeClient.openstack_get_all_list(client)
        
        res = []
        my_vids = []
        if role == "operator":
            with self.session_scope() as session:
                my_volumes = session.query(Volumes).filter(Volumes.user_id==user.id).all()
                for myv in my_volumes:
                    my_vids.append(myv.volume_id)
        
        
            for vol in result:
                if vol["id"] in my_vids:
                    res.append(vol)
        else:
            res = result
            
        data = []
        
        for d in res:
            if not d["name"]:
                d["name"] = d["id"][:8]
        
        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res

        if order_type == "desc" and order_by:
            data = sorted(data, key = lambda x: x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by])        
        
        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        volume_list = obj.show()
        
        for volume in volume_list:
            if volume["attachments"]:
                volume["host"] = volume["attachments"][0]["host_name"]
                re = client.NovaClient.openstack_getpost_server_detail(client, volume["attachments"][0]["server_id"])
                volume["connection"] = re["name"]
            else :
                volume["connection"] = ""
            
                
        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": volume_list
            }
        
        return r
    
    @post(_path="/v2/volumes", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_volumes_v2(self,form):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 查询云硬盘列表
        requestBody:
          description: 查询条件
          content:
            application/json:
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
        """

        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            # session.expunge(user)
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
        page = form.get("page")
        pagecount = form.get("pagecount")

        params = {}
        if search_str != "":
            params["name"] = search_str
        if order_by == "name":
            params["sort_key"] = "name"
        elif order_by == "size":
            params["sort_key"] = "size"
        if order_type == "asc":
            params["sort_dir"] = "asc"

        client = Client()
        vol_list = client.VolumeClient.openstack_get_all_list_v2(client, params)

        params["limit"] = pagecount
        if page > 1:
            marker = vol_list[(page-1) * pagecount-1]["id"]
            params["marker"] = marker

        vol_data = client.VolumeClient.openstack_get_all_detail_v2(client, params)

        total = len(vol_list)

        r = {
            "total": len(vol_list),
            "pages": total // pagecount + 1,
            "data": vol_data
            }

        return r

    @post(_path="/v3/volumes"  , _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_volumes_v3(self,form):
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            if user is not None:
                session.expunge(user)
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")

        client = Client()
        #打印接口时间
        import time
        start = time.time()
        result = client.VolumeClient.openstack_get_all_list(client)
        end = time.time()
        print("云盘接口时间：", end-start)
        print("云盘数量：", len(result))

        start = time.time()
        client = Client()
        server_list = client.NovaClient.openstack_get_all_instance_list(client)
        end = time.time()
        print("虚机接口时间：", end-start)




        for volume in result:
            for server in server_list:
                if volume["attachments"]:
                    if volume["attachments"][0]["server_id"] == server["id"]:
                        #volume["host"] = server["host_name"]
                        volume["connection"] = server["name"]
                        break
                else:
                    volume["connection"] = ""


        res = []
        my_vids = []
        if role == "operator":
            with self.session_scope() as session:
                my_volumes = session.query(Volumes).filter(Volumes.user_id==user.id).all()
                for myv in my_volumes:
                    my_vids.append(myv.volume_id)
        
        
            for vol in result:
                if vol["id"] in my_vids:
                    res.append(vol)
        else:
            res = result
            
        data = []
        
        for d in res:
            if not d["name"]:
                d["name"] = d["id"][:8]
        
        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res

        if order_type == "desc" and order_by:
            data = sorted(data, key = lambda x: x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by])        
        
        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        volume_list = obj.show()
        
        # for volume in volume_list:
        #     if volume["attachments"]:
        #         volume["host"] = volume["attachments"][0]["host_name"]
        #         re = client.NovaClient.openstack_getpost_server_detail(client, volume["attachments"][0]["server_id"])
        #         volume["connection"] = re["name"]
        #     else :
        #         volume["connection"] = ""
            
                
        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": volume_list
            }
        
        return r
    
    @post(_path="/v1/volumes/available"  , _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_available_volumes(self,form):
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")

        client = Client()
        result = client.VolumeClient.openstack_get_all_list(client)
        
        res = []
        my_vids = []
        if role == "operator":
            with self.session_scope() as session:
                my_volumes = session.query(Volumes).filter(Volumes.user_id==user.id).all()
            for myv in my_volumes:
                my_vids.append(myv.volume_id)
        
        
            for vol in result:
                if vol["id"] in my_vids:
                    res.append(vol)
        else:
            res = result
            
        data = []
        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res

        if order_type == "desc" and order_by:
            data = sorted(data, key = lambda x:x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by])       
            
        data_list = []
        for volume in data:
            
            if not volume["name"]:
                volume["name"] = volume["id"][:8]
                
            if volume["status"] == "available":
                data_list.append(volume) 
        
        obj = Pagenation(data_list,form.get("page", 1),form.get("pagecount", 10))
        volume_list = obj.show()
                
        r = {
            "total": len(volume_list),
            "pages": obj.total(),
            "data": volume_list
            }
        
        return r
    
    @get(_path="/v1/availablevolumes"  , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_availablevolumes(self):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 查询云硬盘列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumesListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayVolumesDetailModel'
        """
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()        
        
        client = Client()
        result = client.VolumeClient.openstack_get_all_list(client)
        
        
        res = []
        my_vids = []
        if role == "operator":
            with self.session_scope() as session:
                my_volumes = session.query(Volumes).filter(Volumes.user_id==user.id).all()
            for myv in my_volumes:
                my_vids.append(myv.volume_id)
        
        
            for vol in result:
                if vol["id"] in my_vids:
                    res.append(vol)
        else:
            res = result
        
        data = []
        for volume in res:
            #volume_detail = client.NovaClient.openstack_get_server_detail(client, vm)
            if not volume["attachments"]:
                if not volume["name"]:
                    volume["name"] = volume["id"][:8]
                data.append(volume)
                
                
        return data
    
    @get(_path="/v1/volumes/type", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_volumes_types(self):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 查询全部云硬盘类型
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayVolumeTypeModel'
        """
        client = Client()
        res = client.VolumeClient.openstack_get_all_list_types(client)
        return res
    
    @post(_path="/v1/volumes/create",_types=[VolumeCreateFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_volume(self, volumecreateform):
        
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘 
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeAttachModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        
        try:
            client = Client()
            res = client.VolumeClient.openstack_create_blank_volume(client,volumecreateform)
            update_bootable = client.VolumeClient.openstack_update_volume_bootable(client, res["id"])
            
            
            if role == "operator":
                my_volume = Volumes()
                my_volume.user_id = user.id
                my_volume.volume_id = res["id"]
                with self.session_scope() as session:
                    session.add(my_volume)            
                    session.commit()
            
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云硬盘",
                             "object": volumecreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            new_logger.log(
                self.username, "存储管理", "创建云硬盘", "成功", role, "{}:{},成功".format("创建云硬盘", volumecreateform.name)
            )
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云硬盘",
                             "object": volumecreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "存储管理", "创建云硬盘", "失败", role,
                    "{}:{},失败".format("创建云硬盘", volumecreateform.name)
                )
                return {"msg":"error"}
        return res

    @post(_path="/v2/volumes/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_volume_type(self, volumecreateform):
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        
        try:
            client = Client()
            res = client.VolumeClient.openstack_create_blank_volume_v2(client,volumecreateform)
            update_bootable = client.VolumeClient.openstack_update_volume_bootable(client, res["id"])
            
            
            if role == "operator":
                my_volume = Volumes()
                my_volume.user_id = user.id
                my_volume.volume_id = res["id"]
                with self.session_scope() as session:
                    session.add(my_volume)            
                    session.commit()
            
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建云硬盘",
            #                  "object": volumecreateform.name,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", "创建云硬盘", "成功", role,
                "{}:{},成功".format("创建云硬盘", volumecreateform.get("name", ""))
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "创建云硬盘",
                #              "object": volumecreateform.name,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "创建云硬盘", "失败", role,
                    "{}:{},失败".format("创建云硬盘", volumecreateform.get("name", ""))
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return res
    
    @post(_path="/v1/volumes/createfromimage", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_volume_from_image(self, volumecreateform):
        
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘 
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeAttachModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        """
        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        """
        try:
            client = Client()
            create_form = VolumeCreateFrom()
            create_form.name = volumecreateform.get("name", "")
            create_form.size = volumecreateform.get("size", "")
            create_form.imageRef = volumecreateform.get("imageRef", "")
            create_form.description = volumecreateform.get("description", "")
            
            res = client.VolumeClient.openstack_create_volume(client,create_form)
            
            """
            if role == "operator":
                my_volume = Volumes()
                my_volume.user_id = user.id
                my_volume.volume_id = res["id"]
                with self.session_scope() as session:
                    session.add(my_volume)            
                    session.commit()
            
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云硬盘",
                             "object": create_form.name ,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            """
        except Exception as e:
                """
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云硬盘",
                             "object": volumecreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                """
                return {"msg":"error"}
        return res
    
    @post(_path="/v1/volumes/createimage", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_image_from_volume(self, form):
        role = self.get_cookie("role", "")
        try:
            client = Client()
            
            res = client.NovaClient.openstack_server_update_to_image(client,form.get("volumeid", ""),form.get("name", ""))
            
            
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建云硬盘",
            #                  "object": form.get("name", "") ,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", "创建云硬盘", "失败", role,
                "{}:{},失败".format("创建云硬盘", form.get("name", ""))
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "创建云硬盘",
                #              "object": form.get("name", ""),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })

                traceback.print_exc()
                self.set_status(502)
                new_logger.log(
                    self.username, "存储管理", "创建云硬盘", "失败", role,
                    "{}:{},失败".format("创建云硬盘", form.get("name", ""))
                )
                return {"msg":"error"}
        return res
    
    
    # @delete(_path="/v1/volumes/delete",_types=[VolumeDeleteFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @delete(_path="/v1/volumes/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_volume(self, volumeform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 删除云硬盘
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeDeleteModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = volumeform.get("id", "")
        name = volumeform.get("name", "")
        try:
            client = Client()
            #result = client.VolumeClient.openstack_volume_snapshot_list(client)
            res = client.VolumeClient.openstack_delete_volume(client, id)
            if res["msg"] == "snap":
                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除云硬盘",
                #              "object": volumeform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "删除云硬盘", "失败", role,
                    "{}:{},失败".format("删除云硬盘", name)
                )
            else:
                # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除云硬盘",
                #              "object": volumeform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "成功",
                #              })
                new_logger.log(
                    self.username, "存储管理", "删除云硬盘", "成功", role,
                    "{}:{},成功".format("删除云硬盘", name)
                )
   
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除云硬盘",
                #              "object": volumeform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "删除云硬盘", "失败", role,
                    "{}:{},失败".format("删除云硬盘", name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg": "error"}
        return res
    
    @put(_path="/v1/volumes/edit",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_edit_volume(self,volumeeditform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 编辑云硬盘
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        try:
            client = Client()
            id = volumeeditform.get("id", "")
            name = volumeeditform.get("name", "")
            res = client.VolumeClient.openstack_edit_volume(client, id, name)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "编辑云硬盘",
            #                  "object": "%s %s" % (volumeeditform.id,volumeeditform.name),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", "编辑云硬盘", "成功", role,
                "{}:{},成功".format("编辑云硬盘", name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "编辑云硬盘",
                #              "object": "%s %s" % (volumeeditform.id,volumeeditform.name),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "编辑云硬盘", "失败", role,
                    "{}:{},失败".format("编辑云硬盘", volumeeditform.get("name", ""))
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg": "error"}
        return res
    
    # @post(_path="/v1/volumes/action",_types=[VolumeActionFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/volumes/action",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_action(self, volumeactionform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 执行云硬盘动作
        requestBody:
          description: 云硬盘动作
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeActionModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = volumeactionform.get("id", "")
        name = volumeactionform.get("name", "")
        action = volumeactionform.get("action", "")
        volume_data = volumeactionform.get("data", "")

        if action == "extend":
            op = "调整大小"
        elif action == "retype":
            op = "改变类型"
        elif action == "upload":
            op = "云硬盘另存为镜像"        
        elif action == "detach":
            op = "分离云硬盘"
        
        try:
            client = Client()
            if action == "detach":
                data = volume_data.split("@")
                vm = client.NovaClient.openstack_detach_volume_from_server(client,data[1], id)
            res = client.VolumeClient.openstack_volume_action_v2(client,volumeactionform)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": op,
            #                  "object": volumeactionform.id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", op, "成功", role,
                "{}:{},成功".format(op, name)
            )
        
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": op,
                #              "object": volumeactionform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", op, "失败", role,
                    "{}:{},失败".format(op, name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"} 

        return res
    
    # @post(_path="/v1/volumes/snapshot",_types=[VolumeActionFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/volumes/snapshot",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_snapshot(self, volumeform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘快照
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeSnapshotModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = volumeform.get("id", "")
        name = volumeform.get("name", "")
        action = volumeform.get("action", "")
        if action == "create":
            op = "创建快照" 
        elif action == "backup":
            op = "创建备份"
        elif action == "edit":
            op = "编辑快照"
        elif action == "delete":
            op = "删除快照"
        elif action == "list":
            op = "查询快照"
            volumeform["id"] = ""
            
        try:
            client = Client()
            
            res = client.VolumeClient.openstack_volume_snapshot_v2(client, volumeform)
            
            
            
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": op,
            #                  "object": volumeform.id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "快照管理", op, "成功", role,
                "{}:{},成功".format(op, name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": op,
                #              "object": volumeform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "快照管理", op, "失败", role,
                    "{}:{},失败".format(op, name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"} 
        
        return res
    
    # @post(_path="/v1/volumes/backup",_types=[VolumeActionFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/volumes/backup", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_backup(self, volumeform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 创建云硬盘备份
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeBackupModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        name = volumeform.get("name", "")
        try:
            client = Client()
            res = client.VolumeClient.openstack_volume_backup(client,volumeform)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建云硬盘备份",
            #                  "object": volumeform.id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", "创建云硬盘备份", "成功", role,
                "{}:{},成功".format("创建云硬盘备份", name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "创建云硬盘备份",
                #              "object": volumeform.id,
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "创建云硬盘备份", "失败", role,
                    "{}:{},失败".format("创建云硬盘备份", name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"} 
        return res
    
    # @post(_path="/v1/volumes/attach",_types=[VolumeAttachFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/volumes/attach", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_attach_volume(self, volumeattachform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 连接云硬盘到指定的虚拟机
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeAttachModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        name = volumeattachform.get("name", "")
        vm_name = volumeattachform.get("vm_name", "")
        try:
            client = Client()
            #res = client.VolumeClient.openstack_attach_volume(client,volumeattachform)
            
            res = client.NovaClient.openstack_attach_volume_to_server_v2(client,volumeattachform)
            
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "连接云硬盘到指定的虚拟机",
            #                  "object": "%s %s" % (volumeattachform.id,volumeattachform.vmid),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", "挂载云硬盘", "失败", role,
                "{}:虚拟机:{},磁盘:{}失败".format("挂载云硬盘", vm_name, name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "连接云硬盘到指定的虚拟机",
                #              "object": "%s %s" % (volumeattachform.vmid, volumeattachform.id),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "挂载云硬盘", "失败", role,
                    "{}:虚拟机:{},磁盘:{}失败".format("挂载云硬盘", vm_name, name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"} 
        return res
    
    # @post(_path="/v1/volumes/detach",_types=[VolumeAttachFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/volumes/detach", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_detach_volume(self, volumeattachform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 分离云硬盘从指定的虚拟机
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeAttachModel'
          required: true
        responses:
          '200':
            description: 成功
            content: 
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeMsgModel'
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        vmid = volumeattachform.get("vmid","")
        id = volumeattachform.get("id", "")
        name = volumeattachform.get("name", "")
        vm_name = volumeattachform.get("vm_name", "")
        try:
            client = Client()
            #res = client.VolumeClient.openstack_attach_volume(client,volumeattachform)
            res = client.NovaClient.openstack_detach_volume_from_server(client, vmid, id)
            
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "分离云硬盘从指定的虚拟机",
            #                  "object": "%s %s" % (volumeattachform.id,volumeattachform.vmid),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "存储管理", "分离云硬盘", "成功", role,
                "{}:虚拟机:{},磁盘:{}成功".format("分离云硬盘", vm_name, name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "分离云硬盘从指定的虚拟机",
                #              "object": "%s %s" % (volumeattachform.vmid,volumeattachform.id),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "存储管理", "分离云硬盘", "失败", role,
                    "{}:虚拟机:{},磁盘:{}失败".format("分离云硬盘", vm_name, name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"} 
        return res
    
    @post(_path="/v1/volumes/detail", _types=[VolumeDeleteFrom],_consumes=mediatypes.APPLICATION_JSON,_produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_detail(self,volumeform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 查询云硬盘详情
        requestBody:
          description: 云硬盘id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeDeleteModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeDetailModel'
        """
        client = Client()
        res = client.VolumeClient.openstack_get_volume_detail(client,volumeform.id)
        
        if res["vmid"] != "":
            vm = client.NovaClient.openstack_getpost_server_detail(client,res["vmid"])
            res["vmname"] = vm["name"]
        else:
            res["vmname"] = ""
        return res  
    
    @get(_path="/v1/volumes/theasynservices", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_cinder_services(self):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 查询全部云硬盘服务
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayVolumeTypeModel'
        """
        client = Client()
        res = client.VolumeClient.openstack_get_all_cinder_services(client)
        return res

    @post(_path="/v1/volumes/bootable", _types=[VolumeDeleteFrom],_consumes=mediatypes.APPLICATION_JSON,_produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_detail(self,volumeform):
        """
        ---
        tags:
          - 云硬盘相关接口
        summary: 更新云硬盘可启动状态
        requestBody:
          description: 云硬盘id, 可启动状态bool
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumeDeleteModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/VolumeDetailModel'
        """
        client = Client()
        res = client.VolumeClient.openstack_update_volume_bootable_status(client, volumeform.id, volumeform.status)
        if res == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}




class PassthroughHandler(pyrestful.rest.RestHandler):
    
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        
    
    @get(_path="/v1/volumes/passthrough/all", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_passthrough_devices(self):

        client = Client()
        type_list = client.VolumeClient.openstack_get_all_cinder_volume_types(client)
        
        volume_list = client.VolumeClient.openstack_get_all_list(client)

        c = Pclient()
        passthrough = []
        nodedisk_dict = {}
        #download_bandwidth = c.query_vector_by_query("max(rate(node_network_receive_bytes_total[5m])")
        nodedisk_dict = c.query_vector_by_query("thxh_theexport_new_disk_info[1h]")
        for disk in nodedisk_dict:
            #passthrough.append(disk["metric"])
            
            passdisk = {}
            
            passdisk["hostname"] = disk["metric"]["hostName"]
            passdisk["hostip"] = disk["metric"]["instance"].split(":")[0]
            passdisk["device"] = "/dev/%s" % disk["metric"]["diskName"]
            passdisk["backend"] = "no"
            passdisk["have_volume"] = "no"
            
            for type in type_list:
                if type["name"] == "cinder-%s-%s" % (disk["metric"]["diskName"],disk["metric"]["hostName"]):
                     passdisk["backend"] = "yes"
                     
            for volume in volume_list:
                if volume["name"] == "cinder-%s-%s" % (disk["metric"]["diskName"],disk["metric"]["hostName"]):
                    passdisk["have_volume"] = "yes"
                
            passthrough.append(passdisk)
                
        return passthrough
    
    @get(_path="/v1/volumes/types/listall", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_cinder_types(self):

        client = Client()
        res = client.VolumeClient.openstack_get_all_cinder_volume_types(client)
        return res
    
    @post(_path="/v1/volumes/types/create",_consumes=mediatypes.APPLICATION_JSON,_produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_volume_detail(self,volumeform):

        client = Client()
        
        backend = "cinder-%s-%s" % (volumeform["extra_specs"].split("/")[-1],volumeform["extra_specs"].split("(")[0])
        volumeform["extra_specs"]  = backend
        res = client.VolumeClient.openstack_create_volume_type(client,volumeform)
        
        return res  
    
    @delete(_path="/v1/volumes/types/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_volume(self, volumeform):
        try:
            client = Client()
            #result = client.VolumeClient.openstack_volume_snapshot_list(client)
            res = client.VolumeClient.openstack_delete_volume_type(client, volumeform["id"]) 
            if res["msg"] == "snap":
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除云硬盘类型",
                             "object": volumeform["id"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
            else:
                logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除云硬盘类型",
                             "object": volumeform["id"],
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
   
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除云硬盘类型",
                             "object": volumeform["id"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                return {"msg":"error"} 
        return res
    
    @post(_path="/v1/volumes/passthrough/createconnect", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_passthrough_volume(self, form):
        client = Client()
        dev_host = form["device"].split("(")[0]
        dev_name = form["device"].split("/")[-1]
        type_name = "cinder-%s-%s" % (dev_name,dev_host)
        have_type= False
        extra_specs = ""
        volumeattachform ,passthrough,create_type_form,create_type= {},{},{},{}
        passthrough["have_volume"] = False
        
        volume_list = client.VolumeClient.openstack_get_all_list(client)
        print("--------------")
        print("volume_list:", volume_list)
        print("--------------")
        for volume in volume_list:
            if volume["name"] == type_name :
                passthrough["have_volume"] = True
                passthrough["volumeid"] = volume["id"]
                passthrough["volumestatus"] = volume["status"]
                passthrough["description"] = volume["description"]
        print("--------------")
        print("passthrough:", passthrough)
        print("--------------")
        if passthrough["have_volume"]:
            if passthrough["volumestatus"] == "available" and passthrough["description"] == "thxh-passthrough":
                volumeattachform = VolumeAttachFrom()
                volumeattachform.id = passthrough["volumeid"]
                volumeattachform.vmid = form["vmid"] 
            
                attach_res = client.NovaClient.openstack_attach_volume_to_server(client,volumeattachform)
                return {"msg": "ok"}
            elif passthrough["volumestatus"] != "available" and passthrough["description"] == "thxh-passthrough":
                return {"msg": "error","type": "该磁盘设备已经被占用" } 
                     
        type_list = client.VolumeClient.openstack_get_all_cinder_volume_types(client)
        for type in type_list:
            if type["name"] == type_name :
                have_type = True
                
        if not have_type:
            create_type_form["name"] = type_name
            create_type_form["extra_specs"] = type_name
            create_type_form["description"] = ""
            
            create_type = client.VolumeClient.openstack_create_volume_type(client,create_type_form)
        else:
            create_type["msg"] = "ok"
            
        if len(create_type) > 0:
            if create_type["msg"] == "ok":
                create_volume = client.VolumeClient.openstack_create_volume_by_type(client,type_name,10)
            else:
                return {"msg":"error","type":"创建云硬盘失败"}
        else:
            return {"msg":"error","type":"创建云硬盘类型失败"}
    
        if create_volume["volume_status"] == "creating" :
            while create_volume["volume_status"] != "available" :
                create_volume = client.VolumeClient.openstack_get_volume_detail(client,create_volume["volumeid"])
                create_volume["volume_status"] = create_volume["status"]
                create_volume["volumeid"] = create_volume["id"]
                if create_volume["volume_status"] == "error" :
                    break
        
        volumeattachform = VolumeAttachFrom()
        volumeattachform.id = create_volume["volumeid"] 
        volumeattachform.vmid = form["vmid"] 
            
        attach_res = client.NovaClient.openstack_attach_volume_to_server(client,volumeattachform)
        

            
        '''
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云硬盘",
                             "object": form.get("name", "") ,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云硬盘",
                             "object": form.get("name", ""),
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                return {"msg":"error"}'''
        return attach_res
    
    @get(_path="/v1/volumes/passthrough/connect", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_cinder_passthrough_connect(self):

        client = Client()
        res = client.VolumeClient.openstack_get_all_passthrough(client)
        for passthrough in res:
            if len(passthrough["attachments"]) > 0:
                passthrough["vmid"] = passthrough["attachments"][0]["server_id"]
                passthrough["device"] = passthrough["attachments"][0]["device"]
                vmdetail = client.NovaClient.openstack_getpost_server_detail(client, passthrough["attachments"][0]["server_id"])
                passthrough["vmname"] = vmdetail["name"]
                passthrough["ip"] = list(vmdetail["addresses"].values())[0][0]["addr"]
            else:
                res.remove(passthrough)
            
        return res
    
    @delete(_path="/v1/volumes/passthrough/deleteconnect",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_passthrough_connect(self, form):

        client = Client()
        detach = {}
        volume_detail = client.VolumeClient.openstack_get_volume_detail(client,form["id"])
        if volume_detail["status"] == "in-use":
            detach = client.NovaClient.openstack_detach_volume_from_server(client,volume_detail["vmid"],volume_detail["id"])
            volume_detail = client.VolumeClient.openstack_get_volume_detail(client,form["id"])
            
        #elif volume_detail["status"] == "available":
        #    pass
        else:
            detach["msg"] = "ok"
        
        if volume_detail["status"] == "detaching" :
            while volume_detail["status"] != "available" :
                volume_detail = client.VolumeClient.openstack_get_volume_detail(client,form["id"])

                if volume_detail["status"] == "error" :
                    break
  
        
        if volume_detail["status"] == "available":
            delete = client.VolumeClient.openstack_delete_volume(client, volume_detail["id"]) 
        else:
            return {"msg": "error","type":"卸载云硬盘失败"}
        
            #res = client.VolumeClient.openstack_delete_volume(client, form["id"]) 
        '''if res["msg"] == "snap":
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除直通设备连接",
                             "object": form["id"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
            else:
                logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除直通设备连接",
                             "object": form["id"],
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
   
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除直通设备连接",
                             "object": form["id"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                return {"msg":"error"} '''
        return delete



