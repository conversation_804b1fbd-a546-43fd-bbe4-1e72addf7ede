# -*- coding: utf-8 -*-
import traceback

import tornado.ioloop
import pyrestful.rest
from api.log.log import CustomLogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete


from model.network import *
from util.cov import todict, serialize, is_valid_ip
from api.openstack.client import Client
from api.prometheus.client import Client as Pclient
from db.model.network import Network

import logging
from model import network
logger = logging.getLogger(__name__)
new_logger = CustomLogger()

class NetworksHandler(pyrestful.rest.RestHandler):

    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/networks",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_networks(self):
        """
        ---
        tags:
          - 网络相关接口
        summary: 查询全部网络  
        responses:
          '200':
            description: 查询成功
            content: {}
        """
        client = Client()
        res = client.NeutronClient.openstack_get_all_list(client)
        for network in res:
            network["cidr"] = ""
            for subnet in network["subnets"]:
                subnet_detail = client.NeutronClient.openstack_get_subnet_detail(client, subnet)

                network["cidr"] = "%s%s:%s" % (network["cidr"] ,subnet_detail["name"],subnet_detail["cidr"])
            network["cidr"] = network["cidr"].rstrip(',')
        return res

    @get(_path="/v2/networks",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_networks_detail(self):
        """
        ---
        tags:
          - 网络相关接口
        summary: 查询全部网络
        responses:
          '200':
            description: 查询成功
            content: {}
        """
        client = Client()
        res = client.NeutronClient.openstack_get_all_list(client)
        if res:
            for network in res:
                network["cidr"] = ""
                for subnet in network["subnets"]:
                    subnet_detail = client.NeutronClient.openstack_get_subnet_detail(client, subnet)

                    network["cidr"] = "%s%s:%s" % (network["cidr"], subnet_detail["name"],subnet_detail["cidr"])
                    network["dns_nameservers"] = subnet_detail.get("dns_nameservers", "")
                    network["allocation_pools"] = subnet_detail.get("allocation_pools", [])
                    network["host_routes"] = subnet_detail.get("host_routes", "")
                    network["gateway_ip"] = subnet_detail.get("gateway_ip", "")

            network["cidr"] = network["cidr"].rstrip(',')

            sorted_data = sorted(res, key=lambda x: x['created_at'], reverse=True)

            return sorted_data
        else:
            return res

    @get(_path="/v3/networks", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_networks_detail_v3(self):
            client = Client()
            res = client.NeutronClient.openstack_get_all_list(client)
            if res:
                for network in res:
                    cidr_list = []
                    gateway_ip_list = []
                    dns_nameservers_list = []
                    allocation_pools_list = []
                    host_routes_list = []
                    for subnet in network["subnets"]:
                        subnet_detail = client.NeutronClient.openstack_get_subnet_detail(client, subnet)
                        cidr_list.append(f"{subnet_detail['name']}:{subnet_detail['cidr']}")
                        gateway_ip_list.append(subnet_detail.get("gateway_ip", ""))
                        dns_nameservers_list.append(subnet_detail.get("dns_nameservers", ""))
                        allocation_pools_list.append(subnet_detail.get("allocation_pools", "")[0])
                        host_routes_list.append(subnet_detail.get("host_routes", ""))
                    network["cidr"] = cidr_list
                    network["gateway_ip"] = gateway_ip_list
                    network["dns_nameservers"] = dns_nameservers_list
                    network["allocation_pools"] = allocation_pools_list
                    network["host_routes"] = host_routes_list
                sorted_data = sorted(res, key=lambda x: x['created_at'], reverse=True)
                return sorted_data
            else:
                return res



    @post(_path="/v1/create/networks",_types=[NetworkCreateForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_networks(self, networkcreateform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 创建网络
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateNetworkModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")
        try:
            client = Client()
            network_meta = client.NeutronClient.openstack_create_network(client, networkcreateform)
            network_id = network_meta["id"]
            network_meta = client.NeutronClient.openstack_create_subnet(client, network_id, networkcreateform)
        except Exception as e:
                username = self.get_cookie('username', "")
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建网络",
                             "object": network_id,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "网络管理", "创建网络", "失败", role, "{}:{},失败".format(role, "创建网络", network_id)
                )
                return {"msg":"error:"+e}
        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": self.get_cookie('username', ""),
                     "op": "创建网络",
                     "object": network_id,
                     "role": self.get_cookie('role', ""),
                     "result": "成功",
                     })
        new_logger.log(
            self.username, "网络管理", "创建网络", "成功", role, "{}:{},成功".format("创建网络", network_id)
        )
        return {"msg":"ok"}

    @post(_path="/v1/networks/edit/subnet",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON) #_types=[NetworkEditForm],
    def thecloud_post_edit_networks_subnet(self, networkeditform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 编辑网络
        requestBody:
          description: 编辑
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestEditNetworkModel'
          required: true
        responses:
          '200':
            description: 编辑成功
            content: {}
        """
        role = self.get_cookie('role', "")
        name = networkeditform.get('name', "")
        try:
            client = Client()
            networkeditform.pop("name", None)
            network_meta = client.NeutronClient.openstack_put_subnet(client, networkeditform)
        except Exception as e:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #              {"username": self.get_cookie('username', ""),
            #               "op": "更新网络",
            #               "role": self.get_cookie('role', ""),
            #               "result": "失败",
            #               })
            new_logger.log(
                self.username, "网络管理", "修改子网", "失败", role, "{}:{},失败".format("修改子网", name)
            )
            self.set_status(502)
            return {"msg": "error"}
        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
        #             {"username": self.get_cookie('username', ""),
        #              "op": "更新网络",
        #              "role": self.get_cookie('role', ""),
        #              "result": "成功",
        #              })
        new_logger.log(
            self.username, "网络管理", "修改子网", "成功", role, "{}:{},成功".format("修改子网", name)
        )
        return {"msg": "ok"}

    @post(_path="/v1/networks/create/subnet",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_subnet(self, form):

        role = self.get_cookie('role', "")
        adminnetworkcreateform  = AdminNetworkCreateForm()
        adminnetworkcreateform.name = form.get("name", "")
        adminnetworkcreateform.enable_dhcp = form.get("enable_dhcp", "")
        adminnetworkcreateform.cidr = form.get("cidr", "")
        adminnetworkcreateform.gateway_ip = form.get("gateway_ip", "")
        adminnetworkcreateform.startip = form.get("startip", "")
        adminnetworkcreateform.endip = form.get("endip", "")
        adminnetworkcreateform.dns_nameservers = form.get("dns_nameservers", "")

        if adminnetworkcreateform.cidr != "" and \
                not is_valid_ip(adminnetworkcreateform.cidr.split("/")[0]):
            return {"msg":"子网格式验证失败。"}
        if adminnetworkcreateform.dns_nameservers != "" and \
                not is_valid_ip(adminnetworkcreateform.dns_nameservers):
            return {"msg":"DNS格式验证失败。"}
        if not is_valid_ip(adminnetworkcreateform.gateway_ip):
            return {"msg":"网关地址格式验证失败。"}

        if adminnetworkcreateform.enable_dhcp:
            if not is_valid_ip(adminnetworkcreateform.startip) and \
                    not is_valid_ip(adminnetworkcreateform.endip):
                return {"msg":"DHCP格式验证失败。"}

        host_routes = form.get("host_routes", "")
        if host_routes:
            for route in host_routes:
                if not is_valid_ip(route["destination"].split("/")[0]) and \
                        not is_valid_ip(route["nexthop"]):
                    return {"msg":"主机路由格式验证失败。"}

        try:
            client = Client()

            network_id = form["id"]

            if adminnetworkcreateform.enable_dhcp is True:
                network_meta = client.NeutronClient.openstack_create_subnet_withdhcp(client, network_id, form)
            else:
                network_meta = client.NeutronClient.openstack_create_subnet(client, network_id, form)
            if network_meta["msg"] == "409":
                res = client.NeutronClient.openstack_delete_network(client, network_id)
                network_meta = {"msg": "网络创建失败！"}
        except Exception as e:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #              {"username": self.get_cookie('username', ""),
            #               "op": "创建admin网络",
            #               "object": adminnetworkcreateform.name,
            #               "role": self.get_cookie('role', ""),
            #               "result": "失败",
            #               })
            new_logger.log(
                self.username, "网络管理", "创建子网", "失败", role, "{}:{},失败".format("创建子网", adminnetworkcreateform.name)
            )
            self.set_status(502)
            return {"msg": "error"}
        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
        #             {"username": self.get_cookie('username', ""),
        #              "op": "创建admin网络",
        #              "object": adminnetworkcreateform.name,
        #              "role": self.get_cookie('role', ""),
        #              "result": "成功",
        #              })
        new_logger.log(
            self.username, "网络管理", "创建子网", "成功", role, "{}:{},成功".format("创建子网", adminnetworkcreateform.name)
        )
        return network_meta

    @post(_path="/v1/networks/delete/subnet", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON) #_types=[NetworkEditForm],
    def thecloud_post_delete_subnet(self, subnet):
        role = self.get_cookie('role', "")
        try:
            client = Client()
            network_meta = client.NeutronClient.openstack_delete_subnetid(client, subnet["subnet_id"])
            if network_meta["msg"] != "ok":
                new_logger.log(
                    self.username, "网络管理", "删除子网", "失败", role, "{}:{},失败".format("删除子网", subnet["name"])
                )
                return network_meta
        except Exception as e:

            new_logger.log(
                self.username, "网络管理", "删除子网", "失败", role, "{}:{},失败".format("删除子网", subnet["name"])
            )
            self.set_status(502)
            return {"msg": "error"}

        new_logger.log(
            self.username, "网络管理", "删除子网", "成功", role, "{}:{},成功".format("删除子网", subnet["name"])
        )

        return {"msg": "ok"}

    @post(_path="/v1/networks/edit",_types=[NetworkEditForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_edit_networks(self, networkeditform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 编辑网络
        requestBody:
          description: 编辑
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestEditNetworkModel'
          required: true
        responses:
          '200':
            description: 编辑成功
            content: {}
        """
        role = self.get_cookie('role', "")
        try:
            client = Client()
            network_meta = client.NeutronClient.openstack_edit_network(client, networkeditform)
        except Exception as e:
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "更新网络",
                             "object": networkeditform.id,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "网络管理", "更新网络", "失败", role, "{}:{},失败".format("更新网络", networkeditform.id)
                )
                return {"msg":"error"}
        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": self.get_cookie('username', ""),
                     "op": "更新网络",
                     "object": networkeditform.id,
                     "role": self.get_cookie('role', ""),
                     "result": "成功",
                     })
        new_logger.log(
            self.username, "网络管理", "更新网络", "成功", role, "{}:{},成功".format("更新网络", networkeditform.id)
        )
        
        
        return {"msg":"ok"}

    @post(_path="/v2/networks/edit", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_edit_networks_v2(self, networkeditform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 编辑网络
        requestBody:
          description: 编辑
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestEditNetworkModel'
          required: true
        responses:
          '200':
            description: 编辑成功
            content: {}
        """
        role = self.get_cookie('role', "")
        id = networkeditform.get("id", "")
        name = networkeditform.get("name", "")
        try:
            client = Client()
            network_meta = client.NeutronClient.openstack_edit_network_v2(client, networkeditform)

            network_detail = client.NeutronClient.openstack_get_network_detail(client, id)
            if 'subnets' in network_detail['network']:
                if network_detail['network']["subnets"]:
                    for subnet_id in network_detail['network']["subnets"]:
                        subnetform = {
                            "subnet_id": subnet_id,
                            "name": name
                        }
                        subnet_meta = client.NeutronClient.openstack_put_subnet(client, subnetform)

        except Exception as e:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #              {"username": self.get_cookie('username', ""),
            #               "op": "更新网络",
            #               "object": networkeditform.id,
            #               "role": self.get_cookie('role', ""),
            #               "result": "失败",
            #               })
            new_logger.log(
                self.username, "网络管理", "更新网络", "失败", role, "{}:{},失败".format("更新网络", name)
            )
            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
        #             {"username": self.get_cookie('username', ""),
        #              "op": "更新网络",
        #              "object": networkeditform.id,
        #              "role": self.get_cookie('role', ""),
        #              "result": "成功",
        #              })
        new_logger.log(
            self.username, "网络管理", "更新网络", "成功", role, "{}:{},成功".format("更新网络", name)
        )

        return {"msg": "ok"}
    
    #@post(_path="/v1/create/adminnetworks",_types=[AdminNetworkCreateForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    #def thecloud_post_create_adminnetworks(self, adminnetworkcreateform):
    @post(_path="/v1/create/adminnetworks",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_adminnetworks(self, form):
        """
        ---
        tags:
          - 网络相关接口
        summary: 创建网络
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateAdminNetworkModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")

        adminnetworkcreateform  = AdminNetworkCreateForm()
        adminnetworkcreateform.network_type = form.get("network_type", "")
        adminnetworkcreateform.name = form.get("name", "")
        adminnetworkcreateform.segmentation_id = form.get("segmentation_id", "")
        adminnetworkcreateform.enable_dhcp = form.get("enable_dhcp", "")
        adminnetworkcreateform.cidr = form.get("cidr", "")
        adminnetworkcreateform.gateway_ip = form.get("gateway_ip", "")
        adminnetworkcreateform.startip = form.get("startip", "")
        adminnetworkcreateform.endip = form.get("endip", "")
        adminnetworkcreateform.dns_nameservers = form.get("dns_nameservers", "")

        if adminnetworkcreateform.cidr != "" and \
           not is_valid_ip(adminnetworkcreateform.cidr.split("/")[0]):
            return {"msg":"子网格式验证失败。"}
        if adminnetworkcreateform.dns_nameservers != "" and \
           not is_valid_ip(adminnetworkcreateform.dns_nameservers):
            return {"msg":"DNS格式验证失败。"}
        if not is_valid_ip(adminnetworkcreateform.gateway_ip):
            return {"msg":"网关地址格式验证失败。"}

        if adminnetworkcreateform.enable_dhcp:
            if not is_valid_ip(adminnetworkcreateform.startip) and \
               not is_valid_ip(adminnetworkcreateform.endip):
                return {"msg":"DHCP格式验证失败。"}

        host_routes = form.get("host_routes", "")
        if host_routes:
            for route in host_routes:
                if not is_valid_ip(route["destination"].split("/")[0]) and \
                   not is_valid_ip(route["nexthop"]):
                    return {"msg":"主机路由格式验证失败。"}

        try:
            client = Client()
            if adminnetworkcreateform.network_type == "flat":
                networks = client.NeutronClient.openstack_get_all_list(client)
                for network in networks:
                    if network["network_type"] == "flat":
                        return {"msg":"已存在flat类型网络，无法再次创建"}
                res = client.NeutronClient.openstack_create_flatnetwork(client, adminnetworkcreateform)
            elif adminnetworkcreateform.network_type == "vlan":
                res = client.NeutronClient.openstack_create_vlannetwork(client, adminnetworkcreateform)

            network_id = res["id"]

            if adminnetworkcreateform.enable_dhcp == True:
                network_meta = client.NeutronClient.openstack_create_subnet_withdhcp(client, network_id, form)
            elif adminnetworkcreateform.enable_dhcp == False:
                network_meta = client.NeutronClient.openstack_create_subnet(client, network_id, form)

            if network_meta["msg"] == "409":
                res = client.NeutronClient.openstack_delete_network(client, network_id)
                network_meta = {"msg":"网络创建失败！"}

        except Exception as e:



                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "创建admin网络",
                #              "object": adminnetworkcreateform.name,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "网络管理", "创建网络", "失败", role, "{}:{},失败".format("创建admin网络", adminnetworkcreateform.name)
                )
                self.set_status(502)
                return {"msg":"error"}
        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
        #             {"username": self.get_cookie('username', ""),
        #              "op": "创建admin网络",
        #              "object": adminnetworkcreateform.name,
        #              "role": self.get_cookie('role', ""),
        #              "result": "成功",
        #              })
        new_logger.log(
            self.username, "网络管理", "创建网络", "成功", role, "{}:{},成功".format("创建admin网络", adminnetworkcreateform.name)
        )

        return network_meta

    @delete(_path="/v1/networks/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_networks(self, networkdeleteform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 删除网络  URL参数： 网络id
        responses:
          '204':
            description: 删除成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/NetworkDeleteModel'
        """
        role = self.get_cookie('role', "")
        try:
            client = Client()
            res = client.NeutronClient.openstack_delete_network(client, networkdeleteform["id"])
        except Exception as e:
                traceback.print_exc()

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除网络",
                #              "object": networkdeleteform.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "网络管理", "删除网络", "失败", role, "{}:{},失败".format("删除网络", networkdeleteform["name"])
                )
                self.set_status(502)
                return {"msg": "error"}
        if res["msg"] == "ok":
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #             {"username": self.get_cookie('username', ""),
            #              "op": "删除网络",
            #              "object": networkdeleteform.id,
            #              "role": self.get_cookie('role', ""),
            #              "result": "成功",
            #              })
            new_logger.log(
                self.username, "网络管理", "删除网络", "成功", role, "{}:{},成功".format("删除网络", networkdeleteform["name"])
            )
        else:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #              {"username": self.get_cookie('username', ""),
            #               "op": "删除网络",
            #               "object": networkdeleteform.id,
            #               "role": self.get_cookie('role', ""),
            #               "result": "失败",
            #               })
            new_logger.log(
                self.username, "网络管理", "删除网络", "失败", role, "{}:{},失败".format("删除网络", networkdeleteform["name"])
            )
            # self.set_status(502)
        return res

    @get(_path="/v1/networks/physical",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_networks_physical(self):
        """
        ---
        tags:
          - 网络相关接口
        summary: 查询物理网络  
        responses:
          '200':
            description: 查询成功
            content: {}
        """
        with self.session_scope() as session:
            query = session.query(Network)
        res = query.all()
        r  = []
        for info in res:
            d = serialize(info)
            r.append(d)
        return r

    @post(_path="/v1/create/physical/networks",_types=[NetworkCreatePhyicalForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_phyical_networks(self, networkform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 添加物理网络信息
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreatePhyicalNetworkModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        network = Network()
        network.name = networkform.name
        network.cidr = networkform.cidr
        network.vlanid = networkform.vlanid
        network.bridge = networkform.bridge
        network.bonding = networkform.bonding
        network.devinfo = networkform.devinfo
        network.speed = networkform.speed
        network.type = networkform.type
        try:
            with self.session_scope() as session:
                session.add(network)
                session.commit()
        except Exception:
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": self.get_cookie('username', ""),
                         "op": "添加物理网络信息",
                         "object": networkform.name,
                         "role": self.get_cookie('role', ""),
                         "result": "失败",
                         })
            return {"msg":"error"}
        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
        #             {"username": self.get_cookie('username', ""),
        #              "op": "添加物理网络信息",
        #              "object": networkform.name,
        #              "role": self.get_cookie('role', ""),
        #              "result": "成功",
        #              })
        return {"msg":"ok"}

    @delete(_path="/v1/physical/networks/delete",_types=[NetworkDeleteFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_physical_network(self, networkdeletefrom):
        """
        ---
        tags:
          - 网络相关接口
        summary: 删除物理网络  
        requestBody:
          description: 删除
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkDeleteModel'
          required: true
        responses:
          '200':
            description: 删除成功
            content: {}
        """
        try:
            with self.session_scope() as session:
                row = session.query(Network).filter_by(id=networkdeletefrom.id)[0]
                session.delete(row)
                session.commit()
        except Exception:
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": self.get_cookie('username', ""),
                         "op": "删除物理网络",
                         "object": networkdeletefrom.name,
                         "role": self.get_cookie('role', ""),
                         "result": "失败",
                         })
            return {"msg":"error"}

        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": self.get_cookie('username', ""),
                     "op": "删除物理网络",
                     "object": networkdeletefrom.name,
                     "role": self.get_cookie('role', ""),
                     "result": "成功",
                     })

        return {"msg":"ok"}

    @put(_path="/v1/physical/networks/update",_types=[NetworkUpdatePhyicalForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_update_physical_network(self, networkform):
        """
        ---
        tags:
          - 网络相关接口
        summary: 编辑物理网络  
        requestBody:
          description: 编辑
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestUpdatePhyicalNetworkModel'
          required: true
        responses:
          '200':
            description: 修改成功
            content: {}
        """

        data = {}
        if networkform.name:
            data["name"] =  networkform.name
        if networkform.cidr:
            data["cidr"] = networkform.cidr
        if networkform.vlanid:
            data["vlanid"] = networkform.vlanid
        if networkform.bridge:
            data["bridge"] = networkform.bridge
        if networkform.bonding:
            data["bonding"] = networkform.bonding
        if networkform.devinfo:
            data["devinfo"] = networkform.devinfo
        if networkform.speed:
            data["speed"] = networkform.speed
        if networkform.type:
            data["type"] = networkform.type
        try:
            with self.session_scope() as session:
                session.query(Network).filter_by(id=networkform.id).update(data)
                session.commit()
        except Exception:
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": self.get_cookie('username', ""),
                         "op": "编辑物理网络",
                         "object": networkform.name,
                         "role": self.get_cookie('role', ""),
                         "result": "失败",
                         })
            return {"msg":"error"}

        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": self.get_cookie('username', ""),
                     "op": "编辑物理网络",
                     "object": networkform.name,
                     "role": self.get_cookie('role', ""),
                     "result": "成功",
                     })

        return {"msg":"ok"}

    @get(_path="/v1/networks/agents",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_agent_list(self):
        """
        ---
        tags:
          - 网络相关接口
        summary: 查询全部代理  
        responses:
          '200':
            description: 查询成功
            content: {}
        """
        client = Client()
        res = client.NeutronClient.openstack_get_all_agent_list(client)

        return res

    @get(_path="/v1/networks/topology/manage",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_topology_network_manage(self):
        class graphList:
            def __init__(self):
                self.dict={}

            def addVertex(self,key):
                self.dict[key]=[]

            # 添加有向边
            def addDirectLine(self,start,ends):
                for row in ends:
                    key=row[0]

                    # 1.在start的邻接表中追加新的边和权值
                    lst=self.dict[start]
                    flag=True

                    for k in lst:
                        if k[0]==key:
                            flag=False

                    if flag:
                        self.dict[start].append(row)


        def find_bridge_device_node(res, ids, network_device_node, gl, c):
            device_nodes = {}
            for net_info in res:
                metric = net_info.get("metric", {})
                device_name = metric.get("device")
                bridge = metric.get("bridge")
                ip = metric.get("instance").split(":")[0]
                state = metric.get("bridge_flag").split("|")[0]
                value = net_info.get("value", [0,"0"])[1]



                if not device_nodes.get(ip, {}):
                    parm = 'node_network_up{ hypervisor_state="up",hypervisor_host_ip="%s", device="%s"}' % (ip, bridge)
                    my_status = c.query_vector_by_query(parm)
                    if len(my_status) == 1:
                        if my_status[0]["value"][1] == "1":
                            state = "up"
                        else:
                            state = "down"
                    else:
                        state = "down"
                    id = get_id(ids)
                    ids = id
                    info = {
                        "label":bridge,
                        "ltype":"网桥",
                        "ip": ip,
                        "id":str(id),
                        "state": state,
                        "class": "c2%s" % state
                    }
                    gl.addVertex(str(id))
                    device_nodes[ip] = info

            for ip, net_info in network_device_node.items():
                start = net_info.get("id")

                info = device_nodes.get(ip)
                end = []
                end.append([info.get("id"), info])
                gl.addDirectLine(start, end)

            return device_nodes, ids, gl


        def find_network_device_node(res ,ids,  root_node, gl,  c):
            start = root_node.get("id")
            end = []
            device_nodes = {}
            for net_info in res:
                metric = net_info.get("metric", {})
                device_name = metric.get("device")
                ip = metric.get("instance").split(":")[0]
                speed = metric.get("speed")

                if not ("vnet" in device_name):
                    if not device_nodes.get(ip, {}):
                        parm = 'node_network_up{ hypervisor_state="up", hypervisor_host_ip="%s", device="%s"}' % (ip, device_name)
                        my_status = c.query_vector_by_query(parm)
                        if len(my_status) == 1:
                            if my_status[0]["value"][1] == "1":
                                state = "up"
                            else:
                                state = "down"
                        else:
                            state = "down"
                        id = get_id(ids)
                        ids = id
                        info = {
                            "label":"%s %s" % (ip, device_name),
                            "ltype":"物理网卡",
                            "ip": ip,
                            "id": str(id),
                            "state":state,
                            "speed":speed,
                            "class": "c1%s" % state
                        }

                        device_nodes[ip] = info

                        gl.addVertex(str(id))
                        end.append([str(id), info])
            gl.addDirectLine(start, end)
            return device_nodes, ids, gl


        def find_vnet_device_node(res, ids, bridge_devices_node, gl):
            device_nodes = {}
            result = []
            for net_info in res:
                metric = net_info.get("metric", {})
                device_name = metric.get("device")
                ip = metric.get("instance").split(":")[0]
                state = "up"
                if "vnet" in device_name:
                    id = get_id(ids)
                    ids = id
                    info = {
                        "label":device_name,
                        "ltype":"虚拟网卡",
                        "ip": ip,
                        "id": str(id),
                        "state":state,
                        "class": "c3%s" % state
                    }
                    if not device_nodes.get(ip, []):
                        device_nodes[ip] = []

                    gl.addVertex(str(id))
                    device_nodes[ip].append(info)

            for ip, bridge_info in bridge_devices_node.items():
                start = bridge_info.get("id")
                info_list = device_nodes.get(ip)

                end = []
                if info_list:
                    for info in info_list:
                        end.append([info.get("id"), info])

                gl.addDirectLine(start, end)

            return device_nodes, ids, gl


        def get_id(id):
            return id+1

        c = Pclient()
        res = c.query_vector_by_query_parm("default_gateway_device")
        nodes = []
        edges = []

        gl = graphList()
        ids = 0
        root_node = { "id": "0", "label": "物理交换机",   "class": "c0" }
        gl.addVertex("0")
        nodes.append(root_node)
        print("交换机")
        print(root_node)
        network_device_node, ids, gl = find_network_device_node(res, ids, root_node, gl, c)
        print("物理网卡：")
        for ip, info in network_device_node.items():
            print(info)
            nodes.append(info)


        bridge_devices_node, ids, gl = find_bridge_device_node(res, ids, network_device_node, gl, c)
        print("网桥：")
        for ip, info in bridge_devices_node.items():
            print(info)
            nodes.append(info)
            print(bridge_devices_node)

        vnet_devices_node, ids, gl= find_vnet_device_node(res, ids, bridge_devices_node, gl)
        print("虚拟网卡")
        for ip, info_list in vnet_devices_node.items():
            for info in info_list:
                print(info)
                nodes.append(info)

        for node in nodes:
            print(node)

        for key in gl.dict:
            #print(key,'-->',gl.dict[key])
            for info in gl.dict[key]:
                line = {
                    "source": key,
                    "target": info[0],
                    "label":""
                }
                edges.append(line)

        for line in edges:
            print(line)

        result = {"nodes":nodes, "edges":edges}

        return result

    @get(_path="/v1/networks/topology/work",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_topology_network_work(self):
        class graphList:
            def __init__(self):
                self.dict={}

            def addVertex(self,key):
                self.dict[key]=[]

            # 添加有向边
            def addDirectLine(self,start,ends):
                for row in ends:
                    key=row[0]

                    # 1.在start的邻接表中追加新的边和权值
                    lst=self.dict[start]
                    flag=True

                    for k in lst:
                        if k[0]==key:
                            flag=False

                    if flag:
                        self.dict[start].append(row)




        nodes = []
        edges = []

        gl = graphList()
        ids = 0
        root_node = { "id": "root", "label": "集群",   "class": "c0" }
        gl.addVertex("root")
        nodes.append(root_node)

        client = Client()
        sub_net_list = client.NeutronClient.openstack_get_all_network_for_topology(client)

        start1 = "root"
        end1 = []
        for sub_net in sub_net_list:
            # sub_net["label"] = sub_net["cidr"] + sub_net["name"]
            sub_net["label"] = sub_net["name"]
            sub_net["id"] = sub_net["network_id"]

            nodes.append(sub_net)
            gl.addVertex(sub_net["network_id"])
            end1.append([sub_net["network_id"], sub_net])

        gl.addDirectLine(start1, end1)

        for sub_net in sub_net_list:
            start2 = sub_net["id"]

            vm_list = client.NeutronClient.openstack_get_vm_by_network_id_for_topology(client, start2)
            end2 = []
            for vm in vm_list:

                print(vm)
                vm["label"] = vm["ip"]
                nodes.append(vm)
                gl.addVertex(vm["id"])
                end2.append([vm["id"], vm])
            gl.addDirectLine(start2, end2)

        for key in gl.dict:
            #print(key,'-->',gl.dict[key])
            for info in gl.dict[key]:
                line = {
                    "source": key,
                    "target": info[0],
                    "label":""
                }
                edges.append(line)

        # for line in edges:
        #     print(line)

        result = {"nodes":nodes, "edges":edges}

        return result


    @get(_path="/v2/networks/topology/host",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_topology_network_host_v2(self):
        netclient   = Client()
        promeclient = Pclient()

        net_inter   = netclient.NeutronClient.openstack_network_interface_mappings(netclient)
        net_status  = promeclient.HostClient.get_host_network_status(promeclient)

        for ins_dict in net_inter:
            device = net_status.get(ins_dict["device"] + ins_dict["ipaddress"])
            if device:
                ins_dict["net_status"] = device["status"]

        return net_inter

    @post(_path="/v2/networks/topology/work",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_topology_network_work_v2(self, hostform):
        netclient = Client()
        hostname  = hostform.get("hostname")
        subnets   = netclient.NeutronClient.openstack_get_all_network_for_topology(netclient)
        node_vms  = netclient.NeutronClient.openstack_get_vm_for_topology(netclient, hostname)

        for subnet in subnets:
            vms = node_vms.get(subnet["subname"])
            if vms:
                subnet["vms"] = vms

        return subnets

    @post(_path="/v1/networks/ports/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_port(self, form):

        client = Client()

        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,form.get("networkid", "") )

        port = client.NeutronClient.openstack_create_port(client,form.get("networkid", ""),form.get("ipv4", ""),subnet_id)

        return port

    @post(_path="/v2/networks/ports/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_port_v2(self, form):

        client = Client()

        # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,form.get("networkid", "") )
        subnet_id = form.get("subnet_id", "")

        port = client.NeutronClient.openstack_create_port(client,form.get("networkid", ""),form.get("ipv4", ""),subnet_id)

        return port

    @get(_path="/v1/networks/qos/policies",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_qos_policies(self):

        client = Client()
        res = client.NeutronClient.openstack_get_qos_policies(client)

        return res

    @post(_path="/v1/networks/qos/createpolicy",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_qos_policy(self, form):

        client = Client()
        policy = client.NeutronClient.openstack_create_qos_policy(client,form.get("name", ""))
        rule = client.NeutronClient.openstack_create_bandwidth_limit_rule(client,policy["id"],form["bandwidth"],form["direction"])
        return rule

    @delete(_path="/v1/networks/qos/delete_rule_and_policy",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_qos_rule_and_policy(self, form):

        client = Client()
        if len(form["rule_id"]) > 0:
            res1 = client.NeutronClient.openstack_delete_qos_bandwidth_rule(client, form["policy_id"], form["rule_id"])

        res = client.NeutronClient.openstack_delete_qos_policy(client, form["policy_id"])
        return res

    @post(_path="/v1/networks/ports/update",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_update_port(self, form):

        client = Client()

        port = client.NeutronClient.openstack_update_port(client,form.get("portid", ""))

        return port

