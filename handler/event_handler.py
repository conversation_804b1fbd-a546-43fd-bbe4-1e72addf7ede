# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
import datetime
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from util.cov import todict, serialize, theevent
from api.openstack.client import Client
from db.model.user import User
from db.model.event import Event
from model.event import EventForm
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

class EventHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/event/query", _types=[EventForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_query_event(self, form):
        """
        ---
        tags:
          - 事件相关接口
        summary: 查询事件
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/EventListModel'
        """
        
        with self.session_scope() as session:
            query = session.query(Event)
        if hasattr(form, "username"):
            query = query.filter(Event.username==form.username)
        if hasattr(form, "level"):
            query = query.filter(Event.level==form.level)
        if hasattr(form, "op"):
            query = query.filter(Event.op==form.op)
        if hasattr(form, "start_date"):
            start_date = datetime.datetime.strptime(form.start_date, "%Y-%m-%d %H:%M:%S")
            query = query.filter(Event.create_at > start_date)
        if hasattr(form, "end_date"):
            end_date = datetime.datetime.strptime(form.end_date, "%Y-%m-%d %H:%M:%S")
            query = query.filter(Event.create_at <  end_date)
            
        res = query.all()
        r  = []
        for info in res:
            d = serialize(info)
            r.append(d)
        return r

