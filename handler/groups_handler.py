# -*- coding: utf-8 -*-
import traceback

import tornado.ioloop
import pyrestful.rest
from api.log.log import CustomLogger
from db.model.vm_whitelist import V<PERSON><PERSON><PERSON><PERSON>st

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.model.instances import *
from db.model.user import User
from util.cov import todict, serialize, is_valid_ip
from api.openstack.client import Client
from sqlalchemy_pagination import paginate
from Page.Pagenation import  Pagenation
from sqlalchemy import and_
from db.model.vm import VmGroup, Vms, Volumes
from model.util import Role
from db.model.task import Task, InstanceCreateTask

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()

def paginate_combined_list(fixed_list, main_list, page, pagecount):
    # 合并两个列表
    combined_list = fixed_list + main_list

    # 计算分页的起始和结束索引
    start_index = (page - 1) * pagecount
    end_index = start_index + pagecount

    # 提取当前页的子列表
    paginated_list = combined_list[start_index:end_index]

    return paginated_list

class GroupsHandler(pyrestful.rest.RestHandler):
    
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")
    
    @get(_path="/v1/groups",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_groups(self):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 查询虚拟机分组列表
        responses:
          '200':
            description: 成功
            content: {}
        """
        
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username=="sysadm").first() #V3.1版本临时修改 以后需要修改为username
            group = session.query(VmGroup).filter(VmGroup.user_id==user.id).order_by(VmGroup.pid.asc())
        res = group.all()
        r  = []
        for info in res:
            d = serialize(info)
            r.append(d)
        return r


    @post(_path="/v1/groups/create",  _types=[CreateGroup], _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_add_groups(self, group):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 创建虚拟机分组
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupForm'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """

        role = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                p_group = session.query(VmGroup).filter(VmGroup.id==group.id).first()
                
                vmgroup = VmGroup()
                vmgroup.name = group.name
                vmgroup.pid = group.id
                
                username = self.get_cookie("username", "")
                user = session.query(User).filter(User.username=="sysadm").first() #V3.1版本临时修改 以后需要修改为username
                vmgroup.user_id = user.id
                session.add(vmgroup)
                session.commit()
                             
                vmgroup.pp = "%s-%d" % (p_group.pp, vmgroup.id)
                session.add(vmgroup)
                session.commit()
                session.expunge(vmgroup)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建虚拟机分组",
            #                  "object": group.name,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "虚机管理", "创建虚拟机分组", "成功", role,
                "创建虚拟机分组: {},成功".format(group.name)
            )
        except Exception:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建虚拟机分组",
            #                  "object": group.name,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "失败",
            #                  })
            new_logger.log(
                self.username, "虚机管理", "创建虚拟机分组", "失败", role,
                "创建虚拟机分组: {},失败".format(group.name)
            )
            traceback.print_exc()
            self.set_status(502)
            return {"msg":"error"}
        
        return {"msg":"ok"}
    
    
    def sysadm_delete_group(self, group):
        
        with self.session_scope() as session:
            mygroup = session.query(VmGroup).filter(VmGroup.id==group.id).first()
            pp = mygroup.pp
            mygroup.vms.clear()
            session.add(mygroup)
            #self.session.commit()
            session.query(VmGroup).filter(VmGroup.id==group.id).delete()
            
            #删除下面的全部子组用PP字段like
            subgroups = session.query(VmGroup).filter(VmGroup.pp.like("%s%%" % pp)).all()
            for subgroup in subgroups:
                subgroup.vms.clear()
                session.add(subgroup)
                session.query(VmGroup).filter(VmGroup.id==subgroup.id).delete()
            
            session.commit()
    
    def operator_delete_group(self, group):
        with self.session_scope() as session:
            mygroup = session.query(VmGroup).filter(VmGroup.id==group.id).first()
            pp = mygroup.pp
            mygroup.vms.clear()
            session.add(mygroup)
            session.query(VmGroup).filter(VmGroup.id==group.id).delete()
            
            #删除下面的全部子组用PP字段like
            subgroups = session.query(VmGroup).filter(VmGroup.pp.like("%s%%" % pp)).all()
            for subgroup in subgroups:
                subgroup.vms.clear()
                session.add(subgroup)
                session.query(VmGroup).filter(VmGroup.id==subgroup.id).delete()
            
            session.commit()        
            

    # @delete(_path="/v1/delete/groups",  _types=[DeleteGroup], _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @delete(_path="/v1/delete/groups" , _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_groups(self, group):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 删除虚拟机分组
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupForm'
          required: true
        responses:
          '200':
            description: 删除成功
            content: {}
        """
        
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        id = group.get("id", "")
        name = group.get("name", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            
        if id == 1:
            return {"msg":"root分组不能删除"}
        
        try:
            newsgroup = DeleteGroup()
            newsgroup.id = id
            if role == Role.sysadm:
                self.sysadm_delete_group(newsgroup)
        
            if role == Role.operator:
                self.operator_delete_group(newsgroup)
            

            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "删除虚拟机分组",
            #                  "object": group.id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "虚机管理", "删除虚拟机分组", "成功", role,
                "删除虚拟机分组: {},成功".format(name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除虚拟机分组",
                #              "object": group.id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "虚机管理", "删除虚拟机分组", "失败", role,
                    "删除虚拟机分组: {},失败".format(name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        
        return {"msg":"ok"}
        
    @post(_path="/v1/groups/rename",  _types=[GroupRename], _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_rename_groups(self, group):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 重命名虚拟机分组
        requestBody:
          description: 重命名
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRenameForm'
          required: true
        responses:
          '200':
            description: 重命名成功
            content: {}
        """

        role = self.get_cookie("role")
        try:
            with self.session_scope() as session:
                session.query(VmGroup).filter(VmGroup.id==group.id).update({"name":group.new_name})
                session.commit()
                
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "重命名虚拟机分组",
            #                  "object": group.new_name,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "虚机管理", "编辑虚拟机组", "成功", role,
                "编辑虚拟机组: {},成功".format(group.new_name)
            )
            
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "重命名虚拟机分组",
                #              "object": group.new_name,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "虚机管理", "编辑虚拟机组", "失败", role,
                    "编辑虚拟机组: {},失败".format(group.new_name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        
        return {"msg":"ok"}
        

    def sysadm_query_vms(self, group):
        vms = []
        group_id = group.get("id")
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_id).first()
            vms_dict = {}
            # sysadm角色如果是根目录查询全部虚机
            if group.pid == -1:
                client = Client()
                vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
                for ins in vmsdata:
                    vms.append(ins)
            
            else:
                client = Client()
                vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
                for ins in vmsdata:
                    vms_dict[ins.get("id")] = ins 
                for item in group.vms:
                    if vms_dict.get(item.vmid):
                        vms.append(vms_dict.get(item.vmid)) 

        return vms

    def sysadm_query_vms_v2(self, group):
        vms = []
        group_id = group.get("id")
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_id).first()
            vms_dict = {}
            # sysadm角色如果是根目录查询全部虚机
            if group.pid == -1:
                client = Client()
                vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
                for ins in vmsdata:
                    vms.append(ins)

            else:
                client = Client()
                vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
                for ins in vmsdata:
                    vms_dict[ins.get("id")] = ins
                for item in group.vms:
                    if vms_dict.get(item.vmid):
                        vms.append(vms_dict.get(item.vmid))

        return vms

    def all_query_vms_detail(self, data):
        vms = []
        for ins in data:
            if len(ins['volumes']) == 0:
                volume_id = ""
                ins['disk'] = ""
            else:
                volume_id = ins['volumes'][0]['id']
                client = Client()
                result = client.VolumeClient.openstack_get_volume_detail(client, volume_id)
                ins['disk'] = result['size']
            vms.append(ins)

        return vms


    def operator_query_vms(self, group, user):
        group_id = group.get("id")
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_id).first()
            vms = []
            vms_dict = {}
            # sysadm角色如果是根目录查询全部虚机
            if group.pid == -1:
                myvms = session.query(Vms).filter(Vms.user_id==user.id)
                client = Client()
                vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
                for ins in vmsdata:
                    vms_dict[ins.get("id")] = ins             
                
                
                for item in myvms:
                    if vms_dict.get(item.vmid):
                        vms.append(vms_dict.get(item.vmid))
            
            else:
                client = Client()
                vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
                for ins in vmsdata:
                    vms_dict[ins.get("id")] = ins             
                
                for item in group.vms:
                    if vms_dict.get(item.vmid):
                        vms.append(vms_dict.get(item.vmid))


        return vms

    @post(_path="/v1/groups/instances",  _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_find_vm_by_groupid(self, group):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 获取分组中的所有虚拟机
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupPageForm'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayInstanceDetailModel'
        """
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        page = group.get("page")
        group_id = group.get("id")
        pagecount = group.get("pagecount")
        order_by = group.get("order_by", "")
        order_type = group.get("order_type", "desc")
        search_str = group.get("search_str", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        
        
        
        data_list = []
        
        if role == Role.sysadm:
            data_list = self.sysadm_query_vms(group)
        
        if role == Role.operator:
            data_list = self.operator_query_vms(group, user)
            
        
        data = []
        for ins_detail in data_list:
            if search_str:
                if search_str in ins_detail["name"]  or search_str in ins_detail["ip"]:
                    data.append(ins_detail)
            else:
                data.append(ins_detail)
        
        
        
        count = len(data)
        pages = count // pagecount + 1
        if order_type == "desc" and order_by:
            data = sorted(data, key = lambda x:x[order_by], reverse=True)
        if order_by == "ip":
            data = sorted([item for item in data if item[order_by]], key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "desc"))
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by], reverse=False)
        if order_by == "ip":
            data = sorted([item for item in data if item[order_by]], key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "asc"))


        start = (page - 1) * pagecount
        #end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
        end =  start + pagecount
        
        data = data[start:end]
        fin_data = self.all_query_vms_detail(data)
          
        r = {
            "total": count,
            "pages": pages,
            "data": fin_data
            }
        
        """
        ins_detail = client.NovaClient.openstack_get_all_instance_detail(client)
        obj = Pagenation(ins_detail,page,pagecount)
        ins_list = obj.show()

        r = {
            "total": len(ins_detail),
            "pages": obj.total(),
            "data": ins_list
            }
        
        """
 
        return r

    @post(_path="/v2/groups/instances", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_find_vm_by_groupid_v2(self, group):
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        page = group.get("page")
        group_id = group.get("id")
        pagecount = group.get("pagecount")
        order_by = group.get("order_by", "")
        order_type = group.get("order_type", "desc")
        search_str = group.get("search_str", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()

        data_list = []
        if role == Role.operator:
            data_list = self.operator_query_vms(group, user)

            data = []
            for ins_detail in data_list:
                if search_str:
                    if search_str in ins_detail["name"] or search_str in ins_detail["ip"]:
                        data.append(ins_detail)
                else:
                    data.append(ins_detail)

            count = len(data)
            pages = count // pagecount + 1
            if order_type == "desc" and order_by:
                data = sorted(data, key=lambda x: x[order_by], reverse=True)
            if order_by == "ip":
                data = sorted([item for item in data if item[order_by]],
                              key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "desc"))
            if order_type == "asc" and order_by:
                data = sorted(data, key=lambda x: x[order_by], reverse=False)
            if order_by == "ip":
                data = sorted([item for item in data if item[order_by]],
                              key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "asc"))

            start = (page - 1) * pagecount
            # end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
            end = start + pagecount

            data = data[start:end]
            fin_data = self.all_query_vms_detail(data)

            r = {
                "total": count,
                "pages": pages,
                "data": fin_data
            }

            """
            ins_detail = client.NovaClient.openstack_get_all_instance_detail(client)
            obj = Pagenation(ins_detail,page,pagecount)
            ins_list = obj.show()
    
            r = {
                "total": len(ins_detail),
                "pages": obj.total(),
                "data": ins_list
                }
    
            """

            return r
        else:
            # 通过openstack接口进行分页
            params = {}

            if search_str != "" and is_valid_ip(search_str):
                params["ip"] = search_str
            elif search_str != "":
                params["name"] = search_str

            if order_by == "name":
                params["sort_key"] = "display_name"
            elif order_by == "ip":
                params["sort_key"] = "access_ip_v4"
            else:
                params["sort_key"] = "created_at"

            if order_type == "desc":
                params["sort_dir"] = "desc"
            else:
                params["sort_dir"] = "asc"

            tags = []

            with self.session_scope() as session:
                group_info = session.query(VmGroup).filter(VmGroup.id == group_id).first()

            if role == Role.operator:
                tags.append("ope_group_id = " + group_info.id)
                tags.append("username = " + username)

            if group_info.pid != -1:
                tags.append("sys_group_id = " + str(group_info.id))

            if tags:
                result_string = ','.join(tags)
                params["tags"] = result_string

            # 测试
            # params["tags-any"] = "user2"
            # params["tags"] = "group2"

            client = Client()
            vmid_list = client.NovaClient.openstack_get_all_instance(client, params)

            # 参数封装
            params["limit"] = pagecount
            if page > 1:
                marker = vmid_list["servers"][(page-1) * pagecount-1]["id"]
                params["marker"] = marker

            # 数据总量
            total = len(vmid_list["servers"])
            # 查询虚拟机列表数据
            vmsdata = client.NovaClient.openstack_get_all_instance_detail_v2(client, params)

            # data_list = []
            #
            # if role == Role.sysadm:
            #     data_list = self.sysadm_query_vms_v2(group)

            # if role == Role.operator:
                # data_list = self.operator_query_vms(group, user)

            r = {
                "total": total,
                "pages": total // pagecount + 1,
                "data": vmsdata
            }

            return r

    @post(_path="/v3/groups/instances", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_find_vm_by_groupid_v3(self, group):
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        page = group.get("page")
        group_id = group.get("id")
        pagecount = group.get("pagecount")
        order_by = group.get("order_by", "")
        order_type = group.get("order_type", "desc")
        search_str = group.get("search_str", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()

        data_list = []
        if role == Role.operator:
            data_list = self.operator_query_vms(group, user)

            data = []
            for ins_detail in data_list:
                if search_str:
                    if search_str in ins_detail["name"] or search_str in ins_detail["ip"]:
                        data.append(ins_detail)
                else:
                    data.append(ins_detail)

            count = len(data)
            pages = count // pagecount + 1
            if order_type == "desc" and order_by:
                data = sorted(data, key=lambda x: x[order_by], reverse=True)
            if order_by == "ip":
                data = sorted([item for item in data if item[order_by]],
                              key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "desc"))
            if order_type == "asc" and order_by:
                data = sorted(data, key=lambda x: x[order_by], reverse=False)
            if order_by == "ip":
                data = sorted([item for item in data if item[order_by]],
                              key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "asc"))

            start = (page - 1) * pagecount
            # end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
            end = start + pagecount

            data = data[start:end]
            fin_data = self.all_query_vms_detail(data)

            r = {
                "total": count,
                "pages": pages,
                "data": fin_data
            }

            return r
        else:
            # 通过openstack接口进行分页
            params = {}

            if search_str != "" and is_valid_ip(search_str):
                params["ip"] = search_str
            elif search_str != "":
                params["name"] = search_str

            if order_by == "name":
                params["sort_key"] = "display_name"
            elif order_by == "ip":
                params["sort_key"] = "access_ip_v4"
            else:
                params["sort_key"] = "created_at"

            if order_type == "desc":
                params["sort_dir"] = "desc"
            else:
                params["sort_dir"] = "asc"

            tags = []

            with self.session_scope() as session:
                group_info = session.query(VmGroup).filter(VmGroup.id == group_id).first()

            if role == Role.operator:
                tags.append("ope_group_id = " + group_info.id)
                tags.append("username = " + username)

            if group_info.pid != -1:
                tags.append("sys_group_id = " + str(group_info.id))

            if tags:
                result_string = ','.join(tags)
                params["tags"] = result_string

            client = Client()
            vmid_list = client.NovaClient.openstack_get_all_instance(client, params)

            # 参数封装
            params["limit"] = pagecount
            if page > 1:
                marker = vmid_list["servers"][(page-1) * pagecount-1]["id"]
                params["marker"] = marker

            # 数据总量
            total = len(vmid_list["servers"])
            # 查询虚拟机列表数据
            vmsdata = client.NovaClient.openstack_get_all_instance_detail_v3(client, params)
            
            # # 如果数据不足一页，且不是第一页，说明是最后一页，需要减1
            # if len(vmsdata) < pagecount:
            #     c =  pagecount - len(vmsdata)
            #     total = total - c

            r = {
                "total": total,
                "pages": total // pagecount + 1,
                "data": vmsdata
            }

            return r

    # 获取虚拟机列表 增加是否启用宕机迁移
    @post(_path="/v5/groups/instances", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_find_vm_by_groupid_v5(self, group):
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        page = group.get("page")
        group_id = group.get("id")
        pagecount = group.get("pagecount")
        order_by = group.get("order_by", "")
        order_type = group.get("order_type", "desc")
        search_str = group.get("search_str", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            whitelist = session.query(VmWhitelist).all()
        ha_list = whitelist

        data_list = []
        if role == Role.operator:
            data_list = self.operator_query_vms(group, user)

            data = []
            for ins_detail in data_list:
                if search_str:
                    if search_str in ins_detail["name"] or search_str in ins_detail["ip"]:
                        data.append(ins_detail)
                else:
                    data.append(ins_detail)

            count = len(data)
            pages = count // pagecount + 1
            if order_type == "desc" and order_by:
                data = sorted(data, key=lambda x: x[order_by], reverse=True)
            if order_by == "ip":
                data = sorted([item for item in data if item[order_by]],
                              key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "desc"))
            if order_type == "asc" and order_by:
                data = sorted(data, key=lambda x: x[order_by], reverse=False)
            if order_by == "ip":
                data = sorted([item for item in data if item[order_by]],
                              key=lambda x: tuple(map(int, x[order_by].split('.'))), reverse=(order_type == "asc"))

            start = (page - 1) * pagecount
            # end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
            end = start + pagecount

            data = data[start:end]
            if ha_list:
                for item in data:
                    for white in ha_list:
                        if item["id"] == white.vm_id:
                            item["ha_status"] = True

            fin_data = self.all_query_vms_detail(data)

            r = {
                "total": count,
                "pages": pages,
                "data": fin_data
            }

            return r
        else:
            # 通过openstack接口进行分页
            params = {}

            if search_str != "" and is_valid_ip(search_str):
                params["ip"] = search_str
            elif search_str != "":
                params["name"] = search_str

            if order_by == "name":
                params["sort_key"] = "display_name"
            elif order_by == "ip":
                params["sort_key"] = "access_ip_v4"
            else:
                params["sort_key"] = "created_at"

            if order_type == "desc":
                params["sort_dir"] = "desc"
            else:
                params["sort_dir"] = "asc"

            tags = []

            with self.session_scope() as session:
                group_info = session.query(VmGroup).filter(VmGroup.id == group_id).first()

            if role == Role.operator:
                tags.append("ope_group_id = " + group_info.id)
                tags.append("username = " + username)

            if group_info.pid != -1:
                tags.append("sys_group_id = " + str(group_info.id))

            if tags:
                result_string = ','.join(tags)
                params["tags"] = result_string

            client = Client()
            vmid_list = client.NovaClient.openstack_get_all_instance(client, params)

            # 参数封装
            params["limit"] = pagecount
            if page > 1:
                marker = vmid_list["servers"][(page-1) * pagecount-1]["id"]
                params["marker"] = marker

            # 数据总量
            total = len(vmid_list["servers"])
            # 查询虚拟机列表数据
            vmsdata = client.NovaClient.openstack_get_all_instance_detail_v3(client, params)
            if ha_list:
                for item in vmsdata:
                    for white in ha_list:
                        if item["id"] == white.vm_id:
                            item["ha_status"] = True
            # # 如果数据不足一页，且不是第一页，说明是最后一页，需要减1
            # if len(vmsdata) < pagecount:
            #     c =  pagecount - len(vmsdata)
            #     total = total - c

            r = {
                "total": total,
                "pages": total // pagecount + 1,
                "data": vmsdata
            }

            return r


    @post(_path="/v4/groups/instances", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_find_vm_by_groupid_v4(self, group):
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        page = group.get("page")
        group_id = group.get("id")
        pagecount = group.get("pagecount")
        order_by = group.get("order_by", "")
        order_type = group.get("order_type", "desc")
        search_str = group.get("search_str", "")
        vm_id = group.get("vm_id", "")

        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            ready_ins_list = session.query(InstanceCreateTask).filter(InstanceCreateTask.status=='ready').all()
            ready_ins_index = []
            for ins in ready_ins_list:
                ready_ins_index.append({"id":ins.id, "name":ins.name})


        data_list = []

        # 通过openstack接口进行分页
        params = {}

        if search_str != "" and is_valid_ip(search_str):
            params["ip"] = search_str
        elif search_str != "":
            params["name"] = search_str

        if order_by == "name":
            params["sort_key"] = "display_name"
        elif order_by == "ip":
            params["sort_key"] = "access_ip_v4"
        else:
            params["sort_key"] = "created_at"

        if order_type == "desc":
            params["sort_dir"] = "desc"
        else:
            params["sort_dir"] = "asc"

        tags = []

        with self.session_scope() as session:
            group_info = session.query(VmGroup).filter(VmGroup.id == group_id).first()

        if role == Role.operator:
            tags.append("ope_group_id = " + group_info.id)
            tags.append("username = " + username)

        if group_info.pid != -1:
            tags.append("sys_group_id = " + str(group_info.id))

        if tags:
            result_string = ','.join(tags)
            params["tags"] = result_string


        #查询出所有虚机的id 用于计算
        client = Client()
        vmid_list = client.NovaClient.openstack_get_all_instance(client, params)


        res_data = []        

        # 查询虚机的列表参数计算
        ready_ins_count = len(ready_ins_list)      
        if ready_ins_count == 0:
            params["limit"] = pagecount
            if page > 1:
                marker = vmid_list["servers"][(page-1) * pagecount-1]["id"]
                params["marker"] = marker


                
        if ready_ins_count > 0:
            # 相加两个列表的索引
            total = len(vmid_list["servers"]) + ready_ins_count
            fixed_list = ready_ins_index
            main_list = vmid_list["servers"]
      
            result = paginate_combined_list(fixed_list, main_list, page, pagecount)

            print(result)




        # 数据总量
        total = len(vmid_list["servers"])
        # 查询虚拟机列表数据
        vmsdata = client.NovaClient.openstack_get_all_instance_detail_v3(client, params)
        
        # 如果数据不足一页，且不是第一页，说明是最后一页，需要减1
        if len(vmsdata) < pagecount:
            c =  pagecount - len(vmsdata)
            total = total - c

        r = {
            "total": total,
            "pages": total // pagecount + 1,
            "data": vmsdata
        }

        return r
        
    def add_or_update_vm_to_mygroup(self, vm_ids, user_id, group_p):
        group_name = group_p.name
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_p.id).first()  
            for vmid in vm_ids:
                vm = session.query(Vms).filter(Vms.vmid == vmid).first()
                if not vm:
                    vm = Vms()
                vm.vmid = vmid
                #vm.user_id = user_id
                session.add(vm)
                group.vms.append(vm)
            
            session.add(group)
            session.commit()
            session.expunge(group)
        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", 
            {"username":self.get_cookie('username', ""), 
                "op":"添加虚拟机到分组", 
                "object":"%s" % group_name, 
                "role":self.get_cookie('role', ""), 
                "result":"成功"})

    def update_vm_tag(self, vms):
            client = Client()
            res = client.NovaClient.openstack_replace_vm_single_tag(client, vms)

    def delete_vm_tag(self, vms):
        client = Client()
        res = client.NovaClient.openstack_delete_vm_single_tag(client, vms)

    def update_vm_to_mygroup(self, vm_ids, user_id, group_p):
        role = self.get_cookie('role', "")
        group_name = group_p.name
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_p.id).first()  
            for vmid in vm_ids:
                vm = session.query(Vms).filter(Vms.vmid == vmid).first()
                if not vm:
                    vm = Vms()
                vm.vmid = vmid
                vm.user_id = user_id
                session.add(vm)
                group.vms.append(vm)
            
            session.add(group)
            session.commit()
            session.expunge(group)
        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", 
        #     {"username":self.get_cookie('username', ""), 
        #         "op":"添加虚拟机到分组", 
        #         "object":"%s" % (group_name), 
        #         "role":self.get_cookie('role', ""), 
        #         "result":"成功"})
        new_logger.log(
            self.username, "虚机管理", "添加虚拟机到分组", "成功", role,
            "添加虚拟机到分组: {},成功".format(group_name)
        )

        #TODO 添加失败日志

    @put(_path="/v1/groups/add",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_add_vm_to_group(self,  form):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 添加虚拟机到分组
        requestBody:
          description: 添加
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupEditForm'
          required: true
        responses:
          '200':
            description: 添加成功
            content: {}
        """
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        
        group_id = form.get("group_id")
        vm_ids = form.get("vmids")
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_id).first()
            session.expunge(group)
        
        if group.pid == -1:
            return {"msg":"error"}
        
        try:
            if role == Role.sysadm:
                self.add_or_update_vm_to_mygroup(vm_ids, user.id, group)
            
            if role == Role.operator:
                self.update_vm_to_mygroup(vm_ids, user.id, group)


            
        except Exception as e:
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "添加虚拟机到分组",
                             "object": "%s" % (group.name),
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
            new_logger.log(
                self.username, "虚机管理", "添加虚拟机到分组", "失败", role,
                "添加虚拟机到分组: {},失败".format(group.name)
            )
            return {"msg":"error"}
        
        new_logger.log(
            self.username, "虚机管理", "添加虚拟机到分组", "成功", role,
            "添加虚拟机到分组: {},失败".format(group.name)
        )
        return {"msg":"ok"}

    @put(_path="/v2/groups/add",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_add_vm_to_tag(self,  form):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 添加虚拟机到分组
        requestBody:
          description: 添加
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupEditForm'
          required: true
        responses:
          '200':
            description: 添加成功
            content: {}
        """
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)

        group_id = form.get("group_id")
        vm_ids = form.get("vmids")
        group_name = form.get("group_name", "")
        vm_names = form.get("vm_names", [])
        log_name = ', '.join(map(str, vm_names))
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_id).first()
            session.expunge(group)

        if group.pid == -1:
            return {"msg": "error"}
        vms = {} #vms["tags"] = ["$rule", "group_id", "user_id"]
        try:
            if role == Role.sysadm:
                vms["tags"] = ["role = " + Role.sysadm]
                vms["tags"].append("sys_group_id = " + str(group.id))
            if role == Role.operator:
                vms["tags"] = ["role = " + Role.operator]
                vms["tags"].append("ope_group_id = " + str(group.id))
                vms["tags"].append("username = " + username)
            for vmid in vm_ids:
                vms['vm_id'] = vmid
                self.update_vm_tag(vms)

            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #             {"username": self.get_cookie('username', ""),
            #              "op": "虚拟机移入分组",
            #              "object": "",
            #              "role": self.get_cookie('role', ""),
            #              "result": "成功"
            #              })

            for vm_name in vm_names:
                new_logger.log(
                    self.username, "虚机管理", "虚拟机移入分组", "成功", role,
                    "将虚拟机:{} 移入分组: {},成功".format(vm_name, group_name)
                )

        except Exception as e:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #          {"username": self.get_cookie('username', ""),
            #           "op": "虚拟机移入分组",
            #           "object": "",
            #           "role": self.get_cookie('role', ""),
            #           "result": "失败"
            #           })

            for vm_name in vm_names:
                new_logger.log(
                    self.username, "虚机管理", "虚拟机移入分组", "失败", role,
                    "将虚拟机:{} 移入分组: {},失败".format(vm_name, group_name)
                )
            self.set_status(502)
            return {"msg":"error"}
        return {"msg":"ok"}
    
    
    def sysadm_delete_vm_from_group(self, group_id, vm_ids):
        with self.session_scope() as session:
            mygroup = session.query(VmGroup).filter(VmGroup.id==group_id).first()
            vms = []
            for vm in mygroup.vms:
                if not  vm.vmid in vm_ids:
                    vms.append(vm)
                    #self.session.delete(vm)
                    #self.session.flush()
            mygroup.vms.clear()
            #self.session.add(mygroup)
            #self.session.commit()
            mygroup.vms = vms
            session.add(mygroup)
            session.commit()
            
    def operator_delete_vm_from_group(self, group_id, vm_ids):
        with self.session_scope() as session:
            mygroup = session.query(VmGroup).filter(VmGroup.id==group_id).first()
            vms = []
            for vm in mygroup.vms:
                if not  vm.vmid in vm_ids:
                    vms.append(vm)
                    #self.session.delete(vm)
                    #self.session.flush()
            mygroup.vms.clear()
            #self.session.add(mygroup)
            #self.session.commit()
            mygroup.vms = vms
            session.add(mygroup)
            session.commit() 
    
    
    @delete(_path="/v1/groups/remove",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_vm_from_group(self, form):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 移除虚拟机从分组
        requestBody:
          description: 添加
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupEditForm'
          required: true
        responses:
          '200':
            description: 移除成功
            content: {}
        """
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        #user = self.session.query(User).filter(User.username == username).first()
        
        group_id = form.get("group_id")
        vm_ids = form.get("vmids")       
        
        try:

            if role == Role.sysadm:
                self.sysadm_delete_vm_from_group(group_id, vm_ids)
        
            if role == Role.operator:
                self.operator_delete_vm_from_group(group_id, vm_ids)
                            
            #self.session.query(Vms).filter(and_( Vms.vmgroup_id==instancesaddtogroup.group_id,Vms.vmid== instancesaddtogroup.id) ).delete()
            #self.session.commit()
            
            
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "从分组移除虚拟机",
                             "object": "",
                             "role": self.get_cookie('role', ""),
                             "result": "成功"
                             })
            new_logger.log(
                self.username, "虚机管理", "从分组移除虚拟机", "失败", role,
                "从分组移除虚拟机: {},失败".format(group_id)
            )
        except Exception as e:
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "从分组移除虚拟机",
                             "object": "",
                             "role": self.get_cookie('role', ""),
                             "result": "失败"
                             })
            new_logger.log(
                self.username, "虚机管理", "从分组移除虚拟机", "失败", role,
                "从分组移除虚拟机: {},失败".format(group_id)
            )
            return {"msg":"error"}
        return {"msg":"ok"}

    @delete(_path="/v2/groups/remove",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_vm_from_group(self, form):
        """
        ---
        tags:
          - 虚拟机分组相关接口
        summary: 移除虚拟机从分组
        requestBody:
          description: 添加
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupEditForm'
          required: true
        responses:
          '200':
            description: 移除成功
            content: {}
        """
        role = self.get_cookie("role")
        username = self.get_cookie("username")
        #user = self.session.query(User).filter(User.username == username).first()

        group_id = form.get("group_id")
        vm_ids = form.get("vmids")

        group_name = form.get("group_name", "")
        vm_names = form.get("vm_names", [])
        log_name = ', '.join(map(str, vm_names))
        vms = {}
        vms["tags"] = []
        try:
            if role == Role.sysadm:
                vms["tags"].append("sys_group_id = " + str(group_id))
            if role == Role.operator:
                vms["tags"].append("ope_group_id = " + str(group_id))
            for vmid in vm_ids:
                vms['vm_id'] = vmid
                self.delete_vm_tag(vms)

            #self.session.query(Vms).filter(and_( Vms.vmgroup_id==instancesaddtogroup.group_id,Vms.vmid== instancesaddtogroup.id) ).delete()
            #self.session.commit()


            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #             {"username": self.get_cookie('username', ""),
            #              "op": "从分组移除虚拟机",
            #              "object": "",
            #              "role": self.get_cookie('role', ""),
            #              "result": "成功"
            #              })


            for vm_name in vm_names:
                new_logger.log(
                    self.username, "虚机管理", "从分组移除虚拟机", "成功", role,
                    "从分组: {} 移除虚拟机: {},成功".format(group_name, vm_name)
                )

        except Exception as e:
            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #              {"username": self.get_cookie('username', ""),
            #               "op": "从分组移除虚拟机",
            #               "object": "",
            #               "role": self.get_cookie('role', ""),
            #               "result": "失败"
            #               })

            for vm_name in vm_names:
                new_logger.log(
                    self.username, "虚机管理", "从分组移除虚拟机", "失败", role,
                    "从分组: {} 移除虚拟机: {},失败".format(group_name, log_name)
                )
            self.set_status(500)
            return {"msg": "error"}
        return {"msg": "ok"}