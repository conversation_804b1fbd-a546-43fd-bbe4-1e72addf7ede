import uuid
import subprocess
import os
import psutil


def generate_unique_port_name(prefix='port'):
    """
    使用UUID生成一个唯一的OVS端口名称。

    :param prefix: 端口名称的前缀，默认为'port'
    :return: 生成的端口名称
    """
    unique_id = str(uuid.uuid4())[:8]  # 取UUID的前8个字符以保持简洁
    return f"{prefix}-{unique_id}"



def convert_to_bytes(size: str, unit: str) -> str:
    """
    将不同内存单位转换为字节，处理字符串类型的输入输出

    参数:
        size: 数值(字符串)，如 "4"、"4096"
        unit: 单位(字符串)，如 "KiB", "MiB", "GiB"

    返回:
        bytes_str: 转换后的字节数(字符串)
    """
    # 定义转换因子
    units = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 * 1024,
        'GB': 1024 * 1024 * 1024
    }

    # 检查单位是否支持
    unit = unit.upper()
    if unit not in units:
        raise ValueError(f"不支持的单位: {unit}. 支持的单位有: {', '.join(units.keys())}")

    try:
        # 将输入字符串转换为浮点数进行计算
        size_num = float(size)
        # 转换为字节并转回字符串
        bytes_str = str(int(size_num * units[unit]))
        return bytes_str
    except ValueError:
        raise ValueError(f"无效的数值: {size}")

def convert_to_gigabytes(size: str, unit: str) -> str:
    """将不同内存单位转换为 GB，并返回 GB 单位的字符串值
    
    参数:
        size: 数值（字符串），如 "4"、"4096" 或 "4.5"
        unit: 单位（字符串），如 "KB", "MB", "GB"（暂不支持 KiB/MiB/GiB）

    返回:
        gb_str: 转换后的 GB 值（字符串，保留小数）

    异常:
        ValueError: 当单位不支持、数值无效或为负数时抛出
    """
    # 定义转换因子（十进制单位）
    units = {
        'KB': 1024,
        'MB': 1024 * 1024,
        'GB': 1024 * 1024 * 1024
    }

    # 检查单位是否支持
    unit = unit.upper()
    if unit not in units:
        raise ValueError(f"不支持的单位: {unit}. 支持的单位有: {', '.join(units.keys())}")

    try:
        # 将输入字符串转换为浮点数（支持小数）
        size_num = float(size)
        
        # 检查是否为负数
        if size_num < 0:
            raise ValueError("存储大小不能为负数")

        # 转换为字节
        bytes_num = size_num * units[unit]
        
        # 转换为 GB（保留小数）
        gb_num = bytes_num / (1024 * 1024 * 1024)
        
        # 返回字符串形式（可根据需要调整小数位数）
        return f"{gb_num:.6f}"  # 保留6位小数，可根据需求修改
    except ValueError as e:
        raise ValueError(f"无效的数值: {size}") from e
    
    
    


def get_system_info():
    model = ""
    vendor = ""
    cpu_count = 0
    memory_bytes = 0
    disk_bytes = 0
    try:
        # 获取系统厂商信息
        vendor_output = subprocess.check_output("dmidecode -s system-manufacturer", shell=True)
        vendor = vendor_output.decode().strip()
        
        # 获取系统型号信息
        model_output = subprocess.check_output("dmidecode -s system-product-name", shell=True)
        model = model_output.decode().strip()
        # 获取CPU数量
        cpu_count = os.cpu_count() or 0
        # 获取内存大小（字节）
        memory_bytes = psutil.virtual_memory().total
        # 获取磁盘总容量（字节）
        disk_bytes = sum([d.total for d in psutil.disk_partitions(all=False) if os.path.ismount(d.mountpoint) for d in [psutil.disk_usage(d.mountpoint)]])
        info = {
            "model": model,
            "vendor": vendor,
            "cpu_cores": cpu_count,
            "memory": memory_bytes,
            "storage": disk_bytes
        }
    except Exception as e:
        print(f"无法获取系统信息: {e}")
        info = {
            "model": model,
            "vendor": vendor,
            "cpu_count": cpu_count,
            "memory_bytes": memory_bytes,
            "disk_bytes": disk_bytes
        }
    return info