FROM tianwen1:5000/hci_base:ubuntu-x86_64

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS

# RUN apt-get update -y
# RUN apt-get -y install python3 python3-pip python3-distutils
# RUN apt-get -y install git
# RUN apt-get -y install libvirt-dev libdqlite-dev

WORKDIR /

RUN git clone http://************/hci/hci_db.git \
    && cd /hci_db \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_db_commit_version \
    && git describe --abbrev=1  --tags > hci_db_latest_tag \   
    && rm -rf /hci_db/.git

RUN git clone http://************/hci/hci_api.git \
    && cd /hci_api \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_api_commit_version \
    && git describe --abbrev=1  --tags > hci_api_latest_tag \ 
    && rm -rf /hci_api/.git

RUN git clone http://************/hci/hci_asyn.git \
    && cd /hci_asyn \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_asyn_commit_version \
    && git describe --abbrev=1  --tags > hci_asyn_latest_tag \ 
    && rm -rf /hci_asyn/.git

ENV PYTHONPATH=/hci_api:/hci_db:/hci_asyn

ADD . /code
WORKDIR /code



#RUN pip3 install  --no-cache-dir   -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple --break-system-packages
#RUN pip3 install -r requirements.txt -i  https://mirrors.aliyun.com/pypi/simple/
#RUN pip3 install -r requirements.txt
# RUN pip3 install  --no-cache-dir   -r requirements.txt

#RUN git clone http://************/maojj/vsphere-automation-sdk-python.git
#RUN cd vsphere-automation-sdk-python && pip3 install --upgrade pip setuptools && pip3 install --upgrade --force-reinstall --ignore-installed -r requirements.txt --extra-index-url file:///vsphere-automation-sdk-python/lib

#RUN pip3 install --upgrade git+http://************/maojj/vsphere-automation-sdk-python.git

# RUN git clone http://************/maojj/pydqlite.git
# RUN cd pydqlite && python3 setup.py develop

# RUN git clone  http://************/maojj/sqlalchemy-dqlite.git
# RUN cd sqlalchemy-dqlite && python3 setup.py develop



CMD python3 -u main.py

EXPOSE 3000 8006
