# FROM cr.loongnix.cn/library/python:3.10
FROM tianwen1:5000/hci_base:ubuntu-x86_64

#RUN apt-get install -y gcc

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS



ENV PYTHONPATH=/hci_api:/hci_db

ADD . /code
WORKDIR /code

RUN apt-get update -y
RUN apt-get -y install python3 python3-pip python3-distutils
RUN apt-get -y install git
RUN apt-get -y install libvirt-dev libdqlite-dev  python3-dev default-libmysqlclient-dev build-essential

RUN git clone http://************/hci/hci_db.git /hci_db \
    && cd /hci_db \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_db_commit_version \
    && git describe --abbrev=1  --tags > hci_db_latest_tag \
    && rm -rf /hci_db/.git

RUN git clone http://************/hci/hci_api.git /hci_api \
    && cd /hci_api \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_api_commit_version \
    && git describe --abbrev=1  --tags > hci_api_latest_tag \
    && rm -rf /hci_api/.git



RUN pip3 install -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip3 install mysqlclient
#RUN pip3 install -r requirements.txt -i  https://mirrors.aliyun.com/pypi/simple/
#RUN pip3 install -r requirements.txt

# RUN git clone -b loongarch64 http://************/maojj/pydqlite.git
# RUN cd pydqlite && python3 setup.py develop

# RUN git clone http://************/maojj/sqlalchemy-dqlite.git
# RUN cd sqlalchemy-dqlite && python3 setup.py develop



CMD celery -A app.celery worker --loglevel=info
EXPOSE 8085
