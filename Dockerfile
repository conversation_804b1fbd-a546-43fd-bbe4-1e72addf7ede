FROM tianwen1:5000/hci_base:ubuntu-x86_64

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS


RUN apt-get update -y
RUN DEBIAN_FRONTEND=noninteractive apt-get install -y mysql-client
RUN apt-get -y install python3 python3-pip python3-distutils
RUN apt-get -y install git
RUN apt-get -y install libvirt-dev libdqlite-dev


ADD . /code
WORKDIR /code

RUN pip3 install -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple

RUN git clone http://************/maojj/pydqlite.git
RUN cd pydqlite && python3 setup.py develop

RUN git clone http://************/maojj/sqlalchemy-dqlite.git
RUN cd sqlalchemy-dqlite && python3 setup.py develop

RUN chmod +x /code/bootstrap.sh
ENTRYPOINT ["/bin/bash", "/code/bootstrap.sh"]
