import os
import subprocess
import shutil
from typing import List, Dict, Any, Optional

from infi.iscsiapi import get_iscsiapi
from infi.iscsiapi.base import Endpoint
import pyudev
import psutil
from requests import session
from sqlalchemy import extract


class ISCSIManager:
    def __init__(self, target_ip="************", target_port=3260):
        """
        初始化 ISCSIManager 类实例。

        :param target_ip: iSCSI 目标服务器的 IP 地址
        :param target_port: iSCSI 目标服务器的端口，默认为 3260
        """
        self.api = get_iscsiapi()
        self.target_ip = target_ip
        self.target_port = target_port
        self.logged_in_target = None

    def discover_targets(self):
        """
        发现 iSCSI 目标。
        iscsi_agent = ISCSIManager("************")
        targets = iscsi_agent.discover_targets()

        for target in targets:
            print(target.get_iqn(), target.get_endpoints(),target.get_discovery_endpoint())

        :return: 发现的目标列表
        """
        print(f"正在发现目标 {self.target_ip}...")
        try:
            discovered_targets = self.api.discover(self.target_ip)

            # 确保返回的是列表
            if not isinstance(discovered_targets, list):
                discovered_targets = [discovered_targets]

            if not discovered_targets:
                print(f"未发现任何目标在 {self.target_ip}")
                return []

            print(f"已发现以下目标：")
            for idx, target in enumerate(discovered_targets):
                iqn = target.get_iqn()
                print(
                    f"目标 {idx}: IQN={iqn}, 设备路径={target.get_device_path() if hasattr(target, 'get_device_path') else 'N/A'}")

            return discovered_targets

        except Exception as e:
            print(f"发现目标时发生错误: {e}")
            return []

    def discover_target_list(self):
        """
        获取target列表
        """
        targets = self.discover_targets()
        return [target.get_iqn() for target in targets]

    @staticmethod
    def is_target_logged_in(target_iqn):
        """
        检查指定的 iSCSI 目标是否已经登录。
        :param target_iqn: 目标的 IQN（iSCSI Qualified Name）
        :return: 如果已登录返回 True，否则返回 False
        """
        try:
            # 调用 iscsiadm 查看当前会话
            result = subprocess.run(
                ["iscsiadm", "-m", "session"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 检查目标 IQN 是否在会话列表中
            if result.returncode == 0 and target_iqn in result.stdout:
                print(f"Target {target_iqn} is already logged in.")
                return True
            else:
                print(f"Target {target_iqn} is not logged in.")
                return False
        except Exception as e:
            print(f"Error checking iSCSI sessions: {e}")
            return False

    def login(self, target_iqn):
        """
        登录到指定的 iSCSI 目标。

        :param target_index: 发现的目标列表中的索引，默认为 0
        :return: 登录是否成功
        """
        target = self.get_target_by_iqn(target_iqn)
        if target is None:
            print("目标不存在")
            return False

        endpoint = Endpoint(ip_address=self.target_ip, port=self.target_port)
        self.api.login(target=target, endpoint=endpoint)
        print(f"\n正在登录到目标 IQN={target.get_iqn()}, 端点={endpoint}")
        print("登录成功！")
        return True

    def get_sessions(self):
        """
        获取当前所有的 iSCSI 会话信息。

        :return: 会话信息列表
        """
        try:
            print("\n获取当前会话信息...")
            sessions = self.api.get_sessions()
            if sessions:
                session_info = []
                for session in sessions:
                    uid = session.get_uid()
                    hct = session.get_hct()
                    tgt = session.get_target()
                    print(tgt.get_iqn())
                    src_iqn = session.get_source_iqn()
                    session_info.append({
                        'uid': uid,
                        'hct': hct,
                        'target': tgt,
                        'source_iqn': src_iqn
                    })
                    print(f"会话 UID={uid}, HCT={hct}, 目标={tgt}, 源 IQN={src_iqn}")
                return session_info
            else:
                print("未找到任何会话。")
                return []

        except Exception as e:
            print(f"获取会话信息时发生错误: {e}")
            return []

    def logout(self, target_iqn):
        """
        断开与当前登录目标的连接。
        """

        try:

            target_session = self.get_session_by_iqn(target_iqn)
            if target_session is None:
                print("会话不存在")
                return True

            self.api.logout(target_session)
            print("已成功断开连接。")
            return True

        except Exception as e:
            print(f"断开连接时发生错误: {e}")
            return False

    def perform_operations(self):
        """
        执行一系列操作：发现目标、登录、获取会话信息。
        这是一个示例方法，可以根据需要进行扩展。
        """
        if self.discover_targets():
            if self.login():
                self.get_sessions()
                self.logout()

    def get_target_by_iqn(self, target_iqn):
        """
        根据iqn获取指定的target
        """
        targets = self.discover_targets()
        if not targets:
            print("没有可用的目标进行登录。")
            return None
        try:
            for target in targets:
                if target.get_iqn() == target_iqn:
                    print("获取target成功！")
                    return target
            print("未找到target")
            return None
        except Exception as e:
            print(f'获取target失败：{e}')
            return None

    def get_session_by_iqn(self, target_iqn):
        """
        根据iqn获取会话。
        如果没有会话信息，可能是没有登录该设备
        :return: 会话信息列表
        """
        print("\n获取当前会话信息...")
        sessions = self.api.get_sessions()
        if sessions:
            for session in sessions:
                uid = session.get_uid()
                hct = session.get_hct()
                tgt = session.get_target()

                src_iqn = session.get_source_iqn()
                if tgt.get_iqn() == target_iqn:
                    print(f"会话 UID={uid}, HCT={hct}, 目标={tgt.get_iqn()}, 源 IQN={src_iqn}")
                    return session
            print("未找到任何会话")
            return None
        else:
            print("获取会话信息失败。")
            return None

    @staticmethod
    def get_device_info_by_iqn(target_iqn):
        """
        根据指定的 IQN 获取其对应的所有设备信息。

        :param target_iqn: 目标 IQN（如 iqn.2005-10.org.freenas.ctl:cloud-dev）
        :return: 包含设备信息的列表，如果没有匹配的设备则返回空列表
        """
        context = pyudev.Context()
        matching_devices = []

        # 遍历所有块设备
        for device in context.list_devices(subsystem='block', DEVTYPE='disk'):
            # print("device信息：", device)
            # 检查设备是否属于 SCSI 总线
            if device.get('ID_BUS') == 'scsi':
                # 获取设备路径并提取 IQN
                id_path = device.get('ID_PATH')
                if id_path and 'iscsi' in id_path:
                    parts = id_path.split('-iscsi-')
                    if len(parts) > 1:
                        iqn_part = parts[1].split('-lun-')[0]
                        if iqn_part == target_iqn:
                            # 提取设备信息
                            lun_id = ISCSIManager.extract_lun_number(id_path, target_iqn)
                            device_node = device.device_node  # 设备节点（如 /dev/sda）

                            # --- 修改部分：改用 /sys/block/ 获取磁盘大小 ---
                            size_bytes = ISCSIManager().get_device_size_from_sysfs(device_node)
                            
                            device_info = {
                                "lun_id": lun_id,
                                'device_node': device.device_node,  # 设备节点（如 /dev/sda）
                                'iqn': iqn_part,  # 所属 IQN
                                'device_path': id_path,  # 设备路径
                                # 'size_bytes': device.get('SIZE', 'Unknown'),  # 设备大小（字节）
                                'size_bytes': size_bytes,
                                'vendor': device.get('ID_VENDOR', 'Unknown'),  # 厂商信息
                                'model': device.get('ID_MODEL', 'Unknown'),  # 设备型号
                                'id_serial': device.get('ID_SERIAL', 'Unknown'),  # 设备序列号
                                'driver': device.driver or 'Unknown',  # 驱动程序
                                'subsystem': device.subsystem,  # 子系统类型
                                'tags': list(device.tags),  # 标签
                                # 'properties': dict(device.properties),  # 所有属性
                                'is_ocfs2': False,
                            }
                            # 检查分区是否被格式化为 OCFS2
                            if ISCSIManager.is_device_formatted_as_ocfs2(device.device_node):
                                device_info['is_ocfs2'] = True
                            # 查找该设备的所有分区
                            # for partition in device.children:
                            #     partition_node = partition.device_node
                            #
                            #     # 使用 psutil 获取挂载点
                            #     partitions = psutil.disk_partitions()
                            #     for p in partitions:
                            #         if p.device == partition_node:
                            #             device_info['mount_points'].append(p.mountpoint)

                            matching_devices.append(device_info)

        return matching_devices
    
    
    @staticmethod
    # --- 新增函数：通过 /sys/block/ 获取磁盘大小（字节） ---
    def get_device_size_from_sysfs(device_node):
        """
        通过 /sys/block/<device>/size 获取磁盘大小（单位：字节）
        :param device_node: 设备节点（如 /dev/sda）
        :return: 磁盘大小（字节），如果无法获取则返回 None
        """
        sysfs_path = f"/sys/block/{os.path.basename(device_node)}/size"
        if os.path.exists(sysfs_path):
            try:
                with open(sysfs_path, 'r') as f:
                    sectors = int(f.read().strip())
                size_bytes = sectors * 512  # 1 扇区 = 512 字节
                return size_bytes
            except (IOError, ValueError):
                pass
        return None  # 无法获取时返回 None

    @staticmethod
    def extract_lun_number(id_path, target_iqn):
        """
        根据指定的 IQN 从 id_path 中提取 LUN 编号。

        :param id_path: 完整的 id_path 字符串（如 ip-************:3260-iscsi-iqn.2005-10.org.freenas.ctl:cloud-dev-lun-1）
        :param target_iqn: 目标 IQN（如 iqn.2005-10.org.freenas.ctl:cloud-dev）
        :return: LUN 编号（如 1），如果未找到匹配的 IQN 则返回 None
        """
        # 检查 id_path 是否包含目标 IQN
        if target_iqn in id_path:
            # 分割字符串以获取 LUN 编号部分
            parts = id_path.split(target_iqn)
            if len(parts) > 1:
                lun_part = parts[1].strip('-')  # 去掉多余的 '-'
                if lun_part.startswith('lun-'):
                    lun_number = lun_part.split('lun-')[-1]
                    return int(lun_number) if lun_number.isdigit() else None
        return None

    @staticmethod
    def is_device_formatted_as_ocfs2(device_node):
        """
        判断指定的设备是否被格式化为 OCFS2 文件系统。

        :param device_node: 设备节点（如 /dev/sda1）
        :return: 如果设备是 OCFS2 格式，则返回 True；否则返回 False
        """
        try:
            # 使用 blkid 命令获取设备的文件系统类型
            result = subprocess.run(
                ['blkid', '-s', 'TYPE', '-o', 'value', device_node],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            # 检查输出是否包含 "ocfs2"
            if result.stdout.strip() == "ocfs2":
                return True
        except subprocess.CalledProcessError:
            # 如果命令失败（例如设备未格式化），则返回 False
            pass
        return False
    
    @staticmethod
    def format_disk_with_ocfs2(device_path):
        """
        使用 mkfs.ocfs2 格式化磁盘
        :param device_path: 磁盘设备路径，如 '/dev/sda'
        """
        try:
            print(f"开始格式化磁盘 {device_path} 为 OCFS2...")
            subprocess.run(
                ['mkfs.ocfs2', device_path],
                check=True
            )
            print(f"磁盘 {device_path} 格式化成功！")
            return True
        except subprocess.CalledProcessError as e:
            print(f"格式化磁盘 {device_path} 失败: {e.stderr.strip()}")
            return False      

    @staticmethod
    def mount_lun_device(form):
        """
        挂载指定的 LUN 设备到指定的挂载点。

        :param form: 包含挂载所需参数的字典，至少应包括 'device_node' 和 'mount_point'
        :return: 如果挂载成功返回 True，否则返回 False
        """
        # 从 form 字典中获取参数
        device_node = form.get('device_node')
        mount_point = form.get('mount_point')
        filesystem = form.get('filesystem', 'ocfs2')  # 默认文件系统类型为 ocfs2

        if not device_node or not mount_point:
            print("Error: 'device_node' and 'mount_point' are required parameters.")
            return False

        try:
            # 创建挂载点目录（如果不存在）
            subprocess.run(['mkdir', '-p', mount_point], check=True)

            # 执行挂载命令
            subprocess.run(['mount', '-t', filesystem, device_node, mount_point], check=True)
            print(f"Successfully mounted {device_node} to {mount_point}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to mount {device_node}: {e}")
            return False

    @staticmethod
    def unmount_lun_device(form):
        """
        卸载指定的挂载点。

        :param form: 包含卸载所需参数的字典，至少应包括 'mount_point'
        :return: 如果卸载成功返回 True，否则返回 False
        """
        # 从 form 字典中获取参数
        mount_point = form.get('mount_point')

        if not mount_point:
            print("Error: 'mount_point' is a required parameter.")
            return False

        try:
            # 执行卸载命令
            subprocess.run(['umount', mount_point], check=True)
            print(f"Successfully unmounted {mount_point}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to unmount {mount_point}: {e}")
            return False

    @staticmethod
    def is_device_mounted(device_node):
        """
        判断指定的设备是否已挂载。

        :param device_node: 设备节点（如 /dev/sda1）
        :return: 如果设备已挂载，返回 True；否则返回 False
        """
        # 获取系统中的所有挂载点
        partitions = psutil.disk_partitions()

        # 检查设备节点是否出现在挂载点列表中
        for partition in partitions:
            if partition.device == device_node:
                return True

        # 如果未找到匹配的挂载点，则认为设备未挂载
        return False

    @staticmethod
    def create_catalog(catalog: str) -> None:
        """
        检查目录是否存在，如果不存在则创建它，并设置默认权限（755，属主root）。
        
        :param catalog: 目录（如 '/data'）
        :raises: OSError 如果创建目录失败（如权限不足）
        """
        if not os.path.exists(catalog):
            print(f"目录 {catalog} 不存在，正在创建...")
            try:
                os.makedirs(catalog, mode=0o755)  # 创建目录并设置权限 755
                print(f"成功创建目录 {catalog}")
                return True
            except OSError as e:
                print(f"创建目录 {catalog} 失败: {e}")
                return False
        else:
            print(f"目录 {catalog} 已存在，无需操作")
            return True
        
    @staticmethod
    def remove_catalog(catalog: str) -> bool:
        """
        递归删除指定目录及其所有内容，并处理可能出现的异常。
        
        :param catalog: 要删除的目录路径（如 '/data'）
        :return: True 如果删除成功或目录不存在，False 如果删除失败
        :raises: 不会抛出异常，所有错误会被捕获并打印
        """
        if not os.path.exists(catalog):
            print(f"目录 {catalog} 不存在，无需删除")
            return True
        
        if not os.path.isdir(catalog):
            print(f"路径 {catalog} 不是目录，无法删除")
            return False
        
        print(f"正在删除目录 {catalog} 及其所有内容...")
        try:
            shutil.rmtree(catalog)  # 递归删除目录
            print(f"成功删除目录 {catalog}")
            return True
        except OSError as e:
            print(f"删除目录 {catalog} 失败: {e}")
            return False



    @staticmethod
    def create_pool(form):
        """
        根据 form 字典中的参数，在指定位置创建文件夹。

        :param form: 包含参数的字典，预期包含以下键：
                     - 'base_path': 基础路径（字符串）
                     - 'folder_name': 文件夹名称（字符串）
        :return: 如果成功创建文件夹或文件夹已存在，返回 True；否则返回 False
        """
        try:
            # 从 form 字典中提取参数
            base_path = form.get("storage_path", "").strip()
            folder_name = form.get("pool_name", "").strip()

            # 检查参数是否有效
            if not base_path or not folder_name:
                return False

            # 构造完整路径
            full_path = os.path.join(base_path, folder_name)

            # 检查路径是否存在
            if not os.path.exists(full_path):
                # 创建文件夹
                os.makedirs(full_path, exist_ok=True)

            # 如果文件夹已存在或成功创建，返回 True
            return True

        except Exception as e:
            # 捕获任何异常并返回 False
            print("创建存储池报错：",e)
            return False
        
    @staticmethod    
    def create_disk_volume(form):
        """
        创建虚拟机磁盘文件（纯字典参数设计）

        :param form: 配置字典，必须包含以下键：
            - "path": 存储池路径（如 "/mnt/ocfs2/vm_pool"）
            - "name": 磁盘文件名（如 "vm1_disk.qcow2"）
            - "size": 磁盘大小（数值）
            - "size_unit": 单位（默认 "GB"，支持 "GB"/"MB"/"KB"）
            - "format": 磁盘格式（默认 "qcow2"）
        :return: 成功返回 True，失败返回 False
        """
        # 检查必要参数是否存在
        required_keys = ["path", "name", "size"]
        for key in required_keys:
            if key not in form:
                print(f"错误：缺少必要参数 '{key}'")
                return False

        # 设置默认值
        form.setdefault("size_unit", "GB")  # 默认单位 GB
        form.setdefault("format", "qcow2")  # 默认格式 qcow2

        # 构造磁盘路径
        volume_path = os.path.join(form["path"], form["name"])
        if os.path.exists(volume_path):
            print(f"磁盘已存在: {volume_path}")
            return False

        # 转换大小为 GB（qemu-img 默认单位是 GB）
        size_gb = convert_to_gigabytes(form["size"], form["size_unit"])
        if size_gb is None:
            print(f"错误：不支持的单位 '{form['size_unit']}'，请使用 GB/MB/KB")
            return False

        # 构造 qemu-img 命令
        cmd = [
            "qemu-img", "create",
            "-f", form["format"],
            volume_path,
            f"{size_gb}G"  # qemu-img 只接受 GB 单位
        ]

        try:
            subprocess.run(cmd, check=True)
            print(
                f"磁盘已创建: {volume_path} ({form['size']}{form['size_unit']}, 格式: {form['format']})")
            return True
        except subprocess.CalledProcessError as e:
            print(f"创建磁盘失败: {e}")
            return False


    @staticmethod
    def delete_disk_volume(pool_path, volume_name):
        """
        删除虚拟机磁盘文件
        
        :param pool_path: 存储池路径
        :param volume_name: 磁盘文件名
        :return: 总是返回 True（即使磁盘不存在也返回 True，仅打印记录）
        """
        volume_path = os.path.join(pool_path, volume_name)
        
        if os.path.exists(volume_path):
            os.remove(volume_path)
            print(f"磁盘已删除: {volume_path}")
        else:
            print(f"警告：磁盘不存在，无需删除: {volume_path}")  # 磁盘不存在时仅打印警告
        
        return True  # 无论磁盘是否存在都返回True
    
    
    @staticmethod
    def rename_disk_volume(pool_path, old_name, new_name):
        """
        重命名虚拟机磁盘文件

        :param pool_path: 存储池路径
        :param old_name: 原磁盘文件名
        :param new_name: 新磁盘文件名
        :return: True（成功或原文件不存在都返回True，仅打印记录）
        """
        old_path = os.path.join(pool_path, old_name)
        new_path = os.path.join(pool_path, new_name)

        if os.path.exists(old_path):
            os.rename(old_path, new_path)
            print(f"磁盘已重命名: {old_path} -> {new_path}")
        else:
            print(f"警告：原磁盘不存在，无需重命名: {old_path}")

        return True  # 无论是否存在都返回True
    # 3. 容量监控
    @staticmethod
    def get_storage_pool_capacity(pool_path):
        """
        获取存储池的容量信息
        
        :param pool_path: 存储池路径
        :return: 包含容量信息的字典，格式为:
            {
                "capacity": 总容量(GB),
                "available": 可用容量(GB),
                "allocation": 已分配容量(GB)
            }
            如果路径不存在，返回全0的字典
        """
        if not os.path.exists(pool_path):
            print(f"警告：存储池路径不存在: {pool_path}")
            return {
                "capacity": 0,
                "available": 0,
                "allocation": 0
            }

        stat = os.statvfs(pool_path)
        block_size = stat.f_frsize
        total_blocks = stat.f_blocks
        free_blocks = stat.f_bfree

        # 计算总容量(GB)
        total_gb = (total_blocks * block_size)
        
        # 计算可用容量(GB)
        available_gb = (free_blocks * block_size)
        
        # 计算已分配容量(GB) = 总容量 - 可用容量
        allocation_gb = total_gb - available_gb

        return {
            "capacity": total_gb,
            "available": available_gb,
            "allocation": allocation_gb
        }
        
        
def convert_to_gigabytes(size: str, unit: str) -> str:
    """将不同内存单位转换为 GB，并返回 GB 单位的字符串值
    
    参数:
        size: 数值（字符串），如 "4"、"4096" 或 "4.5"
        unit: 单位（字符串），如 "KB", "MB", "GB"（暂不支持 KiB/MiB/GiB）

    返回:
        gb_str: 转换后的 GB 值（字符串，保留小数）

    异常:
        ValueError: 当单位不支持、数值无效或为负数时抛出
    """
    # 定义转换因子（十进制单位）
    units = {
        'KB': 1024,
        'MB': 1024 * 1024,
        'GB': 1024 * 1024 * 1024
    }

    # 检查单位是否支持
    unit = unit.upper()
    if unit not in units:
        raise ValueError(f"不支持的单位: {unit}. 支持的单位有: {', '.join(units.keys())}")

    try:
        # 将输入字符串转换为浮点数（支持小数）
        size_num = float(size)
        
        # 检查是否为负数
        if size_num < 0:
            raise ValueError("存储大小不能为负数")

        # 转换为字节
        bytes_num = size_num * units[unit]
        
        # 转换为 GB（保留小数）
        gb_num = bytes_num / (1024 * 1024 * 1024)
        
        # 返回字符串形式（可根据需要调整小数位数）
        return f"{gb_num:.6f}"  # 保留6位小数，可根据需求修改
    except ValueError as e:
        raise ValueError(f"无效的数值: {size}") from e