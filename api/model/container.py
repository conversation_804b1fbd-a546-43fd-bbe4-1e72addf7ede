# -*- coding: utf-8 -*-


from dataclasses import dataclass

@dataclass
class Container(object):
    id : str
    name : str
    
class ContainerCreateFrom:
    name : str
    ram : int
    vcpus : int
    disk : int
    extdisk : int
    
@dataclass
class ContainerDetail(object):
    uuid : str
    name : str
    image : str
    cpu : float
    memory : str
    command : list
    status : str
    addresses: dict
    disk : int
    
@dataclass
class ContainerCreateDetail(object):
    uuid : str
    name : str
    image : str
    cpu : float
    memory : str
    status : str
    disk : int
