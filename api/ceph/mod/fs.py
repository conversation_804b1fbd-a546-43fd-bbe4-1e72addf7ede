'''
Created on Mar 1, 2022

@author: maojj
'''

import requests
import json
import settings
#from model.flavors import <PERSON>lavor,FlavorDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class FsClient:

    def ceph_get_ceph_host(self):
        method = "/api/host"
        url = "%s%s" % (self.api_url, method)
        headers = {
            "Authorization": "Bearer %s" % self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        
        res = []
        for ins in d:
            tmp = {}
            tmp["hostname"] = ins.get("hostname")
            for ser in ins.get("services", []):
                if ser.get("type", "") == "osd":
                    res.append(tmp)
                    break
        return res
    
    def ceph_get_ceph_health(self):
        method = "/api/health/full"
        url = "%s%s" % (self.api_url, method)
        headers = {
            "Authorization": "Bearer %s" % self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        
        res = d["health"]["status"]

        return res