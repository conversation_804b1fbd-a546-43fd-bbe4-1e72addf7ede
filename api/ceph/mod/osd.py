'''
Created on Mar 1, 2022

@author: maojj
'''

import requests
import json
import settings
from api.model.ceph import Osd
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class OsdClient:

    def ceph_check_ceph_balancing(self):


        try:
            method = "/api/health/full"
            url = "%s%s" % (self.api_url, method)
            headers = {
                "Authorization": "Bearer %s" % self.token
            }
            
            r = requests.get(url, headers=headers)
            
            health_data = json.loads(r.text)

            # 检查 "PG_DEGRADED" 是否存在于检查列表中
            checks = health_data.get('health', {}).get('checks', [])
            for check in checks:
                if check.get('type') == "PG_DEGRADED":
                    print("Ceph 动平衡尚未完成。")
                    return False
                
        except requests.exceptions.RequestException as e:
            print(f"请求 Ceph API 失败: {e}")
            return False    

        return True
            

    def fetch_ceph_health_minimal(self):
        method = "/api/health/minimal"
        url = "%s%s" % (self.api_url, method)
        headers = {
            "Authorization": "Bearer %s" % self.token
        }
        
        r = requests.get(url, headers=headers)
        
        res = json.loads(r.text)

        return res

    def ceph_get_host(self):
        method = "/api/host"
        url = "%s%s" % (self.api_url, method)
        headers = {
            "Authorization": "Bearer %s" % self.token
        }

        r = requests.get(url, headers=headers)

        return r.json()

    def ceph_osd_info(self):
        method = "/api/osd"
        url = "%s%s" % (self.api_url, method)
        headers = {
            "Authorization": "Bearer %s" % self.token
        }

        devs = requests.get(url, headers=headers).json()

        res = []
        for ins_dict in devs:
            ins = from_dict(data_class=Osd, data=ins_dict)
            ins.__dict__['device_class'] = ins_dict['tree']['device_class']
            ins.__dict__['host'] = ins_dict['host']
            ins.__dict__['status'] = ins_dict['tree']['status']
            ins.__delattr__()
            res.append(ins.__dict__)

        return res

    def ceph_device_info(self, host):
        method = f"/api/host/{host}/devices"
        url = "%s%s" % (self.api_url, method)
        headers = {
            "Authorization": "Bearer %s" % self.token
        }
        devs = requests.get(url, headers=headers).json()

        res = {}
        for dev in devs:
            for host, osd in zip(dev["location"], dev["daemons"]):
                osd_id = osd.split(".")[1]
                host["osd"] = osd
                host["devid"] = dev["devid"]
                res[osd_id] = host

        return res
