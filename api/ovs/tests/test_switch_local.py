import unittest
from unittest.mock import patch
import libvirt
import os
from api.ovn.client import Client
import warnings
warnings.simplefilter("ignore", ResourceWarning)


# 必须用vip访问 不会自动redirct
host = "**********"

# Patch the settings module for testing
class TestClient(unittest.TestCase):
    #@patch('client.host_ip', new=host)
    def test_local_switch(self):

        client = Client(host)

        #创建本地交换机
        # res = client.ovs_create_local_switch(client, "test-switch")
        # self.assertTrue(res)
        
        #查询本地交换机列表
        res = client.ovs_local_switch_list(client)
        self.assertTrue(res)

        # #删除本地交换机
        # res = client.ovs_delete_local_switch(client, "test-switch")
        # self.assertTrue(res)


if __name__ == '__main__':
    unittest.main() 