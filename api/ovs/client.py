'''
Created on Mar 1, 2022

@author: maojj
'''

import json
#import settings
#from model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass

import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
import json
import settings
from dataclasses import dataclass
from string import Template
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl

from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.open_vswitch import impl_idl


    
class Client(object):
    
    host = None
    host_ip = None
    ovs_port = 6640
    ovs_conn = None
    idl = None
    ovs_api = None
    
    def __init__(self, host_ip=None):

        self.host_ip = host_ip
        self.idl = None
        
        package_perfix= "api.ovs.mod"
        
        self.init_ovs_connect()
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    #self.__dict__[attribute_name] = attribute

                    for method_name in dir(attribute):
                        method = getattr(attribute, method_name)
                        if callable(method):
                            self.__dict__[method_name] = method


    def init_ovs_connect(self):
        if self.host_ip is not None:
            ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)
        else:
            # 检查 OVS socket 文件路径
            primary_sock = "/var/run/openvswitch/db.sock"
            alternative_sock = "/usr/local/var/run/openvswitch/db.sock"
            
            if os.path.exists(primary_sock):
                ovs_connection_str = f"unix:{primary_sock}"
            elif os.path.exists(alternative_sock):
                ovs_connection_str = f"unix:{alternative_sock}"
            else:
                raise Exception("OVS socket file not found in either location")
            
        self.idl = connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch")
        self.ovs_conn = connection.Connection(
            idl=self.idl,
            timeout=10
        )
        self.ovs_api = impl_idl.OvsdbIdl(self.ovs_conn)

    
    def close_connection(self):
        if self.ovs_conn:
            self.ovs_conn.stop()
            print("Connection to OVS database closed")
            self.ovs_conn = None
            self.ovs_api = None
            # 还需要关闭并清理idl
            if self.idl:
                self.idl.close()
                self.idl = None
    
    def __del__(self):
        self.close_connection()


class ClientV2(object):
    host = None
    host_ip = "************"
    ovs_port = 6640
    ovs_conn = None
    idl = None
    ovs_api = None

    def __init__(self, host_ip):

        self.host_ip = host_ip
        self.idl = None

        package_perfix = "api.ovs.mod"

        self.init_ovs_connect()

        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):

            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)

                if isclass(attribute) and attribute.__module__ == module_str:
                    # self.__dict__[attribute_name] = attribute

                    for method_name in dir(attribute):
                        method = getattr(attribute, method_name)
                        if callable(method):
                            self.__dict__[method_name] = method

    def init_ovs_connect(self):
        ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)
        self.idl = connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch")
        self.ovs_conn = connection.Connection(
            idl=self.idl,
            timeout=10
        )
        self.ovs_api = impl_idl.OvsdbIdl(self.ovs_conn)

    def close_connection(self):
        if self.ovs_conn:
            self.ovs_conn.stop()
            print("Connection to OVS database closed")
            self.ovs_conn = None
            self.ovs_api = None
            # 还需要关闭并清理idl
            if self.idl:
                self.idl.close()
                self.idl = None

    def __del__(self):
        self.close_connection()
