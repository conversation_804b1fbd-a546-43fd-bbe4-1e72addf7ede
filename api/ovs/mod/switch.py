'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import settings
from dataclasses import dataclass
from string import Template
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl

from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.open_vswitch import impl_idl
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed


class Client:

    def create_ovs_local_switch(self, switch_name):
        ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)

        # 创建与 OVS 数据库的 TCP 连接
        conn = connection.Connection(
            idl=connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch"),
            timeout=10
        )
        ovs_api = impl_idl.OvsdbIdl(conn)
        # 添加端口到桥接
        with ovs_api.transaction(check_error=True) as txn:
            txn.add(ovs_api.add_br(switch_name))
            print(f"Bridge {switch_name} created")

        return True

    def create_ovn_switch(self, switch_name):
        nb_connection_str = "tcp:{}:{}".format(self.host_ip, self.nb_port)

        # 连接到 OVN Northbound 数据库
        conn_nb = connection.Connection(
            idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
            timeout=10
        )
        nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

        with nb_api.transaction(check_error=True) as txn:
            txn.add(nb_api.ls_add(switch_name))
            print(f"Logical switch {switch_name} created")

        return True

    def list_switches(self):
        nb_connection_str = "tcp:{}:{}".format(self.host_ip, self.nb_port)

        # 连接到 OVN Northbound 数据库
        conn_nb = connection.Connection(
            idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
            timeout=10
        )
        nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

        switches = nb_api.ls_list().execute(check_error=True)
        for switch in switches:
            print(f"Logical switch: {switch.name}")

        return True

    def delete_switch(self, switch_name):
        nb_connection_str = "tcp:{}:{}".format(self.host_ip, self.nb_port)

        # 连接到 OVN Northbound 数据库
        conn_nb = connection.Connection(
            idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
            timeout=10
        )
        nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

        with nb_api.transaction(check_error=True) as txn:
            txn.add(nb_api.ls_del(switch_name))
            print(f"Logical switch {switch_name} deleted")

        return True

    def add_port_to_bridge(self, bridge_name, port_name, iface_id):
        ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)

        # 创建与 OVS 数据库的 TCP 连接
        conn = connection.Connection(
            idl=connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch"),
            timeout=10
        )
        ovs_api = impl_idl.OvsdbIdl(conn)
        # 添加端口到桥接
        with ovs_api.transaction(check_error=True) as txn:
            txn.add(ovs_api.add_port(bridge_name, port_name))
            print(f"Port {port_name} added to bridge {bridge_name}")

        with ovs_api.transaction(check_error=True) as txn:
            txn.add(ovs_api.db_set('Interface', port_name, ('external_ids', {'iface-id': iface_id})))
            print(f"Set external_ids for interface {port_name}: iface-id={iface_id}")

        return True

    def create_ovs_bridge(self, bridge_name):
        """创建 OVS 网桥"""
        try:
            with self.ovs_api.transaction(check_error=True) as txn:
                res = txn.add(self.ovs_api.add_br(bridge_name))
                # print(res, vars(res))
            print(f"Bridge {bridge_name} created on {self.host_ip}.")
            return True
        except Exception as e:
            print(f"Failed to create bridge {bridge_name}: {e}")
            return False

    def delete_ovs_bridge(self, bridge_name):
        """删除 OVS 网桥
        
        Returns:
            bool: 如果删除成功或网桥不存在返回 True，删除失败返回 False
        """
        try:
            # 首先检查网桥是否存在
            exists = self.ovs_api.br_exists(bridge_name).execute(check_error=True)
            if not exists:
                print(f"Bridge {bridge_name} does not exist on {self.host_ip}, consider as deleted.")
                return True

            # 网桥存在则删除
            with self.ovs_api.transaction(check_error=True) as txn:
                txn.add(self.ovs_api.del_br(bridge_name))
            print(f"Bridge {bridge_name} deleted from {self.host_ip}.")
            return True
        except Exception as e:
            print(f"Failed to delete bridge {bridge_name}: {e}")
            return False

    @staticmethod
    def check_remote_ovs_service(remote_ip):
        """检查远程主机的 OVS 服务状态（静态方法：纯网络检查）"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 设置超时时间
            result = sock.connect_ex((remote_ip, 6640))  # OVS 默认端口
            sock.close()
            
            if result == 0:
                print(f"OVS service is running on remote host {remote_ip}")
                return True
            else:
                print(f"OVS service is not accessible on remote host {remote_ip}")
                return False
        except Exception as e:
            print(f"Failed to check OVS service on {remote_ip}: {e}")
            return False

    @staticmethod
    def generate_tunnel_name(switch_name, remote_ip):
        """生成唯一的隧道名称"""
        # 使用网桥名称和远程IP组合生成唯一的隧道名称
        return f"geneve-{switch_name}-{remote_ip}"

    def check_geneve_tunnel_status(self, bridge_name, port_name):
        """检查 Geneve 隧道的状态"""
        try:
            # 检查端口状态
            port_info = self.ovs_api.db_find_rows('Port', ('name', '=', port_name)).execute()
            if not port_info:
                return "not_exist"
                
            # 检查接口状态
            iface_info = self.ovs_api.db_find_rows('Interface', ('name', '=', port_name)).execute()
            if not iface_info:
                return "incomplete"
                
            # 获取接口的链路状态
            link_state = iface_info[0].link_state if hasattr(iface_info[0], 'link_state') else None
            if link_state == "up":
                return "up"
            elif link_state == "down":
                return "down"
            else:
                return "unknown"
        except Exception as e:
            print(f"Failed to check tunnel status: {e}")
            return "error"

    def add_geneve_tunnel(self, form):
        """添加 Geneve 隧道"""
        try:
            switch_name = form.get("switch_name", "")
            remote_ip = form.get("remote_ip", "")
            # 生成唯一的隧道名称
            port_name = Client.generate_tunnel_name(switch_name, remote_ip)
            
            print(f"Creating tunnel {port_name} for bridge {switch_name} to remote {remote_ip}")
            
            # 首先检查本地 OVS 服务，使用静态方法
            if not Client.check_remote_ovs_service(remote_ip):
                print(f"Cannot create tunnel: OVS service not available on {remote_ip}")
                return False
                
            # 检查网桥是否存在
            exist = self.ovs_api.br_exists(switch_name).execute()
            if not exist:
                res = self.ovs_api.add_br(switch_name).execute()
                if not res:
                    print(f"Failed to create bridge: {switch_name}")
                    return False

            # 检查 Geneve 端口状态
            tunnel_status = self.check_geneve_tunnel_status(self, switch_name, port_name)
            
            if tunnel_status == "up":
                print(f"Geneve tunnel {port_name} exists and is up")
                return True
            elif tunnel_status == "down":
                print(f"Geneve tunnel {port_name} exists but is down, trying to recreate")
                # 删除现有隧道
                self.delete_port(switch_name, port_name)
            elif tunnel_status == "not_exist":
                print(f"Creating new Geneve tunnel {port_name}")
            else:
                print(f"Tunnel status is {tunnel_status}, trying to recreate")
                self.delete_port(switch_name, port_name)

            with self.ovs_api.transaction() as txn:
                bridges = self.ovs_api.db_find_rows('Bridge', ('name', '=', switch_name)).execute()
                if not bridges:
                    print(f"Bridge {switch_name} not found")
                    return False
                    
                bridge = bridges[0]
                print(f"Creating Geneve tunnel: {port_name}")

                # 创建新的 Geneve 接口
                geneve_iface = txn.add(self.ovs_api.db_create('Interface',
                                                           name=port_name,
                                                           type='geneve',
                                                           options={'remote_ip': remote_ip, 'key': "10"},
                                                           ))

                # 创建新的端口并关联接口
                geneve_port = txn.add(self.ovs_api.db_create('Port',
                                                          name=port_name,
                                                          interfaces=[geneve_iface],
                                                          ))

                # 更新网桥的 ports 字段
                current_ports = bridge.ports if bridge.ports else []
                bridge.ports = current_ports + [geneve_port]
                txn.add(self.ovs_api.db_set('Bridge', bridge.uuid, ('ports', bridge.ports)))

            # 再次检查隧道状态
            final_status = self.check_geneve_tunnel_status(self, switch_name, port_name)
            print(f"Tunnel creation completed. Final status: {final_status}")
            
            return final_status in ["up", "unknown"]

        except Exception as e:
            print(f"Failed to add Geneve tunnel: {e}")
            return False
            
    def delete_port(self, bridge_name, port_name):
        """删除指定端口"""
        try:
            with self.ovs_api.transaction() as txn:
                # 找到网桥
                bridges = self.ovs_api.db_find_rows('Bridge', ('name', '=', bridge_name)).execute()
                if not bridges:
                    print(f"Bridge {bridge_name} does not exist")
                    return False
                    
                bridge = bridges[0]
                
                # 找到要删除的端口
                ports = self.ovs_api.db_find_rows('Port', ('name', '=', port_name)).execute()
                if not ports:
                    print(f"Port {port_name} does not exist")
                    return True  # 端口不存在视为删除成功
                    
                # 更新网桥的 ports 字段，移除要删除的端口
                current_ports = bridge.ports if bridge.ports else []
                new_ports = [p for p in current_ports if p.name != port_name]
                txn.add(self.ovs_api.db_set('Bridge', bridge.uuid, ('ports', new_ports)))
                
                # 删除端口和接口
                txn.add(self.ovs_api.db_destroy('Port', ports[0].uuid))
                
            return True
        except Exception as e:
            print(f"Failed to delete port {port_name}: {e}")
            return False

    def create_tap_interface(self, form, bridge_name="", tap_name=""):
        """创建 TAP 接口端口"""

        print("创建tap接口参数：", form)
        bridge_name = form.get("bridge_name", "")
        tap_name = form.get("tap_name", "")
        mtu = form.get("mtu", "1500")
        vlan_tag = form.get("vlan_tag", "")
        trunk_vlans = form.get("trunk_vlans", "")

        try:
            with self.ovs_api.transaction(check_error=True) as txn:
                # 创建 Port 行
                port_uuid = txn.add(self.ovs_api.db_create('Port', name=tap_name))

                # 创建 Interface 行，并关联到 Port
                interface_uuid = txn.add(self.ovs_api.db_create('Interface', name=tap_name, type='internal',
                                                                options={'mtu_request': str(mtu)}))
                txn.add(self.ovs_api.db_set('Port', port_uuid, ('interfaces', [interface_uuid])))

                if vlan_tag != "":
                    txn.add(self.ovs_api.db_set('Port', port_uuid, ('tag', int(vlan_tag))))
                if trunk_vlans != "":
                    txn.add(self.ovs_api.db_set('Port', port_uuid, ('trunks', trunk_vlans)))

                # 获取网桥并添加端口
                bridges = self.ovs_api.db_find_rows('Bridge', ('name', '=', bridge_name)).execute()
                if not bridges:
                    raise ValueError(f"Bridge {bridge_name} not found.")
                bridge = bridges[0]
                txn.add(self.ovs_api.db_add('Bridge', bridge.uuid, 'ports', port_uuid))

            print(f"TAP interface {tap_name} created and added to bridge {bridge_name}.")
            return True  # 修改: 返回 True 表示操作成功
        except Exception as e:
            print(f"Failed to create TAP interface {tap_name}: {e}")
            return False

    def delete_tap_interface(self, form):
        """从指定网桥删除端口"""
        bridge_name = form.get("bridge_name", "")
        port_name = form.get("port_name", "")
        try:
            with self.ovs_api.transaction(check_error=True) as txn:
                txn.add(self.ovs_api.del_port(port_name, bridge=bridge_name))
            print(f"Port {port_name} deleted from bridge {bridge_name}.")
            return True
        except Exception as e:
            print(f"Failed to delete port {port_name}: {e}")
            return False

    def get_port_info(self, form):
        """从指定网桥删除端口"""
        bridge_name = form.get("bridge_name", "")
        port_name = form.get("port_name", "")
        try:
            # 首先获取指定网桥的信息
            bridge_info = self.ovs_api.db_find_rows('Bridge', ('name', '=', bridge_name)).execute()
            print(len(bridge_info))

            bridge = bridge_info[0]
            for p in bridge.ports:

                if p.name == port_name:
                    print(vars(p))
                    print(p.uuid, p.name)

            return True
        except Exception as e:
            print(f"Failed to get port {port_name}: {e}")
            return False

    def bind_physical_interface(self, switch_name: str, interface_name: str, extra_config: dict = None):
        """
        将物理网卡绑定到 OVS 网桥上
        
        Args:
            switch_name (str): OVS 网桥名称
            interface_name (str): 物理网卡名称（如 eth0, ens33 等）
            extra_config (dict, optional): 额外的端口配置，如 MTU、VLAN 等
        
        Returns:
            dict: 新创建的 OVS 端口信息
            {
                'name': 端口名称,
                'interface': 物理网卡名称,
                'type': 'system',
                'is_physical': True,
                'status': 状态（'active'/'down'/'exists'）,
                'extra_config': 额外配置信息
            }
        
        Raises:
            Exception: 当网桥不存在或操作失败时抛出异常
        """
        try:
            # 检查网桥是否存在
            bridges = self.ovs_api.db_find_rows('Bridge', ('name', '=', switch_name)).execute()
            if not bridges:
                raise Exception(f"Bridge {switch_name} does not exist")

            bridge = bridges[0]

            # 检查物理网卡是否已经被绑定
            ports = self.ovs_api.db_find_rows('Port', ('name', '=', interface_name)).execute()
            if ports:
                ifaces = self.ovs_api.db_find_rows('Interface', ('name', '=', interface_name)).execute()
                port_state = ifaces[0].link_state if ifaces and hasattr(ifaces[0], 'link_state') else 'unknown'
                return {
                    'name': interface_name,
                    'interface': interface_name,
                    'type': 'system',
                    'is_physical': True,
                    'status': 'active' if port_state == 'up' else 'exists',
                    'extra_config': extra_config or {}
                }

            try:
                with self.ovs_api.transaction(check_error=True) as txn:
                    # 创建 Interface
                    interface_uuid = txn.add(self.ovs_api.db_create(
                        'Interface',
                        name=interface_name,
                        type='system'  # system 表示物理网卡
                    ))

                    # 创建 Port 并关联 Interface
                    port_uuid = txn.add(self.ovs_api.db_create(
                        'Port',
                        name=interface_name,
                        interfaces=[interface_uuid]
                    ))

                    # 更新网桥的 ports
                    current_ports = bridge.ports if bridge.ports else []
                    txn.add(self.ovs_api.db_set(
                        'Bridge',
                        bridge.uuid,
                        ('ports', current_ports + [port_uuid])
                    ))

                    # 应用额外配置
                    if extra_config:
                        for key, value in extra_config.items():
                            txn.add(self.ovs_api.db_set(
                                'Interface',
                                interface_uuid,
                                ('options', {key: str(value)})
                            ))

                # 检查端口状态
                ifaces = self.ovs_api.db_find_rows('Interface', ('name', '=', interface_name)).execute()
                port_state = ifaces[0].link_state if ifaces and hasattr(ifaces[0], 'link_state') else 'unknown'

                # 返回新创建的端口信息
                return {
                    'name': interface_name,
                    'interface': interface_name,
                    'type': 'system',
                    'is_physical': True,
                    'status': 'active' if port_state == 'up' else 'down',
                    'extra_config': extra_config or {}
                }

            except Exception as e:
                raise Exception(f"Failed to create port and bind interface: {str(e)}")

        except Exception as e:
            raise Exception(f"Failed to bind physical interface: {str(e)}")

    def unbind_physical_interface(
        self, 
        switch_name: str, 
        interface_name: str, 
        destroy_config: bool = True
    ):
        """
        解绑物理网卡从 OVS 网桥（彻底移除）
        """
        try:
            # 1. 检查网桥是否存在
            bridges = self.ovs_api.db_find_rows('Bridge', ('name', '=', switch_name)).execute()
            if not bridges:
                raise Exception(f"Bridge '{switch_name}' does not exist")
            bridge = bridges[0]

            # 2. 检查端口是否存在
            ports = self.ovs_api.db_find_rows('Port', ('name', '=', interface_name)).execute()
            if not ports:
                raise Exception(f"Interface '{interface_name}' is not bound to any port")
            port = ports[0]

            # 3. 检查 Port 是否属于目标网桥
            port_belongs_to_target_bridge = False
            if hasattr(bridge, 'ports') and bridge.ports:
                port_belongs_to_target_bridge = any(
                    (hasattr(p, 'uuid') and p.uuid == port.uuid) or (isinstance(p, dict) and p.get('uuid') == port.uuid)
                    for p in bridge.ports
                )
            if not port_belongs_to_target_bridge:
                raise Exception(f"Interface '{interface_name}' is not bound to bridge '{switch_name}'")

            # 4. 获取 Interface uuid
            interfaces = self.ovs_api.db_find_rows('Interface', ('name', '=', interface_name)).execute()
            if not interfaces:
                raise Exception(f"Interface row '{interface_name}' not found in OVSDB")
            interface_uuid = interfaces[0].uuid

            with self.ovs_api.transaction(check_error=True) as txn:
                # 先从 Bridge 的 ports 字段移除该 Port
                if hasattr(bridge, 'ports'):
                    new_ports = [p.uuid if hasattr(p, 'uuid') else p for p in bridge.ports if (hasattr(p, 'uuid') and p.uuid != port.uuid) or (isinstance(p, dict) and p.get('uuid') != port.uuid)]
                    txn.add(self.ovs_api.db_set('Bridge', bridge.uuid, ('ports', new_ports)))
                # 再销毁 Port 和 Interface
                if destroy_config:
                    txn.add(self.ovs_api.db_destroy('Port', port.uuid))
                    txn.add(self.ovs_api.db_destroy('Interface', interface_uuid))

            return {"msg": f"Interface {interface_name} unbound and removed from bridge {switch_name}"}

        except Exception as e:
            raise Exception(f"Failed to unbind physical interface: {str(e)}")