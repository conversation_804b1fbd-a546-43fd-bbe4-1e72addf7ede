'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import settings
from dataclasses import dataclass
from string import Template
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl

from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.open_vswitch import impl_idl


class Client:
    
    
    def ovs_create_local_switch(self, switch_name, nic, port_group_name, vlan_id):

        # 创建本地交换机及端口组
        with self.ovs_api.transaction(check_error=True) as txn:
            
            # 创建一个 OVS 桥接器（交换机）
            txn.add(self.ovs_api.add_br(switch_name))
            print(f"Bridge {switch_name} created")
            
            # 添加一个物理网卡到桥接器（绑定到物理网卡）
            txn.add(self.ovs_api.add_port(switch_name, nic))
            print(f"Port {nic} added to bridge {switch_name}")
            
            # 添加一个内部接口并指定 VLAN tag
            txn.add(self.ovs_api.port_add(switch_name, port_group_name, tag=vlan_id))
            txn.add(self.ovs_api.interface_set(port_group_name, type="internal"))
            ###txn.add(self.ovs_api.port_add(switch_name, port_group_name, tag=vlan_id))
            print(f"VLAN port group {port_group_name} with tag {vlan_id} created successfully.")            
            
            
        return True
    
    
    def ovs_create_port_to_switch(self, switch_name, port_name):
        # 创建端口
        with self.ovs_api.transaction(check_error=True) as txn:
            txn.add(self.ovs_api.add_port(switch_name, port_name))
            print(f"Port {port_name} created on bridge {switch_name}")
        return True
        
    

    def ovs_delete_local_switch(self, switch_name):
        
        # 删除桥接
        with self.ovs_api.transaction(check_error=True) as txn:
            txn.add(self.ovs_api.del_br(switch_name))
            print(f"Bridge {switch_name} created")
       
        return True


