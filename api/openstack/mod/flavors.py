import requests
import json
import settings
from api.model.flavors import <PERSON>lav<PERSON>,FlavorDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class FlavorClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_list(self):
        method = "/flavors"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["flavors"]:
            ins = from_dict(data_class=Flavor, data=ins_dict)
            res.append(ins.__dict__)
        return res
    
    def openstack_create_flavor(self, flavorcreateform):
        method = "/flavors"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
    
        temp = """{
                "flavor": {
                    "name": "$name",
                    "ram": $ram,
                    "vcpus": $vcpus,
                    "disk": $disk,
                    "OS-FLV-EXT-DATA:ephemeral": $extdisk
                }
            }"""
        
        t = Template(temp)
        body = t.substitute(flavorcreateform.__dict__)
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=FlavorDetail, data=d["flavor"])
        return ins.__dict__
    
    def openstack_create_flavor_extra_specs(self, id, cpu_node):
        method = "/flavors/%s/os-extra_specs" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
    
        temp = """{
                "extra_specs": {
                    "hw:numa_nodes": "$node"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"node":cpu_node})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        #ins = from_dict(data_class=FlavorDetail, data=d["flavor"])
        return d

    def openstack_create_flavor_extra_specs_key(self, id, cpu_model_id, sys_version):
        method = "/flavors/%s/os-extra_specs" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "extra_specs": {
                    "hw:cpu_flags":"-3dnowext,-3dnow",
                    "hw:cpu_name":"phenom",
                    "hw:cpu_vendor":"AuthenticAMD",
                    "hw:cpu_model_id":"$cpu_model_id",
                    "hw:sys_version":"$sys_version"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"cpu_model_id":cpu_model_id,"sys_version":sys_version})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        #ins = from_dict(data_class=FlavorDetail, data=d["flavor"])
        return d
    
    def openstack_delete_flavor(self, id):
        method = "/flavors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 202:
            return {"msg": "ok"}
        
    def openstack_get_flavor_detail(self, id):
        method = "/flavors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        # ins = from_dict(data_class=FlavorDetail, data=d["flavor"])
        return d["flavor"]
    

        
        