'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import settings
#from model.flavors import Flavor,FlavorDetail
from dataclasses import dataclass
from string import Template
import libvirt
import os

class Client:

    def attach_disk(self, domain_name):

        # 连接到本地的 qemu 实例
        conn = libvirt.open('qemu:///system')
        if conn is None:
            print('Failed to open connection to qemu:///system')
            return False

        # 查找目标虚拟机
        #domain_name = 'test-vm'
        dom = conn.lookupByName(domain_name)
        if dom is None:
            print(f'Failed to find the domain {domain_name}')
            conn.close()
            return False

        # 创建磁盘设备的 XML 配置
        disk_xml = """
        <disk type='file' device='disk'>
            <driver name='qemu' type='qcow2'/>
            <source file='/var/lib/libvirt/images/new-disk.qcow2'/>
            <target dev='sda' bus='scsi'/>
        </disk>
        """

        # 将磁盘设备插入到运行中的虚拟机中
        try:
            dom.attachDeviceFlags(disk_xml, libvirt.VIR_DOMAIN_AFFECT_LIVE)
            print('Disk device attached successfully.')
        except libvirt.libvirtError as e:
            print(f'Failed to attach disk device: {e}')

        # 关闭连接
        conn.close()

        return True