# import random
# from xml.dom.minidom import Document
# import uuid
# import datetime
# import libvirt
#
# from api.libvirt.utils.utils import validate_data
#
#
# class VMClient:
#     state_str = {libvirt.VIR_DOMAIN_NOSTATE: '无状态',
#                  libvirt.VIR_DOMAIN_RUNNING: '运行中',
#                  libvirt.VIR_DOMAIN_BLOCKED: '阻塞',
#                  libvirt.VIR_DOMAIN_PAUSED: '暂停',
#                  libvirt.VIR_DOMAIN_SHUTDOWN: '关机',
#                  libvirt.VIR_DOMAIN_SHUTOFF: '关闭',
#                  libvirt.VIR_DOMAIN_CRASHED: '崩溃',
#                  libvirt.VIR_DOMAIN_PMSUSPENDED: '挂起'}
#
#     @validate_data(["vm_name", "memory_unit", "vcpu_unit", "disk", "interface"])
#
#     # def create_dom_vm(self, data):
#     #     # 创建一个新的 DOM 文档对象
#     #     doc = Document()
#     #
#     #     # 创建根元素 <domain> 并添加属性 type='kvm'
#     #     domain = doc.createElement('domain')
#     #     domain.setAttribute('type', 'kvm')
#     #     doc.appendChild(domain)
#     #
#     #     # 创建 UUID
#     #     uuid_elem = doc.createElement('uuid')
#     #     vm_uuid = str(uuid.uuid4())
#     #     uuid_elem.appendChild(doc.createTextNode(vm_uuid))
#     #     domain.appendChild(uuid_elem)
#     #
#     #     # 创建 <name> 元素
#     #     vm_name = data.get('vm_name')
#     #     name = doc.createElement('name')
#     #     name.appendChild(doc.createTextNode(vm_name))
#     #     domain.appendChild(name)
#     #
#     #     # 创建 <memory> 元素
#     #     memory_unit = data.get('memory_unit')
#     #     memory = doc.createElement('memory')
#     #     memory.appendChild(doc.createTextNode(str(memory_unit)))
#     #     domain.appendChild(memory)
#     #
#     #     # 创建 <vcpu> 元素
#     #     vcpu_unit = data.get('vcpu_unit')
#     #     vcpu = doc.createElement('vcpu')
#     #     vcpu.appendChild(doc.createTextNode(str(vcpu_unit)))
#     #     domain.appendChild(vcpu)
#     #
#     #     # 创建 metadata
#     #     print("data:",data,"vm_name:")
#     #     metadata = create_metadata(doc, data, vm_name, vm_uuid)
#     #     domain.appendChild(metadata)
#     #
#     #     # 创建 sysinfo
#     #     sysinfo = create_sysinfo(doc, vm_uuid)
#     #     domain.appendChild(sysinfo)
#     #
#     #     # 创建 <os> 元素
#     #     os = doc.createElement('os')
#     #     domain.appendChild(os)
#     #
#     #     os_type = doc.createElement('type')
#     #     os_type.setAttribute('arch', 'x86_64')
#     #     os_type.setAttribute('machine', 'pc')
#     #     os_type.appendChild(doc.createTextNode('hvm'))
#     #     os.appendChild(os_type)
#     #
#     #     boot = doc.createElement('boot')
#     #     boot.setAttribute('dev', 'hd')
#     #     os.appendChild(boot)
#     #
#     #     smbios = doc.createElement('smbios')
#     #     smbios.setAttribute('mode', 'sysinfo')
#     #     os.appendChild(smbios)
#     #
#     #     # 创建 features
#     #     features = create_features(doc)
#     #     domain.appendChild(features)
#     #
#     #     # 创建 clock
#     #     clock = create_clock(doc)
#     #     domain.appendChild(clock)
#     #
#     #     # 创建 CPU 配置
#     #     cpu = create_cpu(doc, data.get('vcpu_unit', 2))
#     #     domain.appendChild(cpu)
#     #
#     #     # 创建 <devices> 元素
#     #     devices = doc.createElement('devices')
#     #     domain.appendChild(devices)
#     #
#     #     # 设置启动顺序：先从光盘启动，后从硬盘启动
#     #     boot1 = doc.createElement('boot')
#     #     boot1.setAttribute('dev', 'cdrom')
#     #     os.appendChild(boot1)
#     #
#     #     boot2 = doc.createElement('boot')
#     #     boot2.setAttribute('dev', 'hd')
#     #     os.appendChild(boot2)
#     #
#     #     # 创建网络接口
#     #     network_list = data.get('interface', [])
#     #     for network in network_list:
#     #         interface = create_interface_element(doc, network)
#     #         devices.appendChild(interface)
#     #
#     #     # 创建 <devices> 元素
#     #     devices = doc.createElement('devices')
#     #     domain.appendChild(devices)
#     #
#     #     # 添加系统盘
#     #     if data.get('disk'):
#     #         disk_list = data.get("disk", [])
#     #         for disk_dict in disk_list:
#     #             system_disk = create_local_disk_element(doc, disk_dict)
#     #             devices.appendChild(system_disk)
#     #
#     #     # 添加 CDROM 设备
#     #     if data.get('cdrom'):
#     #         cdrom = create_cdrom_element(doc, data['cdrom'][0])
#     #         devices.appendChild(cdrom)
#     #
#     #     # 添加其他设备
#     #     add_additional_devices(doc, devices)
#     #
#     #     # 添加graphics设备
#     #     if data.get('display_protocol'):
#     #         graphics = create_graphics_element(doc, data.get('display_protocol'))
#     #         devices.appendChild(graphics)
#     #
#     #     # 添加鼠标和键盘
#     #     if data.get('input_devices'):
#     #         create_input_element(doc, devices, data)
#     #
#     #     # 添加GPU设备
#     #     if data.get('enable_gpu'):
#     #         gpu = create_gpu_element(doc)
#     #         devices.appendChild(gpu)
#     #
#     #
#     #     # 生成XML字符串
#     #     vm_xml = doc.toprettyxml(indent="  ")
#     #     print("xxxxxxxxxx:",vm_xml)
#     #
#     #     try:
#     #         dom = self.conn.defineXML(vm_xml)
#     #         if dom is None:
#     #             return {'status': 'failed', 'message': 'Failed to define a persistent domain.'}
#     #
#     #         # 启动虚拟机
#     #         dom.create()
#     #         return {'status': 'success', 'data': data, 'vm_xml': vm_xml}
#     #
#     #     except libvirt.libvirtError as e:
#     #         return {'status': 'failed', 'message': str(e)}
#
# def create_metadata(doc, data, vm_name, vm_uuid):
#     metadata = doc.createElement('metadata')
#     nova_instance = doc.createElement('TheCloud:instance')
#     nova_instance.setAttribute('xmlns:TheCloud', 'http://www.thedatasys.com/')
#
#     # Package version
#     package = doc.createElement('TheCloud:package')
#     package.setAttribute('version', '3.3')
#     nova_instance.appendChild(package)
#
#     # Name
#     nova_name = doc.createElement('TheCloud:name')
#     nova_name.appendChild(doc.createTextNode(vm_name))
#     nova_instance.appendChild(nova_name)
#
#     # Creation time
#     creation_time = doc.createElement('TheCloud:creationTime')
#     current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#     creation_time.appendChild(doc.createTextNode(current_time))
#     nova_instance.appendChild(creation_time)
#
#     # Add flavor
#     flavor = create_flavor_element(doc, data)
#     nova_instance.appendChild(flavor)
#
#     metadata.appendChild(nova_instance)
#     return metadata
#
# def create_flavor_element(doc, data):
#     flavor = doc.createElement('TheCloud:flavor')
#     flavor.setAttribute('name', f"{data.get('vcpu_unit')}-{data.get('memory_unit')//1024}-{data.get('disk_size', 20)}")
#
#     memory = doc.createElement('TheCloud:memory')
#     memory.appendChild(doc.createTextNode(str(data.get('memory_unit')//1024)))
#     flavor.appendChild(memory)
#
#     disk = doc.createElement('TheCloud:disk')
#     disk.appendChild(doc.createTextNode(str(data.get('disk_size', 50))))
#     flavor.appendChild(disk)
#
#     return flavor
#
# def create_sysinfo(doc, vm_uuid):
#     sysinfo = doc.createElement('sysinfo')
#     sysinfo.setAttribute('type', 'smbios')
#
#     system = doc.createElement('system')
#     entries = [
#         ('manufacturer', 'TheCloud Foundation'),
#         ('product', 'TheCloud web'),
#         ('version', '3.3'),
#         ('serial', vm_uuid),
#         ('uuid', vm_uuid),
#         ('family', 'Virtual Machine')
#     ]
#
#     for name, value in entries:
#         entry = doc.createElement('entry')
#         entry.setAttribute('name', name)
#         entry.appendChild(doc.createTextNode(value))
#         system.appendChild(entry)
#
#     sysinfo.appendChild(system)
#     return sysinfo
#
# def create_features(doc):
#     features = doc.createElement('features')
#     feature_list = ['acpi', 'apic', 'pae', 'vmcoreinfo']
#
#     for feature_name in feature_list:
#         feature = doc.createElement(feature_name)
#         features.appendChild(feature)
#
#     return features
#
# def create_clock(doc):
#     clock = doc.createElement('clock')
#     clock.setAttribute('offset', 'utc')
#
#     timers = [
#         ('pit', 'delay'),
#         ('rtc', 'catchup'),
#         ('hpet', None, 'no')
#     ]
#
#     for timer_data in timers:
#         timer = doc.createElement('timer')
#         timer.setAttribute('name', timer_data[0])
#         if len(timer_data) > 1 and timer_data[1]:
#             timer.setAttribute('tickpolicy', timer_data[1])
#         if len(timer_data) > 2:
#             timer.setAttribute('present', timer_data[2])
#         clock.appendChild(timer)
#
#     return clock
#
# def create_cpu(doc, vcpus):
#     cpu = doc.createElement('cpu')
#     cpu.setAttribute('mode', 'host-model')
#     cpu.setAttribute('match', 'exact')
#
#     topology = doc.createElement('topology')
#     topology.setAttribute('sockets', str(vcpus))
#     topology.setAttribute('cores', '1')
#     topology.setAttribute('threads', '1')
#     cpu.appendChild(topology)
#
#     return cpu
#
# def create_disk_element(doc, disk_dict):
#     disk = doc.createElement('disk')
#     disk.setAttribute('type', disk_dict.get('disk_type', 'network'))
#     disk.setAttribute('device', 'disk')
#
#     driver = doc.createElement('driver')
#     driver.setAttribute('name', 'qemu')
#     driver.setAttribute('type', disk_dict.get('format', 'raw'))
#     driver.setAttribute('cache', 'writeback')
#     driver.setAttribute('discard', 'unmap')
#     disk.appendChild(driver)
#
#     if disk_dict.get('disk_type') == 'network':
#         source = doc.createElement('source')
#         source.setAttribute('protocol', 'rbd')
#         source.setAttribute('name', f"volumes/{disk_dict.get('volume_id')}")
#
#         for host in disk_dict.get('hosts', []):
#             host_elem = doc.createElement('host')
#             host_elem.setAttribute('name', host['name'])
#             host_elem.setAttribute('port', str(host['port']))
#             source.appendChild(host_elem)
#
#         disk.appendChild(source)
#
#     target = doc.createElement('target')
#     target.setAttribute('dev', disk_dict.get('target_dev', 'vda'))
#     target.setAttribute('bus', 'virtio')
#     disk.appendChild(target)
#
#     return disk
#
# def create_local_disk_element(doc, disk_dict):
#     disk_size = disk_dict.get('size', 1000000)
#     disk_format = disk_dict.get('format', "qcow2")
#     disk_source_file = disk_dict.get('source')
#     disk_type = disk_dict.get('disk_type')
#
#
#     disk = doc.createElement('disk')
#     disk.setAttribute('type', disk_type)
#     disk.setAttribute('device', 'disk')
#
#
#     driver = doc.createElement('driver')
#     driver.setAttribute('name', 'qemu')
#     driver.setAttribute('type', disk_format)
#     disk.appendChild(driver)
#
#     source = doc.createElement('source')
#     source.setAttribute('file', disk_source_file)
#     disk.appendChild(source)
#
#     target = doc.createElement('target')
#     target.setAttribute('dev', 'vda')
#     target.setAttribute('bus', 'virtio')
#     disk.appendChild(target)
#
#     return disk
#
# def create_cdrom_element(doc, cdrom_dict):
#     """创建CDROM配置"""
#     disk = doc.createElement('disk')
#     disk.setAttribute('type', 'file')
#     disk.setAttribute('device', 'cdrom')
#
#     driver = doc.createElement('driver')
#     driver.setAttribute('name', 'qemu')
#     driver.setAttribute('type', 'raw')
#     disk.appendChild(driver)
#
#     source = doc.createElement('source')
#     source.setAttribute('file', cdrom_dict.get('file'))
#     disk.appendChild(source)
#
#     target = doc.createElement('target')
#     target.setAttribute('dev', 'hdc')
#     target.setAttribute('bus', 'sata')
#     disk.appendChild(target)
#
#     readonly = doc.createElement('readonly')
#     disk.appendChild(readonly)
#
#     return disk
#
# def create_graphics_element(doc, protocol):
#     """创建CDROM配置"""
#     graphics = doc.createElement('graphics')
#     graphics.setAttribute('type', protocol)
#     graphics.setAttribute('port', '-1')
#     graphics.setAttribute('autoport', 'yes')
#     graphics.setAttribute('listen', '0.0.0.0')
#     return graphics
#
#
# def create_input_element(doc, devices, data):
#     """创建CDROM配置"""
#     input_mouse = doc.createElement('input')
#     input_mouse.setAttribute('type', 'mouse')
#     input_mouse.setAttribute('bus', data.get('input_devices', {}).get('mouse', 'usb'))
#     devices.appendChild(input_mouse)
#
#     input_keyboard = doc.createElement('input')
#     input_keyboard.setAttribute('type', 'keyboard')
#     input_keyboard.setAttribute('bus', data.get('input_devices', {}).get('keyboard', 'usb'))
#     devices.appendChild(input_keyboard)
#
# def create_gpu_element(doc):
#     hostdev = doc.createElement('hostdev')
#     hostdev.setAttribute('mode', 'subsystem')
#     hostdev.setAttribute('type', 'pci')
#     hostdev.setAttribute('managed', 'yes')
#     source = doc.createElement('source')
#     address = doc.createElement('address')
#     address.setAttribute('domain', '0x0000')
#     address.setAttribute('bus', '0x03')
#     address.setAttribute('slot', '0x00')
#     address.setAttribute('function', '0x0')
#     source.appendChild(address)
#     hostdev.appendChild(source)
#     return hostdev
#
# def create_interface_element(doc, network):
#     interface = doc.createElement('interface')
#     interface.setAttribute('type', 'network')
#
#     # 添加MAC地址
#     mac = doc.createElement('mac')
#     mac.setAttribute('address', network.get('mac', '52:54:00:58:3e:3f'))
#     interface.appendChild(mac)
#
#     # 添加source元素，明确指定网络为default
#     source = doc.createElement('source')
#     source.setAttribute('network', 'default')  # 明确指定使用default网络
#     interface.appendChild(source)
#
#     # 添加模型类型
#     model = doc.createElement('model')
#     model.setAttribute('type', 'virtio')
#     interface.appendChild(model)
#
#     return interface
#
# def add_additional_devices(doc, devices):
#     # 添加串口设备
#     serial = doc.createElement('serial')
#     serial.setAttribute('type', 'pty')
#     devices.appendChild(serial)
#
#     # 添加显示设备
#     video = doc.createElement('video')
#     model = doc.createElement('model')
#     model.setAttribute('type', 'qxl')
#     model.setAttribute('vram', '262144')
#     video.appendChild(model)
#     devices.appendChild(video)
#
#     # 添加气球设备
#     memballoon = doc.createElement('memballoon')
#     memballoon.setAttribute('model', 'virtio')
#     stats = doc.createElement('stats')
#     stats.setAttribute('period', '10')
#     memballoon.appendChild(stats)
#     devices.appendChild(memballoon)
#
