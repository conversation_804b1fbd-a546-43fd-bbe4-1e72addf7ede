'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import uuid
import time

import settings
# from model.flavors import Flavor,FlavorDetail
from dataclasses import dataclass
from string import Template
import libvirt
import os
from xml.dom.minidom import Document
from api.libvirt.utils.utils import validate_data
from api.libvirt.utils.element import *
import xml.etree.ElementTree as ET
from api.libvirt.utils.vmxml.generate import *
from api.libvirt.utils.vmxml.domain_xml import Domain
from api.libvirt.utils.utils import capacity_to_bytes
from api.libvirt.utils.system_info import get_system_info
from api.libvirt.utils.utils import generate_unique_port_name, generate_random_mac


class VMClient:
    state_str = {
        libvirt.VIR_DOMAIN_NOSTATE:  'nostate',  # 无状态
        libvirt.VIR_DOMAIN_RUNNING: 'running',  # 运行中
        libvirt.VIR_DOMAIN_BLOCKED: 'blocked',  # 阻塞
        libvirt.VIR_DOMAIN_PAUSED: 'paused',  # 暂停
        libvirt.VIR_DOMAIN_SHUTDOWN: 'shutdown',  # 关机
        libvirt.VIR_DOMAIN_SHUTOFF: 'shutoff',  # 关机
        libvirt.VIR_DOMAIN_CRASHED: 'crashed',  # 崩溃
        libvirt.VIR_DOMAIN_PMSUSPENDED: 'suspended',  # 电源管理暂停
    }

    @validate_data(["vm_name", "memory_unit", "vcpu_unit", "disk", "interface"])
    def create_dom_vm(self, data):
        # 生成VM XML配置
        vm_xml = self.generate_vm_xml(self, data)
        
        try:
            dom = self.conn.defineXML(vm_xml)
            if dom is None:
                return {'status': 'failed', 'message': 'Failed to define a persistent domain.'}

            # 启动虚拟机
            dom.create()
            data["vm_xml"] = vm_xml
            
            # 获取虚拟机XML并解析spice/vnc端口
            xml_desc = dom.XMLDesc()
            root = ET.fromstring(xml_desc)
            vnc_port = 0
            spice_port = 0

            for graphics in root.findall(".//graphics"):
                if graphics.get("type") == "vnc":
                    vnc_port = int(graphics.get("port", "0"))
                elif graphics.get("type") == "spice":
                    spice_port = int(graphics.get("port", "0"))

            data["vnc_port"] = vnc_port
            data["spice_port"] = spice_port
            
            # 获取当前虚拟机状态
            state, _ = dom.state()
            data["status"] = VMClient.state_str[state]
            data["result"] = "success"
            data["task_status"] = True

            return data

        except libvirt.libvirtError as e:
            data["result"] = "failed"
            data["error"] = str(e)
            return data

    def update_dom_vm(self, data):
        """
        重新定义虚拟机的XML配置
        """
        # 生成VM XML配置
        new_xml_content = self.generate_vm_xml(data)
        vm_name = data.get("vm_name")
        
        try:
            # 查找虚拟机
            domain = self.conn.lookupByName(vm_name)

            # 获取当前虚拟机状态
            state, _ = domain.state()
            was_running = (state == libvirt.VIR_DOMAIN_RUNNING)

            # 如果虚拟机正在运行，需要先关闭
            if was_running:
                print(f"VM {vm_name} is running, shutting down for XML redefinition...")
                domain.shutdown()

                # 等待虚拟机关闭（最多等待30秒）
                import time
                timeout = 30
                while timeout > 0:
                    state, _ = domain.state()
                    if state == libvirt.VIR_DOMAIN_SHUTOFF:
                        break
                    time.sleep(1)
                    timeout -= 1

                # 如果还没关闭，强制关闭
                if timeout <= 0:
                    print(f"VM {vm_name} shutdown timeout, forcing stop...")
                    domain.destroy()
                    time.sleep(2)

            # 取消定义旧的虚拟机
            domain.undefine()

            # 使用新的XML重新定义虚拟机
            new_domain = self.conn.defineXML(new_xml_content)
            if new_domain is None:
                return {
                    'result': 'failed',
                    'success': False,
                    'message': 'Failed to redefine domain with new XML',
                    'error': 'Failed to redefine domain with new XML'
                }

            # 如果之前是运行状态，重新启动虚拟机
            if was_running:
                print(f"Restarting VM {vm_name} with new configuration...")
                new_domain.create()

            # 获取端口信息
            spice_port, vnc_port = self.get_domain_ports(new_domain)
            
            # 获取VM状态
            state, _ = new_domain.state()
            status = self.get_domain_status_string(state)

            print(f"VM {vm_name} XML redefined successfully.")
            return {
                'result': 'success',
                'success': True,
                'message': f'VM {vm_name} XML redefined successfully',
                'was_running': was_running,
                'spice_port': spice_port,
                'vnc_port': vnc_port,
                'status': status,
                'vm_xml': new_xml_content
            }

        except libvirt.libvirtError as e:
            error_msg = f"Libvirt error redefining VM {vm_name}: {e}"
            print(error_msg)
            return {
                'result': 'failed',
                'success': False,
                'message': error_msg,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"Error redefining VM {vm_name}: {e}"
            print(error_msg)
            return {
                'result': 'failed',
                'success': False,
                'message': error_msg,
                'error': error_msg
            }
            
    def generate_vm_xml(self, data):
        """
        生成虚拟机XML配置的公共函数
        
        Args:
            data (dict): 虚拟机配置数据
            
        Returns:
            str: 生成的XML字符串
        """
        # 创建一个新的 DOM 文档对象
        doc = Document()

        # 创建根元素 <domain> 并添加属性 type='kvm'
        domain = doc.createElement('domain')
        domain.setAttribute('type', data.get('virtualization', 'qemu'))
        doc.appendChild(domain)

        # 添加基本配置元素
        self._add_basic_elements(self, doc, domain, data)
        
        # 添加系统架构相关配置
        self._add_os_and_arch_config(self, doc, domain, data)
        
        # 添加设备配置
        self._add_devices_config(self, doc, domain, data)

        # 生成XML字符串
        vm_xml = doc.toprettyxml(indent="  ")
        print("Generated VM XML:", vm_xml)
        
        return vm_xml

    def _add_basic_elements(self, doc, domain, data):
        """添加基本配置元素：UUID、名称、内存、CPU等"""
        # 创建 UUID
        uuid_elem = doc.createElement('uuid')
        vm_uuid = str(uuid.uuid4())
        uuid_elem.appendChild(doc.createTextNode(vm_uuid))
        domain.appendChild(uuid_elem)

        # 创建 <name> 元素
        vm_name = data.get('vm_name')
        name = doc.createElement('name')
        name.appendChild(doc.createTextNode(vm_name))
        domain.appendChild(name)

        # 创建 <memory> 元素
        memory_unit = data.get('memory_unit')
        memory_unit_type = data.get('memory_unit_type', 'KiB')
        memory = doc.createElement('memory')
        memory.setAttribute('unit', memory_unit_type)
        memory.appendChild(doc.createTextNode(str(memory_unit)))
        domain.appendChild(memory)

        # 添加 currentMemory 配置
        current_memory = doc.createElement('currentMemory')
        current_memory.setAttribute('unit', memory_unit_type)
        current_memory.appendChild(doc.createTextNode(str(memory_unit)))
        domain.appendChild(current_memory)

        # 创建 <vcpu> 元素
        vcpu_unit = data.get('vcpu_unit')
        vcpu = doc.createElement('vcpu')
        vcpu.appendChild(doc.createTextNode(str(vcpu_unit)))
        domain.appendChild(vcpu)

        # 添加事件处理配置
        self._add_event_handlers(self, doc, domain)
        
        # 创建 metadata
        metadata = create_metadata(doc, data, vm_name, vm_uuid)
        domain.appendChild(metadata)

        # 创建 sysinfo
        sysinfo = create_sysinfo(doc, vm_uuid)
        domain.appendChild(sysinfo)

    def _add_event_handlers(self, doc, domain):
        """添加事件处理配置"""
        on_poweroff = doc.createElement('on_poweroff')
        on_poweroff.appendChild(doc.createTextNode('destroy'))
        domain.appendChild(on_poweroff)

        on_reboot = doc.createElement('on_reboot')
        on_reboot.appendChild(doc.createTextNode('restart'))
        domain.appendChild(on_reboot)

        on_crash = doc.createElement('on_crash')
        on_crash.appendChild(doc.createTextNode('destroy'))
        domain.appendChild(on_crash)

    def _add_os_and_arch_config(self, doc, domain, data):
        """添加操作系统和架构相关配置"""
        # 创建 <os> 元素
        os = doc.createElement('os')
        domain.appendChild(os)

        systeminfo = get_system_info()
        arch = systeminfo['machine']
        
        if arch == "aarch64":
            os.setAttribute('firmware', 'efi')
        elif arch == "x86_64":
            # 添加电源管理配置
            pm = doc.createElement('pm')
            suspend_to_mem = doc.createElement('suspend-to-mem')
            suspend_to_mem.setAttribute('enabled', 'no')
            pm.appendChild(suspend_to_mem)
            suspend_to_disk = doc.createElement('suspend-to-disk')
            suspend_to_disk.setAttribute('enabled', 'no')
            pm.appendChild(suspend_to_disk)
            domain.appendChild(pm)
            
        os_type = doc.createElement('type')
        os_type.setAttribute('arch', arch)

        # 动态获取支持的machine类型
        machine_type = data.get('machine_type')
        if not machine_type:
            if arch == 'x86_64':
                machine_type = 'pc'
            elif arch == 'aarch64':
                machine_type = 'virt'
            else:
                machine_type = 'pc'
        os_type.setAttribute('machine', machine_type)

        os_type.appendChild(doc.createTextNode('hvm'))
        os.appendChild(os_type)

        # 设置启动顺序
        if data.get('cdrom'):
            boot1 = doc.createElement('boot')
            boot1.setAttribute('dev', 'cdrom')
            os.appendChild(boot1)

        boot = doc.createElement('boot')
        boot.setAttribute('dev', 'hd')
        os.appendChild(boot)

        smbios = doc.createElement('smbios')
        smbios.setAttribute('mode', 'sysinfo')
        os.appendChild(smbios)

        # 创建 features
        features = create_features(doc, arch)
        domain.appendChild(features)

        # 创建 clock
        clock = create_clock(doc)
        domain.appendChild(clock)

        # 创建 CPU 配置
        cpu = create_cpu(doc, data.get('vcpu_unit', 2), arch)
        domain.appendChild(cpu)

    def _add_devices_config(self, doc, domain, data):
        """添加设备配置"""
        # 创建 <devices> 元素
        devices = doc.createElement('devices')
        domain.appendChild(devices)

        # 添加网络接口
        self._add_network_interfaces(self, doc, devices, data)
        
        # 添加磁盘设备
        self._add_disk_devices(self, doc, devices, data)
        
        # 添加 CDROM 设备
        self._add_cdrom_device(self, doc, devices, data)
        
        # 添加其他设备
        systeminfo = get_system_info()
        arch = systeminfo['machine']
        add_additional_devices(doc, devices, arch, data.get('display_protocol'))

        # 添加graphics设备
        if data.get('display_protocol'):
            graphics = create_graphics_element(doc, data.get('display_protocol'))
            devices.appendChild(graphics)

        # 添加鼠标和键盘
        if data.get('input_devices'):
            create_input_element(doc, devices, data)

        # 添加GPU设备
        if data.get('enable_gpu'):
            gpu = create_gpu_element(doc)
            devices.appendChild(gpu)

    def _add_network_interfaces(self, doc, devices, data):
        """添加网络接口配置"""
        network_list = data.get('interface_info', [])
        for network in network_list:
            interface = create_interface_element_ovs(doc, network)
            devices.appendChild(interface)

    def _add_disk_devices(self, doc, devices, data):
        """添加磁盘设备配置"""
        used_devices = set()  # 记录已使用的设备名
        
        if data.get('disk'):
            disk_list = data.get("disk", [])
            for disk_dict in disk_list:
                ceph_cluster_id = disk_dict.get("ceph_cluster_id", "")
                
                if ceph_cluster_id != "":
                    # 处理Ceph磁盘
                    self._add_ceph_disk(self, doc, devices, disk_dict, used_devices)
                else:
                    # 处理本地磁盘
                    self._add_local_disk(self, doc, devices, disk_dict, used_devices)

    def _add_ceph_disk(self, doc, devices, disk_dict, used_devices):
        """添加Ceph磁盘配置"""
        bus = disk_dict.get("bus", "virtio")
        dev = self._get_next_device_name(self, bus, used_devices)
        
        # 创建ceph磁盘元素
        disk = doc.createElement('disk')
        disk.setAttribute('type', 'network')
        disk.setAttribute('device', 'disk')
        
        # 设置driver
        driver = doc.createElement('driver')
        driver.setAttribute('name', 'qemu')
        driver.setAttribute('type', 'raw')
        driver.setAttribute('cache', 'writeback')
        driver.setAttribute('discard', 'unmap')
        disk.appendChild(driver)
        
        # 设置认证信息
        if disk_dict.get('pool_username') and disk_dict.get('pool_keyring'):
            username = disk_dict.get('pool_username')
            keyring = disk_dict.get('pool_keyring')
            success, secret_uuid = self.get_or_create_ceph_secret(self, username, keyring)
            
            auth = doc.createElement('auth')
            auth.setAttribute('username', disk_dict['pool_username'])
            secret = doc.createElement('secret')
            secret.setAttribute('type', 'ceph')
            if success:
                secret.setAttribute('uuid', secret_uuid)
            auth.appendChild(secret)
            disk.appendChild(auth)
        
        # 设置source
        source = doc.createElement('source')
        source.setAttribute('protocol', 'rbd')
        source.setAttribute('name', disk_dict['path'])
        
        # 添加monitor节点信息
        if disk_dict.get('mon_hosts'):
            mon_hosts = disk_dict['mon_hosts'].split(',')
            for mon in mon_hosts:
                ip, port = mon.strip().split(':')
                host = doc.createElement('host')
                host.setAttribute('name', ip)
                host.setAttribute('port', port)
                source.appendChild(host)
        disk.appendChild(source)
        
        # 设置target
        target = doc.createElement('target')
        target.setAttribute('dev', dev)
        target.setAttribute('bus', bus)
        disk.appendChild(target)
        
        devices.appendChild(disk)

    def _add_local_disk(self, doc, devices, disk_dict, used_devices):
        """添加本地磁盘配置"""
        bus = disk_dict.get("bus", "virtio")
        dev = self._get_next_device_name(self, bus, used_devices)
        
        storage_type_code = disk_dict.get('storage_type_code')
        if storage_type_code is None or storage_type_code == '':
            storage_type_code = 'qcow2'
            disk_dict['storage_type_code'] = storage_type_code
            
        system_disk = create_local_disk_element1(doc, disk_dict, dev)
        devices.appendChild(system_disk)

    def _get_next_device_name(self, bus, used_devices):
        """获取下一个可用的设备名"""
        if bus == "virtio":
            dev_prefix = "vd"
        elif bus in ("sata", "scsi"):
            dev_prefix = "sd"
        elif bus == "ide":
            dev_prefix = "hd"
        else:
            raise ValueError(f"Unsupported bus type: {bus}")
        
        # 找到第一个未使用的设备名
        for letter_code in range(ord('a'), ord('z')+1):
            dev = f"{dev_prefix}{chr(letter_code)}"
            if dev not in used_devices:
                used_devices.add(dev)
                return dev
        
        raise RuntimeError("No available device names (a-z exhausted)")

    def _add_cdrom_device(self, doc, devices, data):
        """添加CDROM设备配置"""
        if data.get('cdrom'):
            cdrom_list = data['cdrom']
            if isinstance(cdrom_list, list) and cdrom_list:
                first_cdrom = cdrom_list[0]
                if isinstance(first_cdrom, dict):
                    cdrom_path = first_cdrom.get('path', '').strip()
                    if cdrom_path:
                        systeminfo = get_system_info()
                        arch = systeminfo['machine']
                        cdrom = create_cdrom_element(doc, first_cdrom, arch)
                        devices.appendChild(cdrom)

    def test_create_vm(self):
        # 定义虚拟机的XML配置
        vm_xml = """
        <domain type='kvm'>
        <name>test-an</name>
        <memory unit='KiB'>1048576</memory>
        <vcpu placement='static'>1</vcpu>
        <os>
            <type arch='x86_64' machine='pc-i440fx-2.9'>hvm</type>
            <boot dev='hd'/>
        </os>
        <devices>
            <disk type='file' device='disk'>
            <driver name='qemu' type='qcow2'/>
            <source file='/var/lib/libvirt/images/an84.qcow2'/>
            <target dev='vda' bus='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x04' function='0x0'/>
            </disk>
            <interface type='network'>
            <mac address='52:54:00:6b:3c:52'/>
            <source network='default'/>
            <model type='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x03' function='0x0'/>
            </interface>
            <graphics type='vnc' port='-1' autoport='yes'/>
        </devices>
        </domain>
        """

        # 创建虚拟机
        try:
            self.conn.createXML(vm_xml, 0)
            print('Virtual Machine created successfully.')
        except libvirt.libvirtError as e:
            print(f'Failed to create VM: {e}')

        return True

    def test_define_vm(self):

        # 连接到 libvirtd 服务
        conn = libvirt.open('qemu:///system')
        if conn is None:
            print('Failed to open connection to qemu:///system')
            return

        # 定义虚拟机的XML配置
        vm_xml = """
        <domain type='kvm'>
        <name>test-an</name>
        <memory unit='KiB'>1048576</memory>
        <vcpu placement='static'>1</vcpu>
        <os>
            <type arch='x86_64' machine='pc-i440fx-2.9'>hvm</type>
            <boot dev='hd'/>
        </os>
        <devices>
            <disk type='file' device='disk'>
            <driver name='qemu' type='qcow2'/>
            <source file='/var/lib/libvirt/images/an84.qcow2'/>
            <target dev='vda' bus='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x04' function='0x0'/>
            </disk>
            <interface type='network'>
            <mac address='52:54:00:6b:3c:52'/>
            <source network='default'/>
            <model type='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x03' function='0x0'/>
            </interface>
            <graphics type='vnc' port='-1' autoport='yes'/>
        </devices>
        </domain>
        """

        # 创建虚拟机
        try:
            dom = conn.defineXML(vm_xml)
            if dom is None:
                print('Failed to define a persistent domain.')
            else:
                print('Virtual Machine defined successfully.')

            # 启动虚拟机
            dom.create()
            print('Virtual Machine started successfully.')
        except libvirt.libvirtError as e:
            print(f'Failed to create VM: {e}')

        # 关闭连接
        conn.close()
        return True

    def get_vm_list(self):
        vms = []
        domains = self.conn.listAllDomains()
        for domain in domains:
            state, reason = domain.state()
            info = domain.info()
            print(state, reason, info)
            vm = {
                "name": domain.name(),
                "cpu_count": info[3],
                "memory": info[1] / 1024,
                "uuid": domain.UUIDString(),
                # "xml": domain.XMLDesc(),
                "state": VMClient.state_str[state]
            }
            vms.append(vm)

        return vms

    def get_vm_info_by_name(self, name):
        """
        通过名字获取虚拟机详情
        """
        domain = self.conn.lookupByName(name)
        info = domain.info()
        state, reason = domain.state()
        xml = domain.XMLDesc()
        uuid = domain.UUIDString()
        disk_paths = []

        # 使用 ElementTree 解析 XML 配置
        tree = ET.ElementTree(ET.fromstring(xml))
        root = tree.getroot()
        for disk in root.findall(".//disk"):
            source = disk.find(".//source")
            if source is not None:
                file_path = source.get("file")  # 获取磁盘文件的路径
                if file_path:
                    disk_paths.append(file_path)
        
        # 获取虚拟化类型
        virtualization = root.get("type", "kvm")

        # 获取图形端口信息和display_protocol
        spice_port = 0
        vnc_port = 0
        display_protocol = None
        for graphics in root.findall(".//graphics"):
            g_type = graphics.get("type")
            port = graphics.get("port")
            if g_type == "spice" and port:
                spice_port = int(port)
                display_protocol = "spice"
            elif g_type == "vnc" and port:
                vnc_port = int(port)
                display_protocol = "vnc"

        # 获取输入设备
        input_devices = {"mouse": None, "keyboard": None}
        for input_elem in root.findall(".//input"):
            input_type = input_elem.get("type")
            input_bus = input_elem.get("bus")
            if input_type == "mouse":
                input_devices["mouse"] = input_bus
            elif input_type == "keyboard":
                input_devices["keyboard"] = input_bus
        # 默认值
        if not input_devices["mouse"]:
            input_devices["mouse"] = "usb"
        if not input_devices["keyboard"]:
            input_devices["keyboard"] = "usb"

        vm = {
            "name": domain.name(),
            "cpu_count": info[3],
            "memory": info[1] / 1024,
            "uuid": uuid,
            "xml": xml,
            "state": VMClient.state_str[state],
            "disk": disk_paths,
            "spice_port": spice_port,
            "vnc_port": vnc_port,
            "virtualization": virtualization,
            "display_protocol": display_protocol or "spice",
            "input_devices": input_devices
        }
        return vm

    def start_vm(self, vm_name):
        """启动虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            state, _ = domain.state()
        
            # 检查虚拟机状态 (state[0] 是状态码)
            if state == libvirt.VIR_DOMAIN_RUNNING:
                print(f"Virtual Machine {vm_name} is already running.")
                return True
            domain.create()  # 启动虚拟机
            print(f"Virtual Machine {vm_name} started.")
            return True
        except libvirt.libvirtError as e:
            error_code = e.get_error_code()
            if error_code == libvirt.VIR_ERR_NO_DOMAIN:
                print(f"Virtual Machine {vm_name} not found.")
            else:
                print(f"Error starting VM {vm_name}: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error starting VM {vm_name}: {e}")
            return False

    # 停止虚拟机
    def stop_vm(self, vm_name):
        """停止虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.shutdown()  # 关机命令
            print(f"Virtual Machine {vm_name} is shutting down.")
        except Exception as e:
            print(f"Error stopping VM: {e}")

    # 强制停止虚拟机
    def force_stop_vm(self, vm_name):
        """强制停止虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.destroy()  # 强制关闭虚拟机
            print(f"Virtual Machine {vm_name} is forcefully stopped.")
        except Exception as e:
            print(f"Error forcing stop VM: {e}")

    # 重启虚拟机
    def reboot_vm(self, vm_name):
        """重启虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.reboot()  # 重启虚拟机
            print(f"Virtual Machine {vm_name} is rebooting.")
        except Exception as e:
            print(f"Error rebooting VM: {e}")

    def migrate_vm(self, vm_name, target_host, migration_type="live", flags=None):
        """
        迁移虚拟机到目标主机

        Args:
            vm_name (str): 虚拟机名称
            target_host (str): 目标主机IP或URI
            migration_type (str): 迁移类型 - "live"(热迁移) 或 "offline"(冷迁移)
            flags (int): libvirt迁移标志位，如果为None则使用默认值

        Returns:
            dict: 迁移结果
        """
        try:
            domain = self.conn.lookupByName(vm_name)

            # 构建目标主机连接URI
            if target_host.startswith('qemu'):
                target_uri = target_host
            else:
                target_uri = f'qemu+tcp://{target_host}/system'

            # 连接到目标主机
            target_conn = libvirt.open(target_uri)
            if target_conn is None:
                return {
                    'status': 'failed',
                    'message': f'无法连接到目标主机: {target_host}'
                }

            # 设置迁移标志
            if flags is None:
                if migration_type == "live":
                    # 热迁移标志
                    flags = (libvirt.VIR_MIGRATE_LIVE |           # 热迁移
                            libvirt.VIR_MIGRATE_PEER2PEER |       # 点对点迁移
                            libvirt.VIR_MIGRATE_PERSIST_DEST |    # 在目标主机持久化
                            libvirt.VIR_MIGRATE_UNDEFINE_SOURCE)  # 从源主机取消定义
                else:
                    # 冷迁移标志
                    flags = (libvirt.VIR_MIGRATE_PEER2PEER |      # 点对点迁移
                            libvirt.VIR_MIGRATE_PERSIST_DEST |    # 在目标主机持久化
                            libvirt.VIR_MIGRATE_UNDEFINE_SOURCE)  # 从源主机取消定义

            # 检查虚拟机状态
            state, _ = domain.state()
            if migration_type == "live" and state != libvirt.VIR_DOMAIN_RUNNING:
                return {
                    'status': 'failed',
                    'message': f'虚拟机 {vm_name} 未运行，无法进行热迁移'
                }

            print(f"开始迁移虚拟机 {vm_name} 到 {target_host} (类型: {migration_type})")

            # 执行迁移
            migrated_domain = domain.migrate(target_conn, flags, None, target_uri, 0)

            if migrated_domain is None:
                return {
                    'status': 'failed',
                    'message': f'虚拟机 {vm_name} 迁移失败'
                }

            # 关闭目标连接
            target_conn.close()

            print(f"虚拟机 {vm_name} 成功迁移到 {target_host}")
            return {
                'status': 'success',
                'message': f'虚拟机 {vm_name} 成功迁移到 {target_host}',
                'vm_name': vm_name,
                'target_host': target_host,
                'migration_type': migration_type
            }

        except libvirt.libvirtError as e:
            error_msg = f"libvirt错误: {str(e)}"
            print(f"迁移虚拟机失败: {error_msg}")
            return {
                'status': 'failed',
                'message': error_msg,
                'vm_name': vm_name,
                'target_host': target_host
            }
        except Exception as e:
            error_msg = f"迁移虚拟机时发生未知错误: {str(e)}"
            print(error_msg)
            return {
                'status': 'failed',
                'message': error_msg,
                'vm_name': vm_name,
                'target_host': target_host
            }

    def check_migration_support(self, vm_name, target_host):
        """
        检查虚拟机是否支持迁移到目标主机

        Args:
            vm_name (str): 虚拟机名称
            target_host (str): 目标主机IP或URI

        Returns:
            dict: 检查结果
        """
        try:
            domain = self.conn.lookupByName(vm_name)

            # 构建目标主机连接URI
            if target_host.startswith('qemu'):
                target_uri = target_host
            else:
                target_uri = f'qemu+tcp://{target_host}/system'

            # 连接到目标主机
            target_conn = libvirt.open(target_uri)
            if target_conn is None:
                return {
                    'supported': False,
                    'message': f'无法连接到目标主机: {target_host}'
                }

            # 检查目标主机是否支持迁移
            try:
                # 获取虚拟机XML
                xml_desc = domain.XMLDesc()

                # 在目标主机上验证XML配置
                target_conn.domainXMLFromNative("qemu-argv", xml_desc)

                target_conn.close()

                return {
                    'supported': True,
                    'message': f'虚拟机 {vm_name} 支持迁移到 {target_host}'
                }

            except libvirt.libvirtError as e:
                target_conn.close()
                return {
                    'supported': False,
                    'message': f'目标主机不支持此虚拟机配置: {str(e)}'
                }

        except Exception as e:
            return {
                'supported': False,
                'message': f'检查迁移支持时发生错误: {str(e)}'
            }

    def force_reboot_vm(self, vm_name):
        """重启虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.destroy()  # 强制关闭虚拟机
            domain.create()  # 重新启动虚拟机 # 重启虚拟机
            print(f"Virtual Machine {vm_name} is rebooting.")
        except Exception as e:
            print(f"Error rebooting VM: {e}")

    def suspend_vm(self, vm_name):
        """暂停虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.suspend()  # 暂停虚拟机
            print(f"Virtual Machine {vm_name} is suspend.")
        except Exception as e:
            print(f"Error suspending VM: {e}")

    def resume_vm(self, vm_name):
        """恢复虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.resume()  # 恢复虚拟机
            print(f"Virtual Machine {vm_name} is resume.")
        except Exception as e:
            print(f"Error resuming VM: {e}")

    # 获取虚拟机的 XML 配置
    def get_vm_xml(self, vm_name):
        """获取虚拟机的 XML 配置"""
        try:
            domain = self.conn.lookupByName(vm_name)
            xml_desc = domain.XMLDesc()  # 获取虚拟机的 XML 配置
            return xml_desc
        except Exception as e:
            print(f"Error getting VM XML: {e}")

    def del_vm(self, vm_name):
        """
        根据名称删除虚拟机及其磁盘文件
        """
        try:
            # 获取domain对象
            domain = self.conn.lookupByName(vm_name)

            # 获取磁盘信息
            xml_desc = domain.XMLDesc()
            root = ET.fromstring(xml_desc)
            disk_paths = []
            for disk in root.findall(".//disk"):
                source = disk.find(".//source")
                if source is not None:
                    file_path = source.get("file")
                    if file_path:
                        disk_paths.append(file_path)

            # 检查并关闭虚拟机
            state, _ = domain.state()
            if state == libvirt.VIR_DOMAIN_RUNNING:
                domain.destroy()
                time.sleep(2)  # 等待虚拟机完全关闭

            # 取消定义虚拟机
            domain.undefineFlags(libvirt.VIR_DOMAIN_UNDEFINE_MANAGED_SAVE |
                                 libvirt.VIR_DOMAIN_UNDEFINE_SNAPSHOTS_METADATA)

            # 删除磁盘文件
            # for disk_path in disk_paths:
            #     if os.path.exists(disk_path):
            #         try:
            #             os.remove(disk_path)
            #         except OSError as e:
            #             print(f"删除磁盘文件失败: {disk_path}, 错误: {e}")

            return True

        except libvirt.libvirtError as e:
            err_msg = str(e)
            # 判断是否为"虚拟机不存在"错误
            if "Domain not found" in err_msg or "no domain with matching name" in err_msg:
                print(f"虚拟机不存在，视为删除成功: {vm_name}")
                return True
            print(f"删除虚拟机失败: {vm_name}, 错误: {e}")
            return False
        except Exception as e:
            print(f"删除虚拟机时发生未知错误: {vm_name}, 错误: {e}")
            return False

    def set_vm_memory(self, vm_name, new_memory):
        """动态增加虚拟机内存"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.setMemory(new_memory)  # 设置内存，单位是字节
            print(f"Memory of VM {vm_name} set to {new_memory} bytes.")
        except Exception as e:
            print(f"Error setting memory for VM {vm_name}: {e}")

    def set_vm_vcpus(self, vm_name, new_vcpus):
        """动态增加虚拟机 CPU 核心数"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.setVcpus(new_vcpus)  # 设置 CPU 核心数
            print(f"VM {vm_name} now has {new_vcpus} vCPUs.")
        except Exception as e:
            print(f"Error setting vCPUs for VM {vm_name}: {e}")

    def redefine_vm_xml(self, vm_name, new_xml_content):
        """
        重新定义虚拟机的XML配置
        """
        try:
            # 查找虚拟机
            domain = self.conn.lookupByName(vm_name)

            # 获取当前虚拟机状态
            state, _ = domain.state()
            was_running = (state == libvirt.VIR_DOMAIN_RUNNING)

            # 如果虚拟机正在运行，需要先关闭
            if was_running:
                print(f"VM {vm_name} is running, shutting down for XML redefinition...")
                domain.shutdown()

                # 等待虚拟机关闭（最多等待30秒）
                import time
                timeout = 30
                while timeout > 0:
                    state, _ = domain.state()
                    if state == libvirt.VIR_DOMAIN_SHUTOFF:
                        break
                    time.sleep(1)
                    timeout -= 1

                # 如果还没关闭，强制关闭
                if timeout <= 0:
                    print(f"VM {vm_name} shutdown timeout, forcing stop...")
                    domain.destroy()
                    time.sleep(2)

            # 取消定义旧的虚拟机
            domain.undefine()

            # 使用新的XML重新定义虚拟机
            new_domain = self.conn.defineXML(new_xml_content)
            if new_domain is None:
                return {
                    'success': False,
                    'message': 'Failed to redefine domain with new XML'
                }

            # 如果之前是运行状态，重新启动虚拟机
            if was_running:
                print(f"Restarting VM {vm_name} with new configuration...")
                new_domain.create()

            print(f"VM {vm_name} XML redefined successfully.")
            return {
                'success': True,
                'message': f'VM {vm_name} XML redefined successfully',
                'was_running': was_running
            }

        except libvirt.libvirtError as e:
            error_msg = f"Libvirt error redefining VM {vm_name}: {e}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
        except Exception as e:
            error_msg = f"Error redefining VM {vm_name}: {e}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
            
            
    def get_unique_vm_name(self, base_name):
        """自动生成唯一虚拟机名"""
        name = base_name
        idx = 1
        while True:
            try:
                self.conn.lookupByName(name)
                # 已存在，生成新名字
                name = f"{base_name}-clone-{idx}"
                idx += 1
            except libvirt.libvirtError:
                # 没有同名虚拟机，可以用
                break
        return name
    
    def get_unique_volume_name(self, pool_name, base_name):
        """自动生成唯一卷名"""
        name = base_name
        idx = 1
        pool = self.conn.storagePoolLookupByName(pool_name)
        while True:
            try:
                pool.storageVolLookupByName(name)
                # 已存在，生成新名字
                name = f"{base_name}-{idx}"
                idx += 1
            except libvirt.libvirtError:
                # 没有同名卷，可以用
                break
        return name

    def vm_clone(self, form):
        """
        有问题
        需要关闭虚拟机以后进行
        """
        name = form["vm_name"]
        clone_name = form.get("clone_name")
        if not clone_name:
            clone_name = name
        clone_name = self.get_unique_vm_name(self, clone_name)
        clone = form.get("clone", "1")  # 克隆方式：链接克隆，全量克隆
        # 获取虚拟机信息
        domain = self.conn.lookupByName(name)
        # 判断虚拟机的状态
        state, _ = domain.state()
        if state == libvirt.VIR_DOMAIN_RUNNING:
            # 关机
            domain.destroy()  # 强制关闭虚拟机

        xml_desc = domain.XMLDesc()

        # 解析 XML 配置
        root = ET.fromstring(xml_desc)
        # 修改虚拟机名称
        name_elem = root.find('.//name')
        if name_elem is not None:
            name_elem.text = clone_name
        # 去掉 UUID
        uuid_elem = root.find('.//uuid')
        if uuid_elem is not None:
            root.remove(uuid_elem)

        # 1. 先构建 volume_name 到新卷信息的映射
        new_disk_map = {}
        for disk in form["disks"]:
            base_new_volume_name = disk["volume_name"] + "-clone"
            new_volume_name = self.get_unique_volume_name(self, disk["storage_pool_name"], base_new_volume_name)
            clone_form = {
                "volume_name": disk["volume_name"],
                "new_volume_name": new_volume_name,
                "storage_pool_name": disk["storage_pool_name"]
            }
            new_volume = self.clone_storage_pool_volume(self, clone_form)
            disk_info = self.get_storage_pool_volume_info(
                self, disk["storage_pool_name"], clone_form["new_volume_name"])
            disk_info["storage_pool_id"] = disk["storage_pool_id"]
            disk_info["volume_id"] = disk.get("volume_id")
            disk_info["volume_name"] = disk["volume_name"]
            new_disk_map[disk["volume_name"]] = disk_info

        # 2. 遍历 XML <disk>，按 volume_name 匹配
        disk_info_list = []
        for disk in root.findall('.//disk'):
                source = disk.find('source')
                if source is not None:
                    old_path = source.get('file')
                matched = None
                for d in form["disks"]:
                    # 你可以用路径或 volume_name 匹配
                    if d.get("volume_name") in old_path:
                        matched = d["volume_name"]
                        break
                if matched and matched in new_disk_map:
                    new_disk = new_disk_map[matched]
                    source.set('file', new_disk["path"])
                    disk_info_list.append({
                        "path": new_disk["path"],
                        "volume_name": new_disk.get("volume_name"),
                        "volume_id": new_disk.get("volume_id"),
                        "pool_name": new_disk.get("pool_name"),
                        "storage_pool_id": new_disk.get("storage_pool_id"),
                        "capacity": new_disk.get("capacity"),
                        "allocation": new_disk.get("allocation"),
                    })
                else:
                    root.remove(disk)

        # 网络处理
        network_info = []
        interfaces = root.findall('.//interface')
        for idx, net in enumerate(form.get("networks", [])):
            if idx >= len(interfaces):
                break
            interface = interfaces[idx]
            new_port_name = generate_unique_port_name(prefix=clone_name)
            new_mac = generate_random_mac()
            source = interface.find('source')
            if source is not None and 'port' in source.attrib:
                source.set('port', new_port_name)
            mac_elem = interface.find('mac')
            if mac_elem is not None:
                mac_elem.set('address', new_mac)
            network_info.append({
                "port_name": new_port_name,
                "mac": new_mac,
                "type": interface.get('type'),
                "source": source.get('bridge') if source is not None else None,
                "switch_id": net.get("switch_id"),
                "switch_port_id": net.get("switch_port_id"),
                "switch_port_group_id": net.get("switch_port_group_id"),
                "switch_name": net.get("switch_name"),
                "switch_port_name": net.get("switch_port_name"),
                "switch_port_group_name": net.get("switch_port_group_name"),
            })

        new_xml = ET.tostring(root, encoding='unicode', method='xml')
        dom = self.conn.defineXML(new_xml)
        if dom is None:
            return {'status': 'failed', 'message': 'Failed to define a persistent domain.'}

            # 启动虚拟机
        dom.create()

        state, _ = dom.state()
        xml_desc = dom.XMLDesc()
        root = ET.fromstring(xml_desc)
        vnc_port = 0
        spice_port = 0
        for graphics in root.findall(".//graphics"):
            g_type = graphics.get("type")
            port = graphics.get("port")
            if g_type == "spice" and port:
                spice_port = int(port)
            elif g_type == "vnc" and port:
                vnc_port = int(port)
        status_str = VMClient.state_str.get(state, str(state))
        vm_info = {
            "name": clone_name,
            "uuid": dom.UUIDString(),
            "status": status_str,
            "spice_port": spice_port,
            "vnc_port": vnc_port,
            # 可补充更多字段
        }

        return {
            "vm_info": vm_info,
            "disk_info": disk_info_list,
            "network_info": network_info
        }

    def test_create_vm_xml(self, form):
        name = form["name"]
        mem = form["memory"]
        vcpu = form["vcpu"]
        disk_form = form["disks"]
        print(self.conn.getVersion())
        dom = Domain()
        dom.set_name(name)

        vm_mem = str(capacity_to_bytes(mem))
        dom.set_memory(vm_mem)
        dom.set_current_memory(vm_mem)
        dom.set_vcpu(vcpu)

        os_boot = dom.set_os()
        host_info = self.get_host_info(self)  # 获取远程主机信息
        os_boot.set_type("hvm", attributes={
                         "arch": host_info["arch"], "machine": "pc-i440fx-oracular"})
        os_boot.set_boot("", attributes={"dev": "hd"})
        os_boot.set_boot_menu("")

        feature = dom.set_feature()
        feature.set_acpi()
        feature.set_apic()
        feature.set_vmport({"vmport": "off"})

        cpu = dom.set_cpu()
        cpu.set_atr_model("host-passthrough")

        clock = dom.set_clock()
        clock.set_default_timer()

        dom.set_on_poweroff()
        dom.set_on_reboot()
        dom.set_on_crash()

        pm = dom.set_pm()
        pm.set_suspend_to_mem()
        pm.set_suspend_to_disk()

        # devices设备处理
        devices = dom.set_devices()

        # 磁盘
        for d in disk_form:
            disk_atr_type = d["disk_atr_type"]
            disk = devices.set_disk()
            disk.set_atr_type(disk_atr_type)
            disk.set_atr_device()
            disk.set_driver(d["driver_type"])
            if disk_atr_type == "file":
                d_source_atr = {
                    "file": d["path"]
                }
                disk.set_source(d_source_atr)
            elif disk_atr_type == "block":
                d_source_atr = {
                    "dev": d["path"]
                }
                disk.set_source(d_source_atr)

            disk.set_target()

        # 控制器

        # 网络

        devices.set_console()
        devices.set_channel("")

        devices.set_default_input()
        devices.set_default_graphics()
        devices.set_audio_backend()
        devices.set_default_sound()

        devices.set_default_redirect()
        # devices.set_default_redirect()

        xml_desc = dom.format_xml()

        return xml_desc
