import libvirt


class Host:

    def get_host_info(self):
        host_info = self.conn.getInfo()
        info = {
            "arch": host_info[0],
            "mem": host_info[1],
            "cpu_num": host_info[2],
            "cpu_clock_frequency": host_info[3],
            "numa": host_info[4],
            "cpu_num_on_numa": host_info[5],
            "cpu_slot_cpu_num": host_info[6],
            "single_core_num": host_info[7]
        }

        return info

    def get_version(self):
        v = self.conn.getVersion()
        print(v)
        return v

    def get_capabilities(self):
        ret = self.conn.getCapabilities()
        print(ret)
        return ret

    def get_storage_capabilities(self):
        ret = self.conn.getStoragePoolCapabilities()
        print("====================")
        print(ret)
        print("====================")
        return ret

    def get_domain_capabilities(self):
        ret = self.conn.getDomainCapabilities()
        print("====================")
        print(ret)
        print("====================")
        return ret
