'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import time

import settings
# from model.flavors import Flavor,FlavorDetail
from dataclasses import dataclass
from string import Template
import libvirt
import os
import xml.etree.ElementTree as ET


class Client:

    def create_snapshot(self, form):

        domain_name = form['vm_name']
        snapshot_name = form.get("snapshot_name", "")
        remark = form.get("remark", "")
        # 获取指定虚拟机的 domain 对象
        now = int(time.time())
        if snapshot_name == "":
            snapshot_name = domain_name + str(now)
        domain = self.conn.lookupByName(domain_name)
        if domain is None:
            print(f'Failed to find the domain {domain_name}')
            return

        # 创建快照 XML 描述
        snapshot_xml = f"""
        <domainsnapshot>
            <name>{snapshot_name}</name>

            <description>{remark}</description>

        </domainsnapshot>

        """

        # 创建快照
        try:
            domain.snapshotCreateXML(snapshot_xml, 0)
            print(f'Snapshot {snapshot_name} created for domain {domain_name}')
        except libvirt.libvirtError as e:
            print(f'Failed to create snapshot: {e}')
        return True

    def delete_snapshot(self, form):
        """
        删除快照
        :param form:
        :return:
        """

        domain_name = form['vm_name']
        snapshot_name = form.get("snapshot_name", "")        
        domain = self.conn.lookupByName(domain_name)
        if domain is None:
            print(f'Failed to find the domain {domain_name}')
            return True  # 视为删除成功
        try:
            # 获取快照对象
            snapshot = domain.snapshotLookupByName(snapshot_name)
            # 删除快照
            snapshot.delete()  # 可选标志，可根据需要删除
            print(f'Snapshot {snapshot_name} deleted for domain {domain_name}')
        except libvirt.libvirtError as e:
            # 判断是否是快照不存在的错误
            if "Snapshot not found" in str(e) or "no snapshot with matching name" in str(e):
                print(f'Snapshot {snapshot_name} does not exist for domain {domain_name}, treat as deleted.')
                return True
            else:
                print(f'Failed to delete snapshot: {e}')
                return False
        return True

    def restore_snapshot(self, form):
        # 查找目标虚拟机
        domain_name = form['vm_name']
        snapshot_name = form["snapshot_name"]
        try:
            dom = self.conn.lookupByName(domain_name)
        except libvirt.libvirtError:
            print(f'Failed to find the domain {domain_name}')
            return False

        # 查找快照
        try:
            snapshot = dom.snapshotLookupByName(snapshot_name, 0)
        except libvirt.libvirtError:
            print(f'Failed to find the snapshot {snapshot_name} for domain {domain_name}')
            return False

        # 恢复快照
        try:
            dom.revertToSnapshot(snapshot)
            print(f'Successfully restored snapshot {snapshot_name} for domain {domain_name}')
        except libvirt.libvirtError as e:
            print(f'Failed to restore snapshot: {e}')
            return False

        return True

    def snapshot_list(self, form):
        """
        快照列表
        :param form:
        :return:
        """
        vm = form.get("vm_name", "")
        domain = self.conn.lookupByName(vm)
        if domain is None:
            print(f'Failed to find the domain {vm}')
            return False

        try:
            # 获取当前激活快照名
            try:
                current_snapshot = domain.snapshotCurrent(0)
                current_snapshot_name = current_snapshot.getName()
            except libvirt.libvirtError:
                current_snapshot_name = None

            # 获取所有快照对象
            snapshots = domain.listAllSnapshots(0)
            snapshot_info = []

            for snap in snapshots:
                xml = snap.getXMLDesc(0)
                root = ET.fromstring(xml)
                name = root.findtext("name")
                creation_time = root.findtext("creationTime")
                if creation_time and creation_time.isdigit():
                    creation_time_fmt = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(creation_time)))
                else:
                    creation_time_fmt = creation_time
                description = root.findtext("description")
                info = {
                    "name": name,
                    "creation_time": creation_time_fmt,
                    "description": description,
                }
                if name == current_snapshot_name:
                    info["is_current"] = True
                snapshot_info.append(info)

            return snapshot_info
        except libvirt.libvirtError as e:
            print(f"无法获取快照列表: {e}")
            return []

    def create_manual_snapshot(self, form):
        """
        创建虚拟机快照
        todo 待完善
        """
        name = form["name"]
        # 获取虚拟机对象
        dom = self.conn.lookupByName(name)
        if dom is None:
            print(f"虚拟机 {name} 不存在。")
            return None
        xml_desc = dom.XMLDesc()
        # 解析XML
        tree = ET.fromstring(xml_desc)

        # 创建根元素
        domainsnapshot = ET.Element('domainsnapshot')
        # 添加描述
        description = ET.SubElement(domainsnapshot, 'description')
        description.text = f'虚拟机 {name} 快照'

        # mem = ET.SubElement(domainsnapshot, "memory", attrib={"snapshot": "no"})

        # 查找所有的磁盘目标
        disks = tree.findall('.//disk')
        for disk in disks:
            target_dev = disk.find('target').get('dev')  # 获取磁盘在虚拟机中的设备名
            source_path = disk.find('source').get('file')  # 获取磁盘文件的路径
            disk_type = disk.find('driver').get('type')
            print(f"Virtual Machine ID: {id}, Name: {dom.name()}")
            print(f"Disk Device: {target_dev}, Path: {source_path}")

            snap_disks = ET.SubElement(domainsnapshot, 'disks')

            # 添加第一个磁盘元素
            disk_xml = ET.SubElement(snap_disks, 'disk', attrib={'snapshot': 'manual', 'name': target_dev})

            current_timestamp = int(time.time())

            # 使用 split 按 "/" 分割，提取最后一部分
            disk_name = source_path.split('/')[-1]
            snap_name = name + "-snapshot-" + str(current_timestamp) + "." + disk_type
            new_path = source_path.rsplit(disk_name, 1)[0] + snap_name

            source = ET.SubElement(disk_xml, "source")
            source_xml = ET.SubElement(disk_xml, 'source', attrib={'file': new_path})
            break

        tree = ET.ElementTree(domainsnapshot)
        xml_string = ET.tostring(domainsnapshot, encoding='unicode', method='xml', xml_declaration=False)
        print(xml_string)

        # 创建快照
        snapshot = dom.snapshotCreateXML(xml_string, 0)
        if snapshot is None:
            print(f"无法创建虚拟机 {name}的快照。")
            return None

        print(f"虚拟机 {name} 快照已创建。")
        return snapshot


    def create_external_snapshot(self, form):
        """
        TODO 开发中，禁止使用
        创建外部快照
        :param form: {
            "vm_name": "虚拟机名称",
            "snapshot_name": "快照名称", 
            "description": "快照描述",
            "snapshot_type": "external",  # 外部快照
            "disks": [磁盘信息列表]
        }
        :return:
        """
        try:
            vm_name = form.get("vm_name")
            snapshot_name = form.get("snapshot_name")
            description = form.get("description", f"外部快照 {snapshot_name}")
            disks = form.get("disks", [])
            
            if not vm_name or not snapshot_name:
                return {"result": "failed", "error": "缺少必要参数: vm_name 或 snapshot_name"}
            
            # 获取虚拟机对象
            try:
                dom = self.conn.lookupByName(vm_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"虚拟机 {vm_name} 不存在: {str(e)}"}
            
            # 获取虚拟机XML描述
            xml_desc = dom.XMLDesc()
            tree = ET.fromstring(xml_desc)
            
            # 创建快照XML根元素
            domainsnapshot = ET.Element('domainsnapshot')
            
            # 添加快照名称
            name_elem = ET.SubElement(domainsnapshot, 'name')
            name_elem.text = snapshot_name
            
            # 添加描述
            description_elem = ET.SubElement(domainsnapshot, 'description')
            description_elem.text = description
            
            # 添加创建时间
            creation_time = ET.SubElement(domainsnapshot, 'creationTime')
            creation_time.text = str(int(time.time()))
            
            # 内存快照设置为no（外部快照不包含内存状态）
            memory_elem = ET.SubElement(domainsnapshot, 'memory', attrib={'snapshot': 'no'})
            
            # 创建磁盘快照配置
            disks_elem = ET.SubElement(domainsnapshot, 'disks')
            
            # 查找虚拟机的所有磁盘
            vm_disks = tree.findall('.//disk[@type="file"][@device="disk"]')
            
            current_timestamp = int(time.time())
            
            # 收集新创建的磁盘信息
            new_disk_files = []
            
            for disk in vm_disks:
                target_elem = disk.find('target')
                source_elem = disk.find('source')
                driver_elem = disk.find('driver')
                
                if target_elem is None or source_elem is None or driver_elem is None:
                    continue
                    
                target_dev = target_elem.get('dev')  # 磁盘设备名 (如 vda, sda)
                source_path = source_elem.get('file')  # 原始磁盘文件路径
                disk_format = driver_elem.get('type', 'qcow2')  # 磁盘格式
                
                if not target_dev or not source_path:
                    continue
                
                print(f"处理磁盘: 设备={target_dev}, 路径={source_path}, 格式={disk_format}")
                
                # 创建磁盘快照元素
                disk_elem = ET.SubElement(disks_elem, 'disk', attrib={
                    'name': target_dev,
                    'snapshot': 'external'
                })
                
                # 生成外部快照文件路径
                # 从原路径提取目录和文件名
                path_parts = source_path.rsplit('/', 1)
                if len(path_parts) == 2:
                    dir_path, original_filename = path_parts
                else:
                    dir_path = '.'
                    original_filename = source_path
                
                # 移除原文件扩展名
                name_without_ext = original_filename.rsplit('.', 1)[0]
                
                # 生成快照文件名: 原文件名-快照名-时间戳.格式
                snapshot_filename = f"{name_without_ext}-{snapshot_name}-{current_timestamp}.{disk_format}"
                snapshot_path = f"{dir_path}/{snapshot_filename}"
                
                # 添加快照文件源
                source_elem = ET.SubElement(disk_elem, 'source', attrib={
                    'file': snapshot_path
                })
                
                # 添加驱动信息
                driver_elem = ET.SubElement(disk_elem, 'driver', attrib={
                    'type': disk_format
                })
                
                print(f"快照文件路径: {snapshot_path}")
                
                # 收集新磁盘文件信息
                import os
                disk_info = {
                    "device": target_dev,
                    "original_path": source_path,
                    "snapshot_path": snapshot_path,
                    "format": disk_format,
                    "snapshot_filename": snapshot_filename,
                    "directory": dir_path,
                    "size": 0,  # 初始大小为0，实际大小需要快照创建后获取
                    "created": False  # 标记是否成功创建
                }
                new_disk_files.append(disk_info)
            
            # 生成快照XML字符串
            xml_string = ET.tostring(domainsnapshot, encoding='unicode', method='xml')
            print(f"快照XML: {xml_string}")
            
            # 创建外部快照
            # 使用VIR_DOMAIN_SNAPSHOT_CREATE_DISK_ONLY标志创建仅磁盘快照
            # 使用VIR_DOMAIN_SNAPSHOT_CREATE_ATOMIC标志确保原子性操作
            flags = libvirt.VIR_DOMAIN_SNAPSHOT_CREATE_DISK_ONLY | libvirt.VIR_DOMAIN_SNAPSHOT_CREATE_ATOMIC
            
            snapshot = dom.snapshotCreateXML(xml_string, flags)
            
            if snapshot is None:
                return {"result": "failed", "error": f"创建虚拟机 {vm_name} 的外部快照失败"}
            
            # 快照创建成功后，更新磁盘文件信息
            import os
            for disk_info in new_disk_files:
                snapshot_path = disk_info["snapshot_path"]
                if os.path.exists(snapshot_path):
                    disk_info["created"] = True
                    try:
                        # 获取文件大小
                        disk_info["size"] = os.path.getsize(snapshot_path)
                        # 获取文件创建时间
                        disk_info["created_time"] = time.strftime('%Y-%m-%d %H:%M:%S', 
                                                                 time.localtime(os.path.getctime(snapshot_path)))
                        # 获取文件修改时间
                        disk_info["modified_time"] = time.strftime('%Y-%m-%d %H:%M:%S', 
                                                                  time.localtime(os.path.getmtime(snapshot_path)))
                    except Exception as e:
                        print(f"获取磁盘文件 {snapshot_path} 信息失败: {str(e)}")
                else:
                    print(f"警告: 快照磁盘文件不存在: {snapshot_path}")
            
            print(f"虚拟机 {vm_name} 外部快照 {snapshot_name} 创建成功")
            
            return {
                "result": "success",
                "snapshot_name": snapshot_name,
                "vm_name": vm_name,
                "message": f"外部快照 {snapshot_name} 创建成功",
                "disk_files": new_disk_files,
                "total_disks": len(new_disk_files),
                "created_disks": len([d for d in new_disk_files if d["created"]]),
                "snapshot_xml": xml_string
            }
            
        except libvirt.libvirtError as e:
            error_msg = f"libvirt错误: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}
            
        except Exception as e:
            error_msg = f"创建外部快照异常: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}

    def delete_external_snapshot(self, form):
        """
        TODO 开发中，禁止使用
        删除外部快照
        :param form: {
            "vm_name": "虚拟机名称",
            "snapshot_name": "快照名称", 
            "snapshot_type": "external"
        }
        :return:
        """
        try:
            vm_name = form.get("vm_name")
            snapshot_name = form.get("snapshot_name")
            
            if not vm_name or not snapshot_name:
                return {"result": "failed", "error": "缺少必要参数: vm_name 或 snapshot_name"}
            
            # 获取虚拟机对象
            try:
                dom = self.conn.lookupByName(vm_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"虚拟机 {vm_name} 不存在: {str(e)}"}
            
            # 获取快照对象
            try:
                snapshot = dom.snapshotLookupByName(snapshot_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"快照 {snapshot_name} 不存在: {str(e)}"}
            
            # 在删除前收集快照磁盘文件信息
            deleted_disk_files = []
            try:
                snapshot_xml = snapshot.getXMLDesc()
                snapshot_tree = ET.fromstring(snapshot_xml)
                
                # 查找快照中的磁盘信息
                disks_elem = snapshot_tree.find("disks")
                if disks_elem is not None:
                    for disk in disks_elem.findall("disk"):
                        device = disk.get("name")
                        snapshot_type = disk.get("snapshot")
                        
                        source_elem = disk.find("source")
                        if source_elem is not None:
                            snapshot_path = source_elem.get("file")
                            
                            if snapshot_path and device:
                                import os
                                disk_info = {
                                    "device": device,
                                    "snapshot_path": snapshot_path,
                                    "snapshot_type": snapshot_type,
                                    "existed_before_delete": os.path.exists(snapshot_path),
                                    "size": 0,
                                    "deleted": False
                                }
                                
                                # 获取删除前的文件信息
                                if disk_info["existed_before_delete"]:
                                    try:
                                        disk_info["size"] = os.path.getsize(snapshot_path)
                                        disk_info["created_time"] = time.strftime('%Y-%m-%d %H:%M:%S', 
                                                                             time.localtime(os.path.getctime(snapshot_path)))
                                        disk_info["modified_time"] = time.strftime('%Y-%m-%d %H:%M:%S', 
                                                                              time.localtime(os.path.getmtime(snapshot_path)))
                                    except Exception as e:
                                        print(f"获取磁盘文件 {snapshot_path} 信息失败: {str(e)}")
                                
                                deleted_disk_files.append(disk_info)
                                
            except Exception as e:
                print(f"收集快照磁盘信息失败: {str(e)}")
            
            # 删除快照（包括快照文件）
            # 使用VIR_DOMAIN_SNAPSHOT_DELETE_CHILDREN删除子快照
            # 使用VIR_DOMAIN_SNAPSHOT_DELETE_METADATA_ONLY仅删除元数据（保留文件）
            flags = libvirt.VIR_DOMAIN_SNAPSHOT_DELETE_CHILDREN
            
            if snapshot.delete(flags) != 0:
                return {"result": "failed", "error": f"删除快照 {snapshot_name} 失败"}
            
            # 检查磁盘文件是否被实际删除
            import os
            for disk_info in deleted_disk_files:
                snapshot_path = disk_info["snapshot_path"]
                if disk_info["existed_before_delete"]:
                    # 检查文件是否还存在
                    disk_info["deleted"] = not os.path.exists(snapshot_path)
                    if not disk_info["deleted"]:
                        print(f"注意: 快照磁盘文件仍然存在: {snapshot_path}")
                        # 可以选择手动删除文件
                        try:
                            os.remove(snapshot_path)
                            disk_info["deleted"] = True
                            disk_info["manually_deleted"] = True
                            print(f"手动删除快照磁盘文件: {snapshot_path}")
                        except Exception as e:
                            print(f"手动删除快照磁盘文件失败: {str(e)}")
                            disk_info["manually_deleted"] = False
            
            print(f"虚拟机 {vm_name} 外部快照 {snapshot_name} 删除成功")
            
            return {
                "result": "success",
                "snapshot_name": snapshot_name,
                "vm_name": vm_name,
                "message": f"外部快照 {snapshot_name} 删除成功",
                "deleted_disk_files": deleted_disk_files,
                "total_disks": len(deleted_disk_files),
                "successfully_deleted": len([d for d in deleted_disk_files if d["deleted"]])
            }
            
        except libvirt.libvirtError as e:
            error_msg = f"libvirt错误: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}
            
        except Exception as e:
            error_msg = f"删除外部快照异常: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}

    def restore_from_external_snapshot(self, form):
        """
        TODO 开发中，禁止使用
        从外部快照恢复虚拟机
        :param form: {
            "vm_name": "虚拟机名称",
            "snapshot_name": "快照名称",
            "restore_type": "恢复类型",
            "snapshot_type": "external",
            "disks": [磁盘信息列表]
        }
        :return:
        """
        try:
            vm_name = form.get("vm_name")
            snapshot_name = form.get("snapshot_name")
            restore_type = form.get("restore_type", "full")
            
            if not vm_name or not snapshot_name:
                return {"result": "failed", "error": "缺少必要参数: vm_name 或 snapshot_name"}
            
            # 获取虚拟机对象
            try:
                dom = self.conn.lookupByName(vm_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"虚拟机 {vm_name} 不存在: {str(e)}"}
            
            # 获取快照对象
            try:
                snapshot = dom.snapshotLookupByName(snapshot_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"快照 {snapshot_name} 不存在: {str(e)}"}
            
            # 检查虚拟机状态，如果运行中需要先关闭
            state = dom.state()[0]
            was_running = (state == libvirt.VIR_DOMAIN_RUNNING)
            
            if was_running:
                print(f"虚拟机 {vm_name} 正在运行，先关闭虚拟机")
                if dom.shutdown() != 0:
                    # 如果优雅关机失败，强制关闭
                    dom.destroy()
                
                # 等待虚拟机关闭
                import time
                for _ in range(30):  # 最多等待30秒
                    if dom.state()[0] == libvirt.VIR_DOMAIN_SHUTOFF:
                        break
                    time.sleep(1)
            
            # 恢复到快照状态
            # 使用VIR_DOMAIN_SNAPSHOT_REVERT_RUNNING标志在恢复后启动虚拟机
            flags = 0
            if was_running:
                flags = libvirt.VIR_DOMAIN_SNAPSHOT_REVERT_RUNNING
            
            if dom.revertToSnapshot(snapshot, flags) != 0:
                return {"result": "failed", "error": f"恢复到快照 {snapshot_name} 失败"}
            
            print(f"虚拟机 {vm_name} 已从外部快照 {snapshot_name} 恢复")
            
            return {
                "result": "success",
                "snapshot_name": snapshot_name,
                "vm_name": vm_name,
                "restore_type": restore_type,
                "message": f"从外部快照 {snapshot_name} 恢复成功"
            }
            
        except libvirt.libvirtError as e:
            error_msg = f"libvirt错误: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}
            
        except Exception as e:
            error_msg = f"从外部快照恢复异常: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}

    def export_vm_backup(self, form):
        """
        TODO 开发中，禁止使用
        导出虚拟机备份
        :param form: {
            "vm_name": "虚拟机名称",
            "snapshot_name": "快照名称",
            "export_path": "导出路径",
            "export_format": "导出格式",
            "disks": [磁盘信息列表]
        }
        :return:
        """
        try:
            vm_name = form.get("vm_name")
            snapshot_name = form.get("snapshot_name")
            export_path = form.get("export_path")
            export_format = form.get("export_format", "qcow2")
            
            if not vm_name or not snapshot_name or not export_path:
                return {"result": "failed", "error": "缺少必要参数: vm_name, snapshot_name 或 export_path"}
            
            # 获取虚拟机对象
            try:
                dom = self.conn.lookupByName(vm_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"虚拟机 {vm_name} 不存在: {str(e)}"}
            
            # 获取快照对象
            try:
                snapshot = dom.snapshotLookupByName(snapshot_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"快照 {snapshot_name} 不存在: {str(e)}"}
            
            # 获取快照XML信息
            snapshot_xml = snapshot.getXMLDesc()
            snapshot_tree = ET.fromstring(snapshot_xml)
            
            # 获取虚拟机XML信息
            vm_xml = dom.XMLDesc()
            vm_tree = ET.fromstring(vm_xml)
            
            # 创建导出目录
            import os
            os.makedirs(export_path, exist_ok=True)
            
            # 导出虚拟机配置
            vm_config_path = os.path.join(export_path, f"{vm_name}-{snapshot_name}.xml")
            with open(vm_config_path, 'w', encoding='utf-8') as f:
                f.write(vm_xml)
            
            # 导出快照配置
            snapshot_config_path = os.path.join(export_path, f"{vm_name}-{snapshot_name}-snapshot.xml")
            with open(snapshot_config_path, 'w', encoding='utf-8') as f:
                f.write(snapshot_xml)
            
            exported_files = [vm_config_path, snapshot_config_path]
            
            # 查找并导出磁盘文件
            vm_disks = vm_tree.findall('.//disk[@type="file"][@device="disk"]')
            
            for disk in vm_disks:
                source_elem = disk.find('source')
                target_elem = disk.find('target')
                
                if source_elem is None or target_elem is None:
                    continue
                    
                source_path = source_elem.get('file')
                target_dev = target_elem.get('dev')
                
                if not source_path or not target_dev:
                    continue
                
                # 检查磁盘文件是否存在
                if not os.path.exists(source_path):
                    print(f"警告: 磁盘文件不存在: {source_path}")
                    continue
                
                # 生成导出文件名
                disk_filename = f"{vm_name}-{snapshot_name}-{target_dev}.{export_format}"
                disk_export_path = os.path.join(export_path, disk_filename)
                
                # 使用qemu-img转换格式（如果需要）
                import subprocess
                try:
                    cmd = ['qemu-img', 'convert', '-f', 'qcow2', '-O', export_format, source_path, disk_export_path]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
                    
                    if result.returncode == 0:
                        exported_files.append(disk_export_path)
                        print(f"磁盘 {target_dev} 导出成功: {disk_export_path}")
                    else:
                        print(f"磁盘 {target_dev} 导出失败: {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    print(f"磁盘 {target_dev} 导出超时")
                except Exception as e:
                    print(f"磁盘 {target_dev} 导出异常: {str(e)}")
            
            print(f"虚拟机 {vm_name} 快照 {snapshot_name} 导出完成")
            
            return {
                "result": "success",
                "vm_name": vm_name,
                "snapshot_name": snapshot_name,
                "export_path": export_path,
                "export_format": export_format,
                "exported_files": exported_files,
                "message": f"虚拟机备份导出成功，共导出 {len(exported_files)} 个文件"
            }
            
        except Exception as e:
            error_msg = f"导出虚拟机备份异常: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}

    def get_snapshot_list(self, form):
        """
        TODO 开发中，禁止使用
        获取虚拟机快照列表（包括外部快照）
        :param form: {
            "vm_name": "虚拟机名称",
            "snapshot_type": "external"
        }
        :return:
        """
        try:
            vm_name = form.get("vm_name")
            snapshot_type = form.get("snapshot_type", "all")
            
            if not vm_name:
                return {"result": "failed", "error": "缺少必要参数: vm_name"}
            
            # 获取虚拟机对象
            try:
                dom = self.conn.lookupByName(vm_name)
            except libvirt.libvirtError as e:
                return {"result": "failed", "error": f"虚拟机 {vm_name} 不存在: {str(e)}"}
            
            # 获取快照列表
            snapshot_names = dom.snapshotListNames()
            snapshots = []
            
            for name in snapshot_names:
                try:
                    snapshot = dom.snapshotLookupByName(name)
                    xml_desc = snapshot.getXMLDesc()
                    
                    # 解析快照XML
                    root = ET.fromstring(xml_desc)
                    
                    # 获取快照信息
                    creation_time = root.findtext("creationTime")
                    if creation_time and creation_time.isdigit():
                        creation_time_fmt = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(creation_time)))
                    else:
                        creation_time_fmt = creation_time or "未知"
                    
                    description = root.findtext("description") or ""
                    
                    # 检查是否为外部快照并收集磁盘信息
                    is_external = False
                    disk_files = []
                    disks_elem = root.find("disks")
                    if disks_elem is not None:
                        for disk in disks_elem.findall("disk"):
                            device = disk.get("name")
                            snapshot_attr = disk.get("snapshot")
                            
                            if snapshot_attr == "external":
                                is_external = True
                            
                            # 收集磁盘文件信息
                            source_elem = disk.find("source")
                            driver_elem = disk.find("driver")
                            
                            if source_elem is not None and device:
                                snapshot_path = source_elem.get("file")
                                disk_format = driver_elem.get("type") if driver_elem is not None else "unknown"
                                
                                if snapshot_path:
                                    import os
                                    disk_info = {
                                        "device": device,
                                        "snapshot_path": snapshot_path,
                                        "format": disk_format,
                                        "snapshot_type": snapshot_attr or "internal",
                                        "exists": os.path.exists(snapshot_path),
                                        "size": 0
                                    }
                                    
                                    # 获取文件信息
                                    if disk_info["exists"]:
                                        try:
                                            disk_info["size"] = os.path.getsize(snapshot_path)
                                            disk_info["size_mb"] = round(disk_info["size"] / (1024 * 1024), 2)
                                            disk_info["created_time"] = time.strftime('%Y-%m-%d %H:%M:%S', 
                                                                                     time.localtime(os.path.getctime(snapshot_path)))
                                            disk_info["modified_time"] = time.strftime('%Y-%m-%d %H:%M:%S', 
                                                                                      time.localtime(os.path.getmtime(snapshot_path)))
                                        except Exception as e:
                                            print(f"获取快照磁盘文件 {snapshot_path} 信息失败: {str(e)}")
                                    
                                    disk_files.append(disk_info)
                    
                    # 根据类型过滤
                    if snapshot_type == "external" and not is_external:
                        continue
                    elif snapshot_type == "internal" and is_external:
                        continue
                    
                    snapshot_info = {
                        "name": name,
                        "creation_time": creation_time_fmt,
                        "description": description,
                        "type": "external" if is_external else "internal",
                        "disk_files": disk_files,
                        "total_disk_files": len(disk_files),
                        "total_size": sum(d.get("size", 0) for d in disk_files),
                        "total_size_mb": round(sum(d.get("size", 0) for d in disk_files) / (1024 * 1024), 2)
                    }
                    
                    # 检查是否为当前快照
                    try:
                        current_snapshot = dom.snapshotCurrent()
                        if current_snapshot and current_snapshot.getName() == name:
                            snapshot_info["is_current"] = True
                    except libvirt.libvirtError:
                        pass  # 没有当前快照
                    
                    snapshots.append(snapshot_info)
                    
                except libvirt.libvirtError as e:
                    print(f"获取快照 {name} 信息失败: {str(e)}")
                    continue
            
            # 计算总体统计信息
            total_size = sum(s.get("total_size", 0) for s in snapshots)
            total_disk_files = sum(s.get("total_disk_files", 0) for s in snapshots)
            
            return {
                "result": "success",
                "snapshots": snapshots,
                "total": len(snapshots),
                "vm_name": vm_name,
                "total_disk_files": total_disk_files,
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "total_size_gb": round(total_size / (1024 * 1024 * 1024), 2)
            }
            
        except Exception as e:
            error_msg = f"获取快照列表异常: {str(e)}"
            print(error_msg)
            return {"result": "failed", "error": error_msg}
