"""
用于创建xml文件的工具类
"""

import xml.etree.ElementTree as ET
from xml.dom.minidom import parseString


class XMLElement:
    def __init__(self, tag=None, attributes=None, text=None):
        self.tag = tag
        self.element = ET.Element(self.tag, attributes if attributes else {})
        self.element.text = text
        self.children = []

    @staticmethod
    def remove_xml_declaration(xml_string):
        """Remove the XML declaration from the XML string."""
        # Strip leading and trailing whitespace
        xml_string = xml_string.strip()
        # Check if the first line starts with XML declaration
        if xml_string.startswith('<?xml'):
            # Find the end of the XML declaration
            end_index = xml_string.find('?>') + 2
            # Remove the XML declaration
            return xml_string[end_index:].lstrip()
        return xml_string

    def set_tag(self, tag, attributes=None, text=None):
        """
        创建一个新的 XML 标签并为其设置属性。

        :param tag: 新标签的名称
        :param attributes: 新标签的属性字典
        :param text: 新标签的文本内容
        :return: 新创建的 XMLElement 实例
        """
        new_element = XMLElement(tag, attributes, text)
        self.add_child(new_element)
        return new_element

    def set_tag_value(self, tag, text=None):
        """
        创建一个新的 XML 标签，设置值。

        :param tag: 新标签的名称
        :param text: 新标签的文本内容
        :return: 新创建的 XMLElement 实例
        """
        new_element = XMLElement(tag, None, text)
        self.add_child(new_element)
        return new_element

    def add_child(self, child):
        """
        向当前元素添加子元素
        :param child: 要添加的子元素对象
        """
        self.children.append(child)
        self.element.append(child.element)

    def set_text(self, text):
        """
        设置当前元素的文本内容

        :param text: 要设置的文本内容
        """
        self.element.text = text

    def set_attribute(self, key, value):
        """
        设置当前元素的属性

        :param key: 属性名
        :param value: 属性值
        """
        self.element.attrib[key] = value

    def to_xml(self):
        return ET.tostring(self.element, encoding='unicode', method='xml').strip()

    def format_xml(self):
        # 使用minidom格式化XML
        dom = parseString(self.to_xml().encode('utf-8'))
        formatted_xml = dom.toprettyxml(indent="  ")
        remove_declaration_xml = self.remove_xml_declaration(formatted_xml)
        return remove_declaration_xml
