from .default import XMLElement


class Network(XMLElement):
    """
    网络
    """

    def set_name(self, name):
        self.set_tag("name", None, name)

    def set_forward(self, attributes=None):
        if attributes is None:
            attributes = {
                "mode": "bridge"
            }
        self.set_tag("forward", attributes)

    def set_bridge(self, bridge):
        br = self.set_tag("bridge")
        br.set_attribute("name", bridge)

    def set_virtual_port(self, attributes=None):
        if attributes is None:
            attributes = {
                'type': 'openvswitch'
            }
        self.set_tag("virtualport", attributes)
