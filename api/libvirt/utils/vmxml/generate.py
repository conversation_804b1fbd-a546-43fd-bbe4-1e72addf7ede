import datetime
import random
import uuid
import xml.etree.ElementTree as ET

from api.libvirt.utils.utils import capacity_to_bytes


def create_metadata(doc, data, vm_name, vm_uuid):
    metadata = doc.createElement('metadata')
    nova_instance = doc.createElement('TheCloud:instance')
    nova_instance.setAttribute('xmlns:TheCloud', 'http://www.thedatasys.com/')

    # Package version
    package = doc.createElement('TheCloud:package')
    package.setAttribute('version', '3.3')
    nova_instance.appendChild(package)

    # Name
    nova_name = doc.createElement('TheCloud:name')
    nova_name.appendChild(doc.createTextNode(vm_name))
    nova_instance.appendChild(nova_name)

    # Creation time
    creation_time = doc.createElement('TheCloud:creationTime')
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    creation_time.appendChild(doc.createTextNode(current_time))
    nova_instance.appendChild(creation_time)

    # Add flavor
    flavor = create_flavor_element(doc, data)
    nova_instance.appendChild(flavor)

    metadata.appendChild(nova_instance)
    return metadata

def create_flavor_element(doc, data):
    flavor = doc.createElement('TheCloud:flavor')
    flavor.setAttribute('name', f"{data.get('vcpu_unit')}-{data.get('memory_unit')}{data.get('memory_unit_type')}-{data.get('disk_size', 20)}")

    memory = doc.createElement('TheCloud:memory')
    memory.appendChild(doc.createTextNode(str(data.get('memory_unit'))+data.get('memory_unit_type')))
    flavor.appendChild(memory)

    disk = doc.createElement('TheCloud:disk')
    disk.appendChild(doc.createTextNode(str(data.get('disk_size', 20))))
    flavor.appendChild(disk)

    return flavor

def create_sysinfo(doc, vm_uuid):
    sysinfo = doc.createElement('sysinfo')
    sysinfo.setAttribute('type', 'smbios')

    system = doc.createElement('system')
    entries = [
        ('manufacturer', 'TheCloud Foundation'),
        ('product', 'TheCloud web'),
        ('version', '3.3'),
        ('serial', vm_uuid),
        ('uuid', vm_uuid),
        ('family', 'Virtual Machine')
    ]

    for name, value in entries:
        entry = doc.createElement('entry')
        entry.setAttribute('name', name)
        entry.appendChild(doc.createTextNode(value))
        system.appendChild(entry)

    sysinfo.appendChild(system)
    return sysinfo

def create_features(doc, arch='x86_64'):
    features = doc.createElement('features')
    
    # 基础特性配置（所有架构通用）
    feature_list = ['acpi', 'vmcoreinfo']  # 保留 acpi 和 vmcoreinfo
    
    # 根据架构调整配置
    if arch == 'aarch64':
        # ARM 架构：添加 GIC v3，移除 apic
        gic = doc.createElement('gic')
        gic.setAttribute('version', '3')
        features.appendChild(gic)
    else:
        # x86 架构：添加 apic
        feature_list.append('apic')
    
    # 添加通用特性
    for feature_name in feature_list:
        feature = doc.createElement(feature_name)
        features.appendChild(feature)
    
    return features

def create_clock(doc):
    clock = doc.createElement('clock')
    clock.setAttribute('offset', 'utc')

    timers = [
        ('rtc', 'catchup'),
        ('pit', 'delay'),
        ('hpet', None, 'no')
    ]

    for timer_data in timers:
        timer = doc.createElement('timer')
        timer.setAttribute('name', timer_data[0])
        if len(timer_data) > 1 and timer_data[1]:
            timer.setAttribute('tickpolicy', timer_data[1])
        if len(timer_data) > 2:
            timer.setAttribute('present', timer_data[2])
        clock.appendChild(timer)

    return clock

def create_cpu(doc, vcpus, arch='x86_64'):
    cpu = doc.createElement('cpu')
    vcpus = safe_to_int(vcpus, 1)  # 确保至少2个vCPU
    
    # ARM架构特殊处理
    if arch == 'aarch64':
        cpu.setAttribute('mode', 'host-passthrough')  # ARM必须用此模式
        cpu.setAttribute('check', 'none')
        
        # ARM推荐拓扑配置（通常1die, 1thread）
        sockets = min(vcpus, 8)  # ARM服务器通常支持更多sockets
        cores = max(1, vcpus // sockets)
        
        topology = doc.createElement('topology')
        topology.setAttribute('sockets', str(sockets))
        topology.setAttribute('dies', '1')
        topology.setAttribute('cores', str(cores))
        topology.setAttribute('threads', '1')  # ARM通常不支持SMT
        cpu.appendChild(topology)
                
    else:  # x86_64保留原逻辑
        cpu.setAttribute('mode', 'host-model')
        cpu.setAttribute('check', 'partial')
        
        sockets = min(vcpus, 4)
        cores = max(1, vcpus // sockets)
        
        topology = doc.createElement('topology')
        topology.setAttribute('sockets', str(sockets))
        topology.setAttribute('dies', '1')
        topology.setAttribute('cores', str(cores))
        topology.setAttribute('threads', '1')
        cpu.appendChild(topology)
    
    return cpu

def create_disk_element(doc, disk_dict):
    disk = doc.createElement('disk')
    disk.setAttribute('type', disk_dict.get('disk_type', 'network'))
    disk.setAttribute('device', 'disk')

    driver = doc.createElement('driver')
    driver.setAttribute('name', 'qemu')
    driver.setAttribute('type', disk_dict.get('format', 'raw'))
    driver.setAttribute('cache', 'writeback')
    driver.setAttribute('discard', 'unmap')
    disk.appendChild(driver)

    if disk_dict.get('disk_type') == 'network':
        source = doc.createElement('source')
        source.setAttribute('protocol', 'rbd')
        source.setAttribute('name', f"volumes/{disk_dict.get('volume_id')}")

        for host in disk_dict.get('hosts', []):
            host_elem = doc.createElement('host')
            host_elem.setAttribute('name', host['name'])
            host_elem.setAttribute('port', str(host['port']))
            source.appendChild(host_elem)

        disk.appendChild(source)

    target = doc.createElement('target')
    target.setAttribute('dev', disk_dict.get('target_dev', 'vda'))
    target.setAttribute('bus', 'virtio')
    disk.appendChild(target)

    return disk

def create_local_disk_element1(doc, disk_dict, dev):
    disk_format = disk_dict.get('storage_type_code', "qcow2")
    path = disk_dict.get("path", "")
    disk_name = disk_dict.get("disk_name", "")

    if path.endswith(f'.{disk_format}'):
        # Path already has the correct extension
        disk_source_file = path
    else:
        # Path needs extension - combine path, name and format
        disk_source_file = f"{path}/{disk_name}.{disk_format}"

    disk_type = disk_dict.get('disk_type')


    disk = doc.createElement('disk')
    disk.setAttribute('type', disk_type)
    disk.setAttribute('device', 'disk')


    driver = doc.createElement('driver')
    driver.setAttribute('name', 'qemu')
    driver.setAttribute('type', disk_format)
    disk.appendChild(driver)

    source = doc.createElement('source')
    source.setAttribute('file', disk_source_file)
    disk.appendChild(source)

    target = doc.createElement('target')
    target.setAttribute('dev', dev)
    target.setAttribute('bus', 'virtio')
    disk.appendChild(target)

    return disk

def create_cdrom_element(doc, cdrom_dict, arch='x86_64'):
    """创建CDROM配置"""
    disk = doc.createElement('disk')
    disk.setAttribute('type', 'file')
    disk.setAttribute('device', 'cdrom')

    driver = doc.createElement('driver')
    driver.setAttribute('name', 'qemu')
    driver.setAttribute('type', 'raw')
    disk.appendChild(driver)

    source = doc.createElement('source')
    source.setAttribute('file', cdrom_dict.get('path'))
    disk.appendChild(source)

    target = doc.createElement('target')
    target.setAttribute('dev', 'hdc')
    
    if arch == 'x86_64':
        target.setAttribute('bus', 'sata')
    elif arch == 'aarch64':
        target.setAttribute('bus', 'virtio')
    else:
        target.setAttribute('bus', 'ide')
    # target.setAttribute('bus', 'sata')
    # virtio全架构通用，arm可能不兼容sata
    disk.appendChild(target)

    readonly = doc.createElement('readonly')
    disk.appendChild(readonly)

    return disk

def create_graphics_element(doc, protocol):
    # 添加显示设备
    graphics = doc.createElement('graphics')
    graphics.setAttribute('type', protocol)
    if protocol == 'spice':
        graphics.setAttribute('port', '-1')
        graphics.setAttribute('tlsPort', '-1')
        graphics.setAttribute('autoport', 'yes')
        graphics.setAttribute('listen', '0.0.0.0')
    elif protocol == 'vnc':
        graphics.setAttribute('port', '-1')
        graphics.setAttribute('autoport', 'yes')
        graphics.setAttribute('listen', '0.0.0.0')
    return graphics


def create_input_element(doc, devices, data):
    """创建CDROM配置"""
    input_mouse = doc.createElement('input')
    input_mouse.setAttribute('type', 'mouse')
    input_mouse.setAttribute('bus', data.get('input_devices', {}).get('mouse', 'usb'))
    devices.appendChild(input_mouse)

    input_keyboard = doc.createElement('input')
    input_keyboard.setAttribute('type', 'keyboard')
    input_keyboard.setAttribute('bus', data.get('input_devices', {}).get('keyboard', 'usb'))
    devices.appendChild(input_keyboard)

def create_gpu_element(doc):
    hostdev = doc.createElement('hostdev')
    hostdev.setAttribute('mode', 'subsystem')
    hostdev.setAttribute('type', 'pci')
    hostdev.setAttribute('managed', 'yes')
    source = doc.createElement('source')
    address = doc.createElement('address')
    address.setAttribute('domain', '0x0000')
    address.setAttribute('bus', '0x03')
    address.setAttribute('slot', '0x00')
    address.setAttribute('function', '0x0')
    source.appendChild(address)
    hostdev.appendChild(source)
    return hostdev

def create_interface_element(doc, network):
    interface = doc.createElement('interface')
    interface.setAttribute('type', 'network')


    def generate_mac_address():
        """生成随机MAC地址，使用libvirt默认前缀52:54:00   52:54:00:34:34:df"""

        mac = [0x52, 0x54, 0x00,
               random.randint(0x00, 0xff),
               random.randint(0x00, 0xff),
               random.randint(0x00, 0xff)]
        return ':'.join(map(lambda x: "%02x" % x, mac))

    # 添加MAC地址
    mac = doc.createElement('mac')
    mac.setAttribute('address', generate_mac_address())
    interface.appendChild(mac)

    # 添加source元素，明确指定网络为default
    source = doc.createElement('source')
    source.setAttribute('network', network.get('network', "default"))  # 明确指定使用default网络
    interface.appendChild(source)

    # 添加模型类型
    model = doc.createElement('model')
    model.setAttribute('type', 'virtio')
    interface.appendChild(model)

    return interface

def create_interface_element_ovs(doc, network):
    interface = doc.createElement('interface')
    interface.setAttribute('type', 'bridge')  # 使用 bridge 类型

    # def generate_mac_address():
    #     """生成随机MAC地址，使用libvirt默认前缀52:54:00   52:54:00:34:34:df"""
    #     mac = [0x52, 0x54, 0x00,
    #            random.randint(0x00, 0xff),
    #            random.randint(0x00, 0xff),
    #            random.randint(0x00, 0xff)]
    #     return ':'.join(map(lambda x: "%02x" % x, mac))

    # 添加MAC地址
    mac = doc.createElement('mac')
    mac.setAttribute('address', network["mac"])
    interface.appendChild(mac)

    # 添加source元素，指定 OVS 网桥名称
    source = doc.createElement('source')
    source.setAttribute('bridge', network.get('network', "ovsbr0"))  # 指定 OVS 网桥名称，默认为 "ovsbr0"
    interface.appendChild(source)

    # 添加虚拟端口类型
    virtualport = doc.createElement('virtualport')
    virtualport.setAttribute('type', 'openvswitch')
    interface.appendChild(virtualport)

    if 'port_name' in network and network['port_name']:
        target = doc.createElement('target')
        target.setAttribute('dev', network['port_name'])
        interface.appendChild(target)

    # 添加模型类型
    model = doc.createElement('model')
    model.setAttribute('type', 'virtio')
    interface.appendChild(model)

    return interface

def add_additional_devices(doc, devices, arch="x86_64", display_protocol=''):
    # 添加模拟器路径
    # emulator = doc.createElement('emulator')
    # emulator.appendChild(doc.createTextNode('/usr/bin/qemu-system-x86_64'))
    # devices.appendChild(emulator)

    # 添加USB控制器
     # 根据架构选择USB控制器配置
    if arch == "aarch64":
        # ARM架构使用qemu-xhci控制器（USB 3.0）
        ctrl = doc.createElement('controller')
        ctrl.setAttribute('type', 'usb')
        ctrl.setAttribute('model', 'qemu-xhci')
        
        # 添加PCI地址（ARM需要显式指定）
        addr = doc.createElement('address')
        addr.setAttribute('type', 'pci')
        addr.setAttribute('domain', '0x0000')
        addr.setAttribute('bus', '0x02')
        addr.setAttribute('slot', '0x00')
        addr.setAttribute('function', '0x0')
        ctrl.appendChild(addr)
        
        devices.appendChild(ctrl)
    elif arch == "x86_64":
        usb_controllers = [
            ('usb', 'ich9-ehci1'),
            ('usb', 'ich9-uhci1', '0'),
            ('usb', 'ich9-uhci2', '2'),
            ('usb', 'ich9-uhci3', '4')
        ]

        for controller in usb_controllers:
            ctrl = doc.createElement('controller')
            ctrl.setAttribute('type', controller[0])
            ctrl.setAttribute('model', controller[1])
            
            if len(controller) > 2:  # 处理master startport
                master = doc.createElement('master')
                master.setAttribute('startport', controller[2])
                ctrl.appendChild(master)
                
            devices.appendChild(ctrl)

    # 添加串口设备
    console = doc.createElement('console')
    console.setAttribute('type', 'pty')
    devices.appendChild(console)

    # 根据显示协议添加相关设备
    if display_protocol == 'spice':
        # 添加SPICE通道
        channel = doc.createElement('channel')
        channel.setAttribute('type', 'spicevmc')
        target = doc.createElement('target')
        target.setAttribute('type', 'virtio')
        target.setAttribute('name', 'com.redhat.spice.0')
        channel.appendChild(target)
        devices.appendChild(channel)

        # 添加USB重定向设备
        for _ in range(2):
            redirdev = doc.createElement('redirdev')
            redirdev.setAttribute('bus', 'usb')
            redirdev.setAttribute('type', 'spicevmc')
            devices.appendChild(redirdev)

    # 添加输入设备
    input_elem = doc.createElement('input')
    if arch == 'aarch64':
        input_elem.setAttribute('type', 'tablet')
        input_elem.setAttribute('bus', 'virtio')
    elif arch == 'x86_64':
        input_elem.setAttribute('bus', 'usb')
        input_elem.setAttribute('type', 'tablet')
    devices.appendChild(input_elem)
    

    # 添加声卡
    if arch == "x86_64":
        sound = doc.createElement('sound')
        sound.setAttribute('model', 'ich6')
        devices.appendChild(sound)

    # 添加视频设备
    video = doc.createElement('video')
    model = doc.createElement('model')
    if arch == 'aarch64':
        model.setAttribute('type', 'virtio')
        model.setAttribute('heads', '1')
        model.setAttribute('primary', 'yes')
        
    # 如果是SPICE且需要3D加速
    # if display_protocol == 'spice':
    #     acceleration = doc.createElement('acceleration')
    #     acceleration.setAttribute('accel3d', 'yes')
    #     model.appendChild(acceleration)
    else:
        model.setAttribute('type', 'qxl' if display_protocol == 'spice' else 'cirrus')
    video.appendChild(model)
    devices.appendChild(video)



def safe_to_int(value, default=1):
    """安全转换为整数，失败返回默认值"""
    if isinstance(value, int):
        return max(1, value)  # 确保至少1个vCPU
    try:
        return max(1, int(str(value).strip()))
    except (ValueError, TypeError, AttributeError):
        return max(1, default)