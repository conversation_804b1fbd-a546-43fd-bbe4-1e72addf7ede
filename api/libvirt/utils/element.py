from xml.dom.minidom import Document

def create_local_disk_element(doc, disk_type, disk_size, disk_format, disk_source_file):
    disk = doc.createElement('disk')
    disk.setAttribute('type', disk_type)
    disk.setAttribute('device', 'disk')


    driver = doc.createElement('driver')
    driver.setAttribute('name', 'qemu')
    driver.setAttribute('type', disk_format)
    disk.appendChild(driver)

    source = doc.createElement('source')
    source.setAttribute('file', disk_source_file)
    disk.appendChild(source)

    target = doc.createElement('target')
    target.setAttribute('dev', 'vda')
    target.setAttribute('bus', 'virtio')   
    disk.appendChild(target)
    
    return disk

def create_ovs_interface_element(doc, bridge_name, mac_address):
    # 创建 <interface> 元素，并设置 type='bridge'
    interface = doc.createElement('interface')
    interface.setAttribute('type', 'bridge')

    # 创建 <mac> 元素，并设置地址属性
    mac = doc.createElement('mac')
    mac.setAttribute('address', mac_address)
    interface.appendChild(mac)

    # 创建 <source> 元素，并设置 bridge 属性
    source = doc.createElement('source')
    source.setAttribute('bridge', bridge_name)
    interface.appendChild(source)
    
    vnet = doc.createElement('virtualport')
    vnet.setAttribute('type', 'openvswitch')
    interface.appendChild(vnet)

    target = doc.createElement('target')
    target.setAttribute('dev', 'vnet2')
    interface.appendChild(target)


    # 创建 <model> 元素，并设置 type 属性
    model = doc.createElement('model')
    model.setAttribute('type', 'virtio')
    interface.appendChild(model)
    
    

    # 不设置 <address> 元素，让 Libvirt 自动分配
    return interface



def create_graphics_vnc_element(doc, port="5900", listen_address="0.0.0.0"):
    # 创建 <graphics> 元素，并设置 type='vnc', port='5900', autoport='yes', listen='0.0.0.0'
    graphics = doc.createElement('graphics')
    graphics.setAttribute('type', 'vnc')
    graphics.setAttribute('port', port)
    graphics.setAttribute('autoport', 'yes')
    graphics.setAttribute('listen', listen_address)

    # 创建 <listen> 子元素，并设置 type='address', address='0.0.0.0'
    listen = doc.createElement('listen')
    listen.setAttribute('type', 'address')
    listen.setAttribute('address', listen_address)

    # 将 <listen> 子元素添加到 <graphics> 元素中
    graphics.appendChild(listen)

    return graphics