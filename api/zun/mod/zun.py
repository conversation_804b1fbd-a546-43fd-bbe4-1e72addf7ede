import requests
import json
import settings
from api.model.container import ContainerDetail,ContainerCreateDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template

class ZunClient:
    def openstack_test(self, id):
        print(id)
        
    def openstack_get_all_container_list(self):
        method = "/containers/"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["containers"]:
            ins = from_dict(data_class=ContainerDetail, data=ins_dict)
            if ins_dict["task_state"]:
                ins.__dict__["task_state"] = ins_dict["task_state"]
            else:
                ins.__dict__["task_state"] = "None"
            res.append(ins.__dict__)
        return res
    
    def openstack_get_all_image_list(self):
        method = "/images/"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["containers"]:
            ins = from_dict(data_class=ContainerDetail, data=ins_dict)
            if ins_dict["task_state"]:
                ins.__dict__["task_state"] = ins_dict["task_state"]
            else:
                ins.__dict__["task_state"] = "None"
            res.append(ins.__dict__)
        return res
    
    
    '''"command":"['/bin/sh','-c','echo hello thxh']"
    "command": "[]"'''
    def openstack_create_container(self, form):
        method = "/containers"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json"
        }
    
        temp = """{
                "name": "$name",
                "memory": $ram,
                "cpu": $vcpus,
                "image": "$imagename",
                "interactive": true,
                "nets": [
                {
                    "network" : "$networkid"
                }
                ],
                "image_driver": "glance"
                
            }"""
        
        t = Template(temp)
        body = t.substitute(form.__dict__)
        
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        
        if r.status_code == 202:
            ins = from_dict(data_class=ContainerCreateDetail, data=d)
            return  ins.__dict__
        else:
            return  {"msg":"error"}
        
    def openstack_container_stop(self, id):
        method = "/containers/%s/stop" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json"
        }
        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_container_start(self, id):
        method = "/containers/%s/start" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json"
        }
        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_container_restart(self, id):
        method = "/containers/%s/reboot" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json"
        }
        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_container_rename(self, id,name):
        method = "/containers/%s" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json",
            "OpenStack-API-Version": "container 1.32"
        }
        temp = """{
                "name": "$name"
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.patch(url, data=dd, headers=headers)
        if r.status_code == 200:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_container_resize(self, id,cpu,ram):
        method = "/containers/%s" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json",
            "OpenStack-API-Version": "container 1.32"
        }
        temp = """{
                "cpu": $cpu,
                "memory": "$ram"
            }"""
        
        t = Template(temp)
        body = t.substitute({"cpu":cpu,"ram":ram})
        
        dd = json.dumps(json.loads(body))
        r = requests.patch(url, data=dd, headers=headers)
        if r.status_code == 200:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}

    def openstack_container_delete(self, id):
        method = "/containers/%s?force=True" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json",
            "OpenStack-API-Version": "container 1.32"

        }
        r = requests.delete(url, headers=headers)
        if r.status_code == 204:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_container_logs(self, id):
        method = "/containers/%s/logs" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json"
        }
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            logs = r.text.replace("\\r\\n","<br/>")
            return {"msg":"ok","logs":logs }
        else:
            return {"msg":"error"}
        
        
    
    