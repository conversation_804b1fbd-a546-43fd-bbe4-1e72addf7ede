import requests
import json
import settings
from api.model.images import Image,ImageDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class ImageClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_image_list(self):
        method = "v2/images?container_format=docker"
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["images"]:
            ins = from_dict(data_class=Image, data=ins_dict)
            if ins_dict["status"] == "active":
                ins.__dict__["size"] = ins_dict["size"]
            res.append(ins.__dict__)
        return res
    
    def openstack_container_create_image(self, name):
        method = "v2/images"
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
    
        temp = """{
                    "container_format": "docker",
                    "disk_format": "raw",
                    "name": "$name"
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__
    
    '''
    def openstack_get_image_detail(self,id):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__

    
    
        
    def openstack_delete_image(self, id):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg": "ok"}
        
    def openstack_edit_image(self, id,name):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/openstack-images-v2.1-json-patch"
        }
                 
        temp = """[
                {
                    "op": "replace",
                    "path": "/name",
                    "value": "$name"
                }
            ]"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.patch(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__
        

    def openstack_upload_image(self, image_id, data):
        method = "v2/images/%s/file" % image_id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/octet-stream"
            
        }
        
        r = requests.put(url, headers=headers, data=data)
        
        if r.status_code == 204:
            return {"msg": "ok"}

    def openstack_upload_image_new(self, image_id, data, content_range):
        method = "v2/images/%s/file" % image_id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/octet-stream",
            "Content-Range":content_range
        }
        
        r = requests.put(url, headers=headers, files=data)
        
        if r.status_code == 204:
            return {"msg": "ok"}
    '''       
        
        