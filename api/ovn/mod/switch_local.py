'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import settings
from dataclasses import dataclass
from string import Template
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl

from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.open_vswitch import impl_idl


class Client:
    
    def ovs_create_local_switch(self, switch_name):
        name = switch_name + "-net"
        # 添加端口到桥接
        with self.ovs_api.transaction(check_error=True) as txn:
            txn.add(self.ovs_api.add_br(name))
            print(f"Bridge { name } created")
                
            
        return True
    
    def ovs_local_switch_list(self):
        
        bridges_list = []
        # 直接执行 list_br() 命令来获取桥接列表
        bridges = self.ovs_api.list_br().execute(check_error=False)
        for bridge in bridges:
            bridges_list.append(bridge)
            print(f"Bridge: {bridge}")

        return bridges_list


    def ovs_delete_local_switch(self, switch_name):
        
        # 删除桥接
        with self.ovs_api.transaction(check_error=True) as txn:
            txn.add(self.ovs_api.del_br(switch_name))
            print(f"Bridge {switch_name} created")
       
        return True

    def ovs_add_port_to_bridge(self, bridge_name, port_name, iface_id):
        ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)

        try:
            # 创建与 OVS 数据库的 TCP 连接
            conn = connection.Connection(
                idl=connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch"),
                timeout=10
            )
            ovs_api = impl_idl.OvsdbIdl(conn)
            # 添加端口到桥接
            with ovs_api.transaction(check_error=True) as txn:
                txn.add(ovs_api.add_port(bridge_name, port_name))
                print(f"Port {port_name} added to bridge {bridge_name}")

            with ovs_api.transaction(check_error=True) as txn:
                txn.add(ovs_api.db_set('Interface', port_name, ('external_ids', {'iface-id': iface_id})))
                print(f"Set external_ids for interface {port_name}: iface-id={iface_id}")
        
        finally:
            # 确保连接被关闭
            if conn:
                conn.stop()
                print("Connection to OVS database closed")

        return True 

