
version=`git  log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10`
argtag=`git describe --abbrev=1 --tags`
argdate=`git show -s --date=format:'%Y-%m-%d-%H:%M:%S' --format=%cd`
docker build --no-cache -t tianwen1:5000/hci_asyn:$argtag --build-arg ARG_VERSION=$version --build-arg ARG_TAG=$argtag  --build-arg ARG_COMMIT_DATE=$argdate .
docker tag tianwen1:5000/hci_asyn:$argtag tianwen1:5000/hci_asyn:latest
docker tag tianwen1:5000/hci_asyn:$argtag tianwen1:5000/hci_asyn:loongarch64
docker push tianwen1:5000/hci_asyn:$argtag
docker push tianwen1:5000/hci_asyn:latest
