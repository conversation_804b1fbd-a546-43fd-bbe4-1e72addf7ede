# -*- coding: utf-8 -*-
import uuid
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import (
    Column, String, Integer, create_engine, ForeignKey, 
    Enum, JSON, DateTime, UniqueConstraint
)
from sqlalchemy.orm import sessionmaker, relationship, backref
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import inspect
import datetime
from db.model.hci.base import BaseModel
import enum



        
class Network(BaseModel):
    __tablename__ = 'networks'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(36),  nullable=False)
    status = Column(String(36), comment="网络状态")
    network_type = Column(String(36), comment="网络类型：flat, vlan, vxlan")
    physical_network = Column(String(36), comment="物理网络")
    remark = Column(String(512))

class Subnet(BaseModel):
    __tablename__ = 'subnets'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    network_id = Column(PG_UUID(as_uuid=True), ForeignKey('networks.id'), nullable=False, comment="网络id")
    cidr = Column(String(36),  nullable=False, comment="cidr地址范围")
    gateway_ip = Column(String(36),  nullable=False, comment="默认网关")
    enable_dhcp = Column(String(36), default=True, nullable=False,comment="是否启用dhcp")
    allocation_pools = Column(String(36), comment="动态ip分配池，dhcp使用")
    dns_nameservers = Column(String(36), comment="dns服务器列表")
    host_routes=Column(String(36), comment="路由表")
    ipv6_mode=Column(String(36), comment="ipv6配置模式：slaac, dhcp6, none")
    status = Column(String(36), comment="子网状态")
    vlan_id = Column(Integer, nullable=True, comment="vlan id, 可选字段，仅当network_type是vlan时使用")
    remark = Column(String(512))

    # 关联DHCP范围配置
    dhcp_ranges = relationship("DHCPRange", back_populates="subnet", cascade="all, delete-orphan")


class DHCPRangeType(enum.Enum):
    RANGE = 'range'
    LIST = 'list'
    EXCLUDE = 'exclude'

class DHCPRange(BaseModel):
    __tablename__ = 'dhcp_ranges'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    subnet_id = Column(PG_UUID(as_uuid=True), ForeignKey('subnets.id'), nullable=False)
    type = Column(Enum(DHCPRangeType), nullable=False, comment="DHCP范围类型：range, list, exclude")

    # 使用JSON类型存储IP范围和地址列表，便于灵活配置
    # ranges格式: [{"start": "*************", "end": "*************"}, ...]
    ranges = Column(JSON, comment="IP地址范围列表")

    # addresses格式: ["************", "************", ...]
    addresses = Column(JSON, comment="离散IP地址列表")

    subnet = relationship("Subnet", back_populates="dhcp_ranges")
    
class Port(BaseModel):
    __tablename__ = 'ports'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    subnet_id = Column(PG_UUID(as_uuid=True), ForeignKey('subnets.id'), nullable=False)
    mac_address = Column(String(36),  nullable=False)
    ip_address = Column(String(36),  nullable=False, comment="ip地址")
    device_id = Column(String(36),  nullable=False)
    device_type = Column(String(36),  nullable=False)
    status = Column(String(36), comment="端口状态")
    role = Column(String(36), nullable=False, comment="端口角色：vm 或 bridge")
    switch_port_id = Column(PG_UUID(as_uuid=True),ForeignKey("switchs_ports.id"))
    remark = Column(String(512))

    port_group_id = Column(PG_UUID(as_uuid=True),ForeignKey('port_groups.id'), comment="与端口关联的安全组")
    port_pairs_vm = relationship("PortPair", foreign_keys='PortPair.vm_port_id', back_populates="vm_port")
    port_pairs_bridge = relationship("PortPair", foreign_keys='PortPair.bridge_port_id', back_populates="bridge_port")
    switch_port = relationship("SwitchPorts", back_populates="port")
    domain = relationship("Domain", back_populates="port", uselist=False)
    # 建立与 PortGroups 的双向关系
    port_group = relationship("PortGroups", back_populates="ports")  # 添加此行以建立关系


class PortPair(BaseModel):
    __tablename__ = 'port_pairs'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    vm_port_id = Column(PG_UUID(as_uuid=True), ForeignKey('ports.id'), nullable=False, comment="虚拟机端口ID")
    bridge_port_id = Column(PG_UUID(as_uuid=True), ForeignKey('ports.id'), nullable=False, comment="网桥端口ID")
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime(timezone=True), onupdate=datetime.datetime.now)

    # 关联到两个端口对象
    vm_port = relationship("Port", foreign_keys=[vm_port_id], back_populates="port_pairs_vm")
    bridge_port = relationship("Port", foreign_keys=[bridge_port_id], back_populates="port_pairs_bridge")


class PortGroups(BaseModel):
    __tablename__ = 'port_groups'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(36), nullable=False, comment="端口组名称")
    remark = Column(String(512))
    # 添加与 Port 的双向关系
    ports = relationship("Port", back_populates="port_group")  # 建立与 Port 的关系

class PortGroupRules(BaseModel):
    __tablename__ = 'port_group_rules'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    port_group_id = Column(PG_UUID(as_uuid=True), ForeignKey("port_groups.id"))
    direction = Column(String(36), comment="方向")
    ethertype = Column(String(36), comment="以太网类型：ipv4/ipv6")
    protocol = Column(String(36), comment="协议：TCP/UDP/ICMP")
    port_range_min = Column(String(36), comment="最小端口号")
    port_range_max = Column(String(36), comment="最大端口号")
    remote_ip_prefix = Column(String(36), nullable=False, comment="远程ip前缀")
    remark = Column(String(512))

class Switch(BaseModel):
    __tablename__ = 'switchs'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(36),  nullable=False)
    ovs_name = Column(String(255), nullable=False)  # 移除单独的唯一约束
    host_id = Column(String(36), ForeignKey('host.id'), nullable=False)
    cluster_id = Column(String(36), ForeignKey('cluster.id'), nullable=False)

    vswitch_type = Column(String(36),  nullable=False) #本地：local； 分布式：distributed
    vlan_ids = Column(String(256),  nullable=True)
    status = Column(String(36),  nullable=True) # building, active, deleting
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now)
    extra_config = Column(String(36),  nullable=True)
    mtu = Column(String(36),  nullable=True)

    # 关联关系定义
    host = relationship("Host", back_populates="switchs")
    cluster = relationship("Cluster", back_populates="switchs")
    ports = relationship("SwitchPorts", back_populates="switch", cascade="all, delete-orphan")
    host_mappings = relationship("HostSwitchMapping", back_populates="switch", cascade="all, delete-orphan")
    switch_port_groups = relationship("SwitchPortGroups", back_populates="switch", cascade="all, delete-orphan")

    # 表级约束：同一主机上的 ovs_name 不能重复
    __table_args__ = (
        UniqueConstraint('host_id', 'ovs_name', name='uq_host_ovs_name'),
    )

class SwitchPorts(BaseModel):
    __tablename__ = 'switchs_ports'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    switchs_id = Column(PG_UUID(as_uuid=True), ForeignKey('switchs.id'), nullable=False)
    name = Column(String(36),  nullable=True)
    gate_way = Column(String(36),  nullable=True)
    next_hop = Column(String(36),  nullable=True)
    ip = Column(String(36),  nullable=True)
    ip_type = Column(String(36),  nullable=True)
    netmask = Column(String(36),  nullable=True)
    mode = Column(String(36),  nullable=True)
    pnics = Column(String(36),  nullable=True)
    ports_num = Column(String(36),  nullable=True)
    status = Column(String(36),  nullable=True)
    extra_config = Column(String(36), nullable=True)
    is_physical = Column(Integer, default=0, nullable=False, comment="是否是物理端口，0:否，1:是")
    port_type = Column(String(36), nullable=True)
    interface = Column(String(36), nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    switch_port_group_id = Column(PG_UUID(as_uuid=True), ForeignKey('switch_port_groups.id'), nullable=True)
    # Define the relationship to Switch
    switch = relationship("Switch", back_populates="ports")
    port = relationship("Port", uselist=False, back_populates="switch_port")
    switch_port_group = relationship("SwitchPortGroups", uselist=False, back_populates="switch_ports")

class SwitchPortGroups(BaseModel):
    __tablename__ = 'switch_port_groups'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(36), nullable=False, comment="交换机端口组名称")
    remark = Column(String(512))
    # 添加与 SwitchPort 的双向关系
    vlan_id = Column(Integer, unique=True, comment="VLAN ID")

    # 添加指向 Switch 的外键
    switchs_id = Column(PG_UUID(as_uuid=True), ForeignKey('switchs.id'), nullable=False)

    __table_args__ = (
        UniqueConstraint('vlan_id', name='uq_switch_port_group_vlan_id'),
    )
    
    # 关系定义
    switch = relationship("Switch", back_populates="switch_port_groups")

    switch_ports = relationship("SwitchPorts", back_populates="switch_port_group")  # 建立与 Port 的关系
    # 添加与规则的双向关系
    rules = relationship("SwitchPortGroupRules", back_populates="switch_port_group")


class SwitchPortGroupRules(BaseModel):
    __tablename__ = 'switch_port_group_rules'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    switch_port_group_id = Column(PG_UUID(as_uuid=True), ForeignKey("switch_port_groups.id"))
    direction = Column(String(36), comment="方向") #'ingress', 'egress
    ethertype = Column(String(36), comment="以太网类型：ipv4/ipv6")
    protocol = Column(String(36), comment="协议：TCP/UDP/ICMP")
    port_range_min = Column(String(36), comment="最小端口号")
    port_range_max = Column(String(36), comment="最大端口号")
    remote_ip_prefix = Column(String(36), nullable=False, comment="远程ip前缀")
    action = Column(String(36), nullable=False, comment="动作") # 'allow', 'deny', 'modify'
    priority = Column(String(36), nullable=False, comment="规则优先级")
    remark = Column(String(512))

    # 设置反向关系
    switch_port_group = relationship("SwitchPortGroups", back_populates="rules")

    
class RouterTable(BaseModel):
    __tablename__ = 'router_table'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    router_id = Column(PG_UUID(as_uuid=True), ForeignKey('routers.id'), nullable=False)
    next_hop = Column(String(36),  nullable=False)
    remark = Column(String(512))
    
class Router(BaseModel):
    __tablename__ = 'routers'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    cluster_id = Column(String(36),  nullable=False)
    name = Column(String(36),  nullable=False)
    status = Column(String(36), comment="路由状态")
    external_gateway_info = Column(String(36), comment="外部网关信息")
    remark = Column(String(512))

class RouterPort(BaseModel):
    __tablename__ = 'router_port'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    router_id = Column(PG_UUID(as_uuid=True), ForeignKey('routers.id'), nullable=False, comment="路由器端口id")
    port_id = Column(PG_UUID(as_uuid=True), ForeignKey('ports.id'), nullable=False, comment="关联的端口id,可选外键，关联到port表")
    subnet_id = Column(PG_UUID(as_uuid=True), ForeignKey('subnets.id'), nullable=False, comment="关联的子网id")
    remark = Column(String(512))
    
class Device(BaseModel):
    __tablename__ = 'devices'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(36),  nullable=False)
    device_type = Column(String(36),  nullable=False)
    remark = Column(String(512))

class DevicePort(BaseModel):
    __tablename__ = 'device_ports'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    device_id = Column(PG_UUID(as_uuid=True), ForeignKey('devices.id'), nullable=False)
    port_id = Column(PG_UUID(as_uuid=True), ForeignKey('ports.id'), nullable=False)
    remark = Column(String(512))



class HostSwitchMapping(BaseModel):
    __tablename__ = 'host_switches_mapping'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    host_id = Column(String(36), ForeignKey('host.id'), nullable=False, comment='主机ID')
    switch_id = Column(PG_UUID(as_uuid=True), ForeignKey('switchs.id'), nullable=False, comment='OVS网桥ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    remark = Column(String(512), comment='备注')

    # 关联关系
    host = relationship("Host", back_populates="switch_mappings")
    switch = relationship("Switch", back_populates="host_mappings")

    # 联合唯一约束
    __table_args__ = (
        UniqueConstraint('host_id', 'switch_id', name='uix_host_switch'),
    )

