# -*- coding: utf-8 -*-
"""
虚拟机备份数据库模型
基于libvirt外部快照实现的虚拟机备份功能相关表结构
"""
import uuid
import datetime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, String, Integer, ForeignKey, BigInteger, DateTime, Text, Boolean, JSON, Index
from sqlalchemy.orm import relationship
from db.model.hci.base import BaseModel


class DomainBackup(BaseModel):
    """
    虚拟机备份主表
    记录虚拟机的备份信息和快照树形结构
    """
    __tablename__ = 'domain_backup'
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='备份主键')

    # 虚拟机信息
    domain_id = Column(PG_UUID(as_uuid=True), ForeignKey('domain.id'), nullable=False, comment='虚拟机ID')

    
    # 备份基本信息
    backup_name = Column(String(255), nullable=False, comment='备份名称（快照名称）')
    backup_type = Column(String(32), nullable=False, default='external', comment='备份类型：external, internal, full')
    backup_status = Column(String(32), nullable=False, default='creating', comment='备份状态：creating, completed, failed, deleting, deleted')
    
    # 快照信息
    backup_xml = Column(Text, nullable=True, comment='快照XML配置')
    
    # 备份描述和元数据
    description = Column(String(512), nullable=True, comment='备份描述')
    backup_reason = Column(String(100), nullable=True, comment='备份原因：manual, scheduled, pre_update等')
    
    # 主机信息
    host_id = Column(PG_UUID(as_uuid=True), ForeignKey('host.id'), nullable=False, comment='执行备份的主机ID')
    host_ip = Column(String(32), nullable=True, comment='主机IP（冗余字段，便于查询）')
    
    # 大小统计
    total_size = Column(BigInteger, nullable=True, default=0, comment='备份总大小（字节）')
    disk_count = Column(Integer, nullable=True, default=0, comment='备份磁盘数量')
    
    # 时间信息
    backup_start_time = Column(DateTime(timezone=True), nullable=True, comment='备份开始时间')
    backup_end_time = Column(DateTime(timezone=True), nullable=True, comment='备份完成时间')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间')
    
    # 用户信息
    created_by = Column(String(100), nullable=True, comment='创建者')
    created_by_role = Column(String(50), nullable=True, comment='创建者角色')
    
    # 备份策略信息
    retention_days = Column(Integer, nullable=True, comment='保留天数')
    is_auto_backup = Column(Boolean, nullable=False, default=False, comment='是否自动备份')
    backup_policy_id = Column(PG_UUID(as_uuid=True), nullable=True, comment='备份策略ID')
    
    # 其他信息
    error_message = Column(Text, nullable=True, comment='错误信息')
    backup_metadata = Column(JSON, nullable=True, comment='备份元数据（JSON格式）')
    
    # 关系定义
    # domain = relationship("Domain", backref="domain_backups")
    # host = relationship("Host", backref="domain_backups")
    

    # 备份存储卷关系
    backup_volumes = relationship("BackupVolumeMapping", back_populates="domain_backup", cascade="all, delete-orphan")


class BackupVolumeMapping(BaseModel):
    """
    备份存储卷关联表
    记录备份与存储卷的关联关系
    """
    __tablename__ = 'backup_volume_mapping'
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='关联记录主键')
    
    # 关联信息
    vm_backup_id = Column(PG_UUID(as_uuid=True), ForeignKey('domain_backup.id'), nullable=False, comment='备份ID')
    storage_volume_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_volume.id'), nullable=False, comment='存储卷ID')
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间')
    
    # 关系定义
    domain_backup = relationship("DomainBackup", back_populates="backup_volumes")
    # storage_volume = relationship("StorageVolume", backref="backup_mappings")
    
    # 索引
    __table_args__ = (
        Index('idx_backup_volume_mapping_backup_id', 'vm_backup_id'),
        Index('idx_backup_volume_mapping_volume_id', 'storage_volume_id'),
    )
