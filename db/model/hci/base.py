# -*- coding: utf-8 -*-
import uuid
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import inspect

import datetime
Base = declarative_base()


class BaseModel(Base):
    __abstract__ = True

    @classmethod
    def from_dict(cls, data):
        # 获取模型的所有列名
        valid_columns = {column.name for column in inspect(cls).c}

        # 过滤字典数据，只保留模型中定义的字段
        filtered_data = {key: value for key, value in data.items() if key in valid_columns}

        # 使用过滤后的数据创建模型实例
        return cls(**filtered_data)

    def to_dict(self):
        """
        将 SQLAlchemy 模型实例转换为字典，处理 UUID 和过滤内部属性。
        """
        result = {}
        for column in inspect(self).mapper.column_attrs:
            value = getattr(self, column.key)
            if isinstance(value, uuid.UUID):
                result[column.key] = str(value)  # 将 UUID 转换为字符串
            else:
                result[column.key] = value
        
            if isinstance(value, datetime.datetime):
                result[column.key] = value.strftime('%Y-%m-%d %H:%M:%S')
        return result
    
    def to_dict_deep(self):
        """
        将 SQLAlchemy 模型实例转换为字典，处理 UUID 和过滤内部属性。
        如果字段是一个列表，并且列表中的元素是对象，将递归调用 to_dict_deep 进行处理。
        """
        result = {}
        for column in inspect(self).mapper.column_attrs:
            value = getattr(self, column.key)
            
            if isinstance(value, uuid.UUID):
                result[column.key] = str(value)  # 将 UUID 转换为字符串
            elif isinstance(value, datetime.datetime):
                result[column.key] = value.strftime('%Y-%m-%d %H:%M:%S')  # 将 datetime 转换为字符串
            else:
                result[column.key] = value

         # 处理关系属性
        for relation in inspect(self).mapper.relationships:
            related_value = getattr(self, relation.key)
            if related_value is not None:
                if isinstance(related_value, list):
                    result[relation.key] = [
                        item.to_dict_deep() if hasattr(item, 'to_dict_deep') else item
                        for item in related_value
                    ]
                if isinstance(related_value, object):
                    result[relation.key] = {}
                    for relation_column in inspect(related_value).mapper.column_attrs:
                        relation_value = getattr(related_value, relation_column.key)
                        print(relation_column.key)
                        print(relation_value)
                        
                        if isinstance(relation_value, uuid.UUID):
                            result[relation.key][relation_column.key] = str(relation_value)  # 将 UUID 转换为字符串
                        elif isinstance(relation_value, datetime.datetime):
                            result[relation.key][relation_column.key] = relation_value.strftime('%Y-%m-%d %H:%M:%S')  # 将 datetime 转换为字符串
                        else:
                            result[relation.key][relation_column.key] = relation_value
        return result
    

    def to_dict_merge(self, processed=None):
        """
        将 SQLAlchemy 模型实例转换为字典，处理 UUID 和过滤内部属性。
        如果字段是一个列表，并且列表中的元素是对象，将递归调用 to_dict_merge 进行处理。
        """
        if processed is None:
            processed = set()
            
        # 如果对象已经被处理过，直接返回空字典
        if id(self) in processed:
            return {}
            
        processed.add(id(self))
        result = {}
        
        for column in inspect(self).mapper.column_attrs:
            value = getattr(self, column.key)
            
            if isinstance(value, uuid.UUID):
                result[column.key] = str(value)  # 将 UUID 转换为字符串
            elif isinstance(value, datetime.datetime):
                result[column.key] = value.strftime('%Y-%m-%d %H:%M:%S')  # 将 datetime 转换为字符串
            else:
                result[column.key] = value

        # 处理关系属性
        for relation in inspect(self).mapper.relationships:
            related_value = getattr(self, relation.key)
            if related_value is not None:
                if isinstance(related_value, list):
                    # 如果是列表类型的关系，递归处理每个元素
                    result[relation.key] = [item.to_dict_merge(processed) for item in related_value]
                elif isinstance(related_value, object):
                    # 如果是单个对象，处理其属性
                    for relation_column in inspect(related_value).mapper.column_attrs:
                        relation_value = getattr(related_value, relation_column.key)
                        r_key = "%s_%s" % (relation.key, relation_column.key)   
                        
                        if isinstance(relation_value, uuid.UUID):
                            result[r_key] = str(relation_value)  # 将 UUID 转换为字符串
                        elif isinstance(relation_value, datetime.datetime):
                            result[r_key] = relation_value.strftime('%Y-%m-%d %H:%M:%S')  # 将 datetime 转换为字符串
                        else:
                            result[r_key] = relation_value
        return result