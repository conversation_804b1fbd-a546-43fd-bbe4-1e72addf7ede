from sqlalchemy import Column, <PERSON>, Integer, DateTime, <PERSON><PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

from db.model.hci.base import BaseModel
from sqlalchemy.dialects.postgresql import UUID as PG_UUID


class HardwareInfo(BaseModel):
    __tablename__ = 'hw_hardware_info'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    host_id = Column(String(36), ForeignKey('host.id'), nullable=False)
    create_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    network_cards = relationship("NetworkCard", back_populates="hardware_info")
    disks = relationship("Disk", back_populates="hardware_info")
    usbs = relationship("Usb", back_populates="hardware_info")
    pci_devices = relationship("PCIDevice", back_populates="hardware_info")
    cpu_infos = relationship("CPUInfo", back_populates="hardware_info")
    memory_infos = relationship("MemoryInfo", back_populates="hardware_info")
    gpus = relationship("GPUInfo", back_populates="hardware_info")
    hba_devices = relationship("HBADevice", back_populates="hardware_info")
    host = relationship("Host", back_populates="hardware_info", uselist=False)

class NetworkCard(BaseModel):
    __tablename__ = 'hw_network_card'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    type = Column(String(50))
    mac = Column(String(17))
    speed = Column(String(50))
    duplex = Column(String(50))
    status = Column(String(50))
    mtu = Column(Integer)
    link_status = Column(String(50))
    ip_address = Column(String(39))
    vendor = Column(String(255))

    hardware_info = relationship("HardwareInfo", back_populates="network_cards")

class Disk(BaseModel):
    __tablename__ = 'hw_disk'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    device = Column(String(255), nullable=False)
    size = Column(BigInteger)
    vendor = Column(String(255))
    model = Column(String(255))
    status = Column(String(50))

    hardware_info = relationship("HardwareInfo", back_populates="disks")

class Usb(BaseModel):
    __tablename__ = 'hw_usb'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    device = Column(String(50), nullable=False)
    name = Column(String(255))
    description = Column(String(255))
    status = Column(String(50))
    pci_path = Column(String(255))

    hardware_info = relationship("HardwareInfo", back_populates="usbs")

class PCIDevice(BaseModel):
    __tablename__ = 'hw_pci_device'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    device_id = Column(String(50), nullable=False)
    name = Column(String(255))
    type = Column(String(255))
    status = Column(String(50))

    hardware_info = relationship("HardwareInfo", back_populates="pci_devices")

class CPUInfo(BaseModel):
    __tablename__ = 'hw_cpu_info'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    model = Column(String(255))
    frequency = Column(String(50))
    threads = Column(Integer)
    cores = Column(Integer)
    cache_l1 = Column(String(50))
    cache_l2 = Column(String(50))
    cache_l3 = Column(String(50))

    hardware_info = relationship("HardwareInfo", back_populates="cpu_infos")

class MemoryInfo(BaseModel):
    __tablename__ = 'hw_memory_info'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    name = Column(String(255))
    manufacturer = Column(String(255))
    size = Column(BigInteger)
    type = Column(String(255))

    hardware_info = relationship("HardwareInfo", back_populates="memory_infos")

class GPUInfo(BaseModel):
    __tablename__ = 'hw_gpu_info'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    model = Column(String(255))
    vendor = Column(String(255))
    device_id = Column(String(255))
    used_vgpus = Column(Integer)
    available_vgpus = Column(Integer)

    hardware_info = relationship("HardwareInfo", back_populates="gpus")

class HBADevice(BaseModel):
    __tablename__ = 'hw_hba_device'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hardware_info_id = Column(String(36), ForeignKey('hw_hardware_info.id'), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    wwn = Column(String(255))
    wwpn = Column(String(255))
    vendor = Column(String(255))
    model = Column(String(255))

    hardware_info = relationship("HardwareInfo", back_populates="hba_devices")