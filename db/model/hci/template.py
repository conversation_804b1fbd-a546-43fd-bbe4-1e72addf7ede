# -*- coding: utf-8 -*-
import uuid
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, BigInteger, DateTime
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import datetime
from db.model.hci.base import BaseModel

class Template(BaseModel):
    __tablename__ = 'template'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    vcpu = Column(String(30),  nullable=True)
    cpu_arch = Column(String(30),  nullable=True)
    memory = Column(String(30),  nullable=True)
    memory_unit = Column(String(30),  nullable=True)
    disk_path = Column(String(30),  nullable=True)
    disk_name = Column(String(30),  nullable=True)
    disk_type = Column(String(30),  nullable=True)
    disk_type_code = Column(String(100), nullable=True, default="local")
    network = Column(String(100))
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True)
