from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, Boolean, BigInteger, create_engine

meta = MetaData()
storage_device = Table(
    'storage_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('device_type', String(64), nullable=False, comment='存储的类型: 1 FC， 2 ISCSI，3 NFS，4 RBD（CEPH） 5.本地目录'),
    Column('device_name', String(100), nullable=False, comment='存储设备名称(区分大小写)'),
    Column('model', String(100), nullable=False, comment='型号'),
    Column('vendor', String(100), nullable=False, comment='厂家'),
    Column('total_capacity', BigInteger, nullable=True, comment='存储总容量,单位:字节(byte)'),
    Column('used_capacity', BigInteger, nullable=True, comment='已使用存储总容量,单位:字节(byte)'),
    Column('ip_mgmt', String(255), nullable=True, comment='管理ip集合(ip间用逗号隔开),如*************,*************'),
    Column('last_scn_time', DateTime(timezone=True), nullable=True, comment='最后扫描的时间'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)



nas_device = Table(
    'nas_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='NAS设备主键'),
    Column('device_id', String(36), ForeignKey('storage_device.id'), nullable=False, unique=True, comment='存储设备ID'),
    Column('hostname', String(100), nullable=True, comment='主机名'),
    Column('ip_address', String(50), nullable=False, comment='IP地址'),
    Column('protocols', String(100), nullable=True, comment='支持的协议, 如 "NFS,SMB,FTP"'),
    Column('authentication_type', String(50), nullable=True, comment='认证类型: AD, LDAP, local, etc.'),
    Column('domain_name', String(100), nullable=True, comment='域名'),
    Column('has_snapshots', Boolean, default=False, nullable=True, comment='是否支持快照'),
    Column('quota_enabled', Boolean, default=False, nullable=True, comment='是否启用配额'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

nas_share = Table(
    'nas_share', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='共享主键'),
    Column('nas_device_id', String(36), ForeignKey('nas_device.id'), nullable=False, comment='NAS设备ID'),
    Column('share_name', String(255), nullable=False, comment='共享名称'),
    Column('share_path', String(255), nullable=False, comment='共享路径'),
    Column('protocol', String(10), nullable=False, comment='协议类型: NFS, SMB, FTP, other'),
    Column('access_rights',  String(100), nullable=True, comment='访问权限设置'),
    Column('client_list',  String(100), nullable=True, comment='允许的客户端列表'),
    Column('quota_limit', BigInteger, nullable=True, comment='配额限制'),
    Column('status', String(10), nullable=False, comment='状态: active, inactive'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    nas_device.create()
    nas_share.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    nas_share.drop()
    nas_device.drop()
