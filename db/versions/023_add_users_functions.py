from sqlalchemy import Table, Column, Integer, String, MetaData, create_engine

# 初始化元数据
meta = MetaData()

# 创建 'users_functions' 表
users_functions = Table(
    'users_functions', meta,
    Column('user_id', String(64)),
    Column('function_ids', String(255))
)

def upgrade(migrate_engine):
    # 绑定引擎到元数据
    meta.bind = migrate_engine
    # 创建 'users_functions' 表
    users_functions.create(migrate_engine)

def downgrade(migrate_engine):
    # 绑定引擎到元数据
    meta.bind = migrate_engine
    # 删除 'users_functions' 表
    users_functions.drop(migrate_engine)