from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

dictionary = Table(
    'dictionary', meta,
    Column('id', Integer, primary_key=True, nullable=False, comment='主键'),
    Column('type_code', String(36), nullable=False, comment='字典类型，例如: storage, network'),
    Column('name', String(36), nullable=False, comment='字典项的名称'),
    Column('code', String(36), nullable=False, unique=True, comment='字典项的唯一编码'),
    Column('order', Integer, nullable=False, comment='排序顺序')
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    dictionary.create()
    # 插入数据
    data_to_insert = [
        {"id": 1, "type_code": "storage", "name": "本地存储", "code": "local", "order": 1},
        {"id": 2, "type_code": "storage", "name": "分布式存储", "code": "ceph", "order": 1},
        {"id": 3, "type_code": "storage", "name": "FC-SAN", "code": "fc_san", "order": 1},
        {"id": 4, "type_code": "storage", "name": "NAS", "code": "nas", "order": 1},
        {"id": 5, "type_code": "volume", "name": "qcow2", "code": "qcow2", "order": 2},
        {"id": 6, "type_code": "volume", "name": "raw", "code": "raw", "order": 2},
        {"id": 7, "type_code": "volume", "name": "iso", "code": "iso", "order": 2},

    ]

    # 使用 migrate_engine 进行连接并插入数据
    conn = migrate_engine.connect()
    conn.execute(dictionary.insert(), data_to_insert)
    conn.close()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    dictionary.drop()
