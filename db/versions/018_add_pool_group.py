from sqlalchemy import *
from migrate import *
import datetime
from sqlalchemy import (
    Table, Column, String, Integer, MetaData, DateTime, ForeignKey, 
    create_engine, Boolean, UniqueConstraint, inspect
)

meta = MetaData()

def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 1. 创建存储池组表
    storage_pool_group = Table(
        'storage_pool_group', meta,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('name', String(64), nullable=False),
        <PERSON>umn('pid', Integer, nullable=False, default=0),
        Column('remark', String(64), nullable=True),
        <PERSON>umn('created_at', DateTime, default=datetime.datetime.now),
        <PERSON>umn('updated_at', DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now),
    )
    storage_pool_group.create()

    # 2. 创建组与存储池关联表
    storage_pool_group_mapping = Table(
        'storage_pool_group_mapping', meta,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('storage_pool_group_id', Integer, ForeignKey('storage_pool_group.id'), nullable=False),
        Column('pool_id', String(36), nullable=False),  # 存储池uuid
        Column('created_at', DateTime, default=datetime.datetime.now),
    )
    storage_pool_group_mapping.create()

    # 3. 插入一个默认组
    conn = migrate_engine.connect()
    conn.execute(
        storage_pool_group.insert().values(
            id = 1,
            name='默认',
            pid=0,
            remark='默认组',
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now()
        )
    )

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    storage_pool_group_mapping = Table('storage_pool_group_mapping', meta, autoload=True)
    storage_pool_group_mapping.drop()
    storage_pool_group = Table('storage_pool_group', meta, autoload=True)
    storage_pool_group.drop()