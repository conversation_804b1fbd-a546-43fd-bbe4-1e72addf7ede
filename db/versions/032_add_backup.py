from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, String, Integer, BigInteger, DateTime, MetaData, create_engine, text, Boolean, Text
import datetime

meta = MetaData()

# 定义域备份主表
domain_backup = Table(
    'domain_backup', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='备份主键'),
    
    # 虚拟机信息
    Column('domain_id', String(36), nullable=False, comment='虚拟机ID'),
    
    # 备份基本信息
    Column('backup_name', String(255), nullable=False, comment='备份名称（快照名称）'),
    Column('backup_type', String(32), nullable=False, default='external', comment='备份类型：external, internal, full'),
    Column('backup_status', String(32), nullable=False, default='creating', comment='备份状态：creating, completed, failed, deleting, deleted'),
    
    # 快照信息
    Column('backup_xml', Text, nullable=True, comment='快照XML配置'),
    
    # 备份描述和元数据
    Column('description', String(512), nullable=True, comment='备份描述'),
    Column('backup_reason', String(100), nullable=True, comment='备份原因：manual, scheduled, pre_update等'),
    
    # 主机信息
    Column('host_id', String(36), nullable=False, comment='执行备份的主机ID'),
    Column('host_ip', String(32), nullable=True, comment='主机IP（冗余字段，便于查询）'),
    
    # 大小统计
    Column('total_size', BigInteger, nullable=True, default=0, comment='备份总大小（字节）'),
    Column('disk_count', Integer, nullable=True, default=0, comment='备份磁盘数量'),
    
    # 时间信息
    Column('backup_start_time', DateTime, nullable=True, comment='备份开始时间'),
    Column('backup_end_time', DateTime, nullable=True, comment='备份完成时间'),
    Column('created_at', DateTime, default=datetime.datetime.now, nullable=False, comment='创建时间'),
    Column('updated_at', DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间'),
    
    # 用户信息
    Column('created_by', String(100), nullable=True, comment='创建者'),
    Column('created_by_role', String(50), nullable=True, comment='创建者角色'),
    
    # 备份策略信息
    Column('retention_days', Integer, nullable=True, comment='保留天数'),
    Column('is_auto_backup', Boolean, nullable=False, default=False, comment='是否自动备份'),
    Column('backup_policy_id', String(36), nullable=True, comment='备份策略ID'),
    
    # 其他信息
    Column('error_message', Text, nullable=True, comment='错误信息'),
    Column('backup_metadata', Text, nullable=True, comment='备份元数据（JSON格式）'),
)

# 定义备份存储卷关联表
backup_volume_mapping = Table(
    'backup_volume_mapping', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='关联记录主键'),
    
    # 关联信息
    Column('vm_backup_id', String(36), nullable=False, comment='备份ID'),
    Column('storage_volume_id', String(36), nullable=False, comment='存储卷ID'),
    
    # 时间信息
    Column('created_at', DateTime, default=datetime.datetime.now, nullable=False, comment='创建时间'),
    Column('updated_at', DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间'),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    domain_backup.create()
    backup_volume_mapping.create()
    
    # 创建索引
    # 备份主表索引
    Index('idx_domain_backup_domain_id', domain_backup.c.domain_id).create()
    Index('idx_domain_backup_host_id', domain_backup.c.host_id).create()
    Index('idx_domain_backup_status', domain_backup.c.backup_status).create()
    Index('idx_domain_backup_created_at', domain_backup.c.created_at).create()
    Index('idx_domain_backup_name', domain_backup.c.backup_name).create()
    
    # 备份存储卷关联表索引
    Index('idx_backup_volume_mapping_backup_id', backup_volume_mapping.c.vm_backup_id).create()
    Index('idx_backup_volume_mapping_volume_id', backup_volume_mapping.c.storage_volume_id).create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 删除索引
    try:
        Index('idx_backup_volume_mapping_volume_id', backup_volume_mapping.c.storage_volume_id).drop()
        Index('idx_backup_volume_mapping_backup_id', backup_volume_mapping.c.vm_backup_id).drop()
        Index('idx_domain_backup_name', domain_backup.c.backup_name).drop()
        Index('idx_domain_backup_created_at', domain_backup.c.created_at).drop()
        Index('idx_domain_backup_status', domain_backup.c.backup_status).drop()
        Index('idx_domain_backup_host_id', domain_backup.c.host_id).drop()
        Index('idx_domain_backup_domain_id', domain_backup.c.domain_id).drop()
    except Exception as e:
        print(f"Warning: Could not drop some indexes: {e}")
    
    # 删除表（注意顺序，先删除有外键依赖的表）
    backup_volume_mapping.drop()
    domain_backup.drop()
