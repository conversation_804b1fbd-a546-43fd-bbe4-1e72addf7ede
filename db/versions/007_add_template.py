from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()



template = Table(
    'template', meta,
    Column('id', String(64), primary_key=True, comment='id'),
    <PERSON>umn('name', String(64), comment='模板名称'),
    <PERSON>umn('vcpu', String(64), comment='cpu数量'),
    <PERSON><PERSON><PERSON>('cpu_arch', String(64), comment='cpu架构'),
    <PERSON><PERSON><PERSON>('memory', String(64), comment='内存大小'),
    <PERSON>umn('memory_unit', String(64), comment='内存单位'),
    <PERSON>umn('disk_path', String(255), comment='磁盘路径'),
    <PERSON><PERSON><PERSON>('disk_name', String(255), comment='磁盘名称'),
    <PERSON><PERSON><PERSON>('disk_type', String(255), comment='磁盘格式'),
    <PERSON>umn('disk_type_code', String(64), comment='磁盘类型'),
    <PERSON>umn('network', String(64), comment='网络名称'),
    Column('created_at', DateTime(), comment='创建时间'),
    <PERSON>umn('updated_at', DateTime(), comment='更新时间'),
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    template.create()

    
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    template.drop()

