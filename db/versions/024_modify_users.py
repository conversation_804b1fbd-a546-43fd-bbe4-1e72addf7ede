from sqlalchemy import *
from migrate import *

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('users', meta, autoload=True)

    # 新建可空字符串列，无默认值
    is_first_login = Column('is_first_login', String(16), nullable=True)
    is_first_login.create(users)


def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('users', meta, autoload=True)
    users.c.is_first_login.drop()