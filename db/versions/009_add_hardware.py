from sqlalchemy import *
from migrate import *
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

hw_hardware_info = Table(
    'hw_hardware_info', meta,
    Column('id', String(36), primary_key=True),
    Column('host_id', String(36)),
    Column('status', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)

hw_network_card = Table(
    'hw_network_card', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    <PERSON>umn('name', String(255)),
    Column('type', String(50)),
    Column('mac', String(17)),
    Column('speed', String(50)),
    Column('duplex', String(50)),
    Column('status', String(50)),
    <PERSON>umn('mtu', Integer),
    <PERSON>umn('link_status', String(50)),
    Column('ip_address', String(39)),
    Column('vendor', String(255)),
)

hw_disk = Table(
    'hw_disk', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('device', String(255)),
    Column('size', BigInteger),
    Column('vendor', String(255)),
    Column('model', String(255)),
    Column('status', String(50)),
)

hw_usb = Table(
    'hw_usb', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('device', String(255)),
    Column('name', String(255)),
    Column('description', String(255)),
    Column('status', String(50)),
    Column('pci_path', String(255)),
)

hw_pci_device = Table(
    'hw_pci_device', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('device_id', String(50)),
    Column('name', String(255)),
    Column('type', String(255)),
    Column('status', String(50)),
)

hw_cpu_info = Table(
    'hw_cpu_info', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('model', String(255)),
    Column('cores', Integer),
    Column('threads', Integer),
    Column('frequency', String(50)),
    Column('cache_l1', String(50)),
    Column('cache_l2', String(50)),
    Column('cache_l3', String(50)),
)

hw_memory_info = Table(
    'hw_memory_info', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('manufacturer', String(255)),
    Column('name', String(255)),
    Column('size', BigInteger),
    Column('type', String(255)),
)

hw_gpu_info = Table(
    'hw_gpu_info', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('model', String(255)),
    Column('vendor', String(255)),
    Column('device_id', String(255)),
    Column('used_vgpus', Integer),
    Column('available_vgpus', Integer),
)

hw_hba_device = Table(
    'hw_hba_device', meta,
    Column('id', String(36), primary_key=True),
    Column('hardware_info_id', String(36)),
    Column('name', String(255)),
    Column('wwn', String(255)),
    Column('wwpn', String(255)),
    Column('vendor', String(255)),
    Column('model', String(255)),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    hw_hardware_info.create()
    hw_network_card.create()
    hw_disk.create()
    hw_usb.create()
    hw_pci_device.create()
    hw_cpu_info.create()
    hw_memory_info.create()
    hw_gpu_info.create()
    hw_hba_device.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    hw_hardware_info.drop()
    hw_network_card.drop()
    hw_disk.drop()
    hw_usb.drop()
    hw_pci_device.drop()
    hw_cpu_info.drop()
    hw_memory_info.drop()
    hw_gpu_info.drop()
    hw_hba_device.drop()