from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, create_engine 
from sqlalchemy import Table, Column, String, Integer, BigInteger, DateTime, MetaData, ForeignKey, create_engine, text
import datetime


meta = MetaData()

# 定义主机池
pool = Table(
    'pool', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('name', String(30), nullable=False, unique=True, comment='名称'),
    Column('remark', String(512), comment='备注'),
)


# 定义 cluster 表结构
cluster = Table(
    'cluster', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('pool_id', String(36), nullable=False, comment='(外键)主机池'),
    Column('name', String(30), nullable=False, unique=True, comment='名称'),
    Column('remark', String(512), comment='备注'),
    Column('ha_level', Integer, nullable=False, default=0, comment='ha等级. 0不启用, 1低, 2中(默认), 3高'),
    Column('enabled_drs', Integer, comment='是否启用 DRS'),
    Column('enable_storage_drs', Integer, comment='1：启用，0不启用'),
    Column('enable_dmp', Integer, comment='1：启用，0不启用'),
    Column('cpu_architecture', String(50), comment='cpu架构'),
)

# 定义 host 表结构
host = Table(
    'host', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('pool_id', String(36),  nullable=False, comment='(外键)主机池'),
    Column('cluster_id', String(36),  nullable=False, comment='(外键)集群'),
    Column('src_cluster_id', String(36), comment='(外键)原集群'),
    Column('is_supporting', Integer, nullable=True, comment='是否支援主机'),
    Column('ip', String(30), nullable=False, unique=True, comment='ip地址'),
    Column('name', String(30), nullable=False, unique=True, comment='名称'),
    Column('remark', String(512), comment='备注'),
    Column('ssh_user', String(30), nullable=False, comment='ssh用户'),
    Column('ssh_password', String(256), nullable=False, comment='ssh用户密码(aes加密)'),
    Column('port', String(32), comment='ssh端口'),
    Column('hostname', String(256), nullable=False, comment='主机名称. hostname脚本获取'),
    Column('host_model', String(256), comment='主机型号'),
    Column('mac', String(32), comment='mac地址'),
    Column('cpu_architecture', String(50), nullable=True, comment='cpu架构'),
    Column('cpu_threads', Integer, nullable=True, comment='逻辑cpu(线程)总数'),
    Column('cpu_threads_per_core', Integer, nullable=True, comment='cpu每物理核心线程数'),
    Column('cpu_cores_per_socket', Integer, nullable=True, comment='cpu每socket物理核心数'),
    Column('cpu_sockets', Integer, nullable=True, comment='cpu的socket数量'),
    Column('cpu_cores', Integer, nullable=True, comment='cpu的core数量'),
    Column('cpu_model_name', String(256), nullable=True, comment='cpu的型号名称'),
    Column('cpu_frequency', BigInteger, nullable=True, comment='cpu频率(单位: MHz)'),
    Column('cpu_hfrequency', String(30), nullable=True, comment='cpu频率(human-readable)'),
    Column('cpu_virtualization', String(100), comment='cpu虚拟化( 如: VT-x ). lscpu不一定能拿到'),
    Column('cpu_virtualization_type', String(100), comment='cpu虚拟化类型( 如: full ). lscpu不一定能拿到'),
    Column('memory', BigInteger, nullable=True, comment='内存大小(单位: byte).采用向下舍入floor规则'),
    Column('hmemory', String(30), nullable=True, comment='内存大小(human-readable)'),
    Column('storage', BigInteger, nullable=True, comment='存储大小(本地 /cnware 分区大小)(单位: byte).采用向下舍入floor规则'),
    Column('hstorage', String(30), nullable=True, comment='存储大小(human-readable)'),
    Column('is_ha', Integer, nullable=True, default=0, comment='是否启用HA 0否 1是'),
    Column('is_maintain', Integer, nullable=True, comment='是否处于维护状态. 0否(平台可以对主机进行需要的操作), 1是(希望平台断开主机连接,不对主机进行任意操作)'),
    Column('is_connected', Integer, nullable=True, comment='是否连接上. 0否(在规定时间内,平台多次尝试,均未能连接上主机) 1是(连接正常)'),
    Column('conn_or_disconn_time', DateTime, nullable=True, default=text("'1970-01-01 00:00:00'"), comment='连接或断开连接时间'),
    Column('is_ha_migrated', Integer, nullable=True, default=0, comment='刚执行过HA迁移. 1是(触发HA迁移后会更新为1)  0否(重新连接会更新为0)'),
    Column('wake_category', Integer, nullable=True, default=2, comment='0:网络唤醒,1:IPMI唤醒,2:禁用'),
    Column('ipmi_ipaddr', String(30), comment='主机ilo地址'),
    Column('ipmi_user', String(30), comment='ipmi用户'),
    Column('ipmi_pw', String(256), comment='ipmi密码'),
    Column('system', String(256), comment='主机系统'),
    Column('uptime', String(256), comment='主机系统启动时间(uptime -s)'),
    Column('numa_cell', Integer, comment='numa节点数'),
    Column('cpu_model_type', String(30), comment='cpu的型号类型'),
    Column('bmc', String(256), comment='主机系统启动时间(uptime -s)'),
    Column('flags', String(4096), comment='标志寄存器'),
    Column('kv_version', String(32), comment='kv版本'),
    Column('iscsi_initiator', String(256), comment='iscsi initiator name'),
    Column('reserve_memory', BigInteger, comment='预留内存配置'),
    Column('serial_number', String(256), comment='序列号'),
    Column('patch_version', String(256), comment='最新补丁版本号'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False),
    Column('defense_status', Integer, default=0, comment='ddos防御状态，写入1为开，0为关'),
    Column('iommu_status',String(36), comment='iommu状态')
)


# 定义 domain 表结构
domain = Table(
    'domain', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('pool_id', String(36),  nullable=False, comment='(外键)主机池'),
    Column('cluster_id', String(36), comment='(外键)集群'),
    Column('host_id', String(36),  nullable=False, comment='(外键)主机'),
    Column('name', String(256), nullable=True, comment='名称'),
    Column('os_type', Integer, nullable=False, comment='系统类型 0.其他 1.linux 2.windows'),
    Column('os_version', String(100), comment='系统版本'),
    Column('remark', String(512), comment='备注'),
    Column('status', String(36), nullable=False, comment='虚拟机状态.0.异常状态 1.运行 2.关闭 3.暂停'),
    Column('is_ha', Integer, nullable=False, default=0, comment='是否启用HA 0.否 1.是'),
    Column('auto_migrate', Integer, nullable=False, default=0, comment='自动迁移 0.否 1.是'),
    Column('is_losing_contact', Integer, nullable=False, comment='是否失联 0.否 1.是(HA在连续的一段时间内没有报告虚拟机的状态)'),
    Column('domainname', String(256), nullable=False, comment='虚拟机名称. dumpxml中的name (新建时将表id字段写入)'),
    Column('uuid', String(36), nullable=False, comment='虚拟机UUID. dumpxml中的uuid (新建时将表id字段写入)'),
    Column('memory', BigInteger, nullable=False, comment='虚拟机内存(单位: byte).采用向上舍入ceil规则. dumpxml中的memory + memory/@unit'),
    Column('hmemory', String(30), comment='虚拟机内存(human-readable)'),
    Column('vcpu', Integer, nullable=False, comment='虚拟机vcpu数量. dumpxml中的vcpu/@current或vcpu值(vcpu/@current和vcpu相同时,vcpu/@current会被省略)'),
    Column('cpu_arch', String(30), nullable=False, comment='虚拟机cpu架构. dumpxml中的os/type/@arch'),
    Column('vnc_port', Integer, comment='vnc控制台端口(null为无vnc控制台) (dumpxml中的devices/graphics/@type为vnc时的devices/graphics/@port值)'),
    Column('spice_port', Integer, comment='spice控制台端口(null为无spice控制台) (dumpxml中的devices/graphics/@type为spice时的devices/graphics/@port值)'),
    Column('bind_ip_list', String(512), comment='绑定的IP列表. (本字段冗余domain_interface表的ip字段)格式: ip,ip,ip (用逗号分隔). 用于列表显示和通过IP快速查找'),
    Column('created_at', DateTime, comment='虚拟机创建时间'),
    Column('operation_time', DateTime, comment='虚拟机最后修改时间'),
    Column('is_persistence', Integer, nullable=False, default=1, comment='是否持久化'),
    Column('secret_id', String(36), comment='密钥ID'),
    Column('secret_alg', String(30), comment='密钥算法'),
    Column('domain_recycle_id', String(36), comment='(外键)虚拟机回收'),
    Column('recycle_or_restore_time', DateTime, comment='虚拟机回收或还原时间'),
    Column('safe_status', Integer, nullable=False, default=0, comment='安全状态 0.安全 1.磁盘文件被篡改'),
    Column('is_hide', Integer, nullable=False, default=0, comment='是否隐藏 0.否 1.是'),
    Column('defense_status', Integer, default=0, comment='安全防护状态 0关闭 1开启（默认0）'),
    Column('is_ignore', Integer, default=0, comment='是否忽略 0否 1是（仅针对资源优化功能）'),
    Column('port_id', String(36), comment='(外键)端口')
)

domain_disk = Table(
    'domain_disk', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('domain_id', String(36), nullable=False, comment='(外键)虚拟机'),
    Column('host_id', String(36),  nullable=False,  comment='(外键)主机'),
    Column('storage_pool_id', String(36), nullable=True, comment='存储池id'),
    Column('storage_pool_type', Integer, nullable=True, comment='存储池的使用类型: 1.FC 2.ISCSI 3.NFS 4.RBD（CEPH） 5.DIR(本地目录) 6.fs(共享存储池)'),
    Column('storage_vol_id', String(36),  nullable=True, comment='(外键)存储卷ID'),
    Column('file_name', String(255), nullable=True, comment='文件名称'),
    Column('path', String(512), nullable=True, comment='文件路径'),
    Column('type_code', String(30), nullable=False, comment='类型(dumpxml中的devices/disk/@type). 可选 file, block. 其他还有 dir, network, volume ...'),
    Column('device', String(30), nullable=False, comment='设备(dumpxml中的devices/disk/@device)(没有该属性时,默认为disk). 可选 disk, cdrom, floppy. 其他还有  lun ...'),
    Column('dev', String(30), nullable=False, comment='设备名(dumpxml中的devices/disk/target/@dev)(必须符合Linux磁盘命名规则,配合bus字段命名).virtio->vda, ide->hda, scsi/sata/usb->sda, fdc->fda'),
    Column('bus', String(30), nullable=False, comment='总线(dumpxml中的devices/disk/target/@bus). 可选 virtio, ide, scsi, sata, usb, fdc'),
    Column('qemu_type', String(30), nullable=False, comment='qemu设备类型(dumpxml中的devices/disk/driver/@type)(type=block时一定是raw). 可选 qcow2, raw. 其他还有 qcow, bochs, cloop, cow, iso, qed, vmdk, vpc ...'),
    Column('boot_order', Integer, nullable=False, comment='系统引导顺序(dumpxml中的devices/disk/boot/@order). (boot元素缺失时写 int.MaxValue )'),
    Column('is_persistence', Integer, nullable=False, default=1, comment='是否持久化'),
    Column('shareable', Integer, nullable=False, default=0, comment='是否共享'),
    Column('by_path_id', String(64), nullable=True, comment='/dev/disk/by-path下的id'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False)
)

domain_interface = Table(
    'domain_interface', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('domain_id', String(36),  nullable=False, comment='(外键)虚拟机'),
    Column('type_code', String(30), nullable=False, comment='类型(dumpxml中的devices/interface/@type). 固定 bridge. 其他还有 network, direct, user, ethernet, hostdev, mcast, server, udp, vhostuser ...'),
    Column('bridge', String(30), nullable=True, comment='虚拟交换机名称(桥接到哪里)(dumpxml中的devices/interface/source/@bridge)(openswitch创建的虚拟交换机名称). type!=bridge时,拿不到就写空字符串'),
    Column('mac', String(30), nullable=False, comment='MAC地址(dumpxml中的devices/interface/mac/@address). 用户填写或交由libvirt自动生成'),
    Column('ip', String(1024), nullable=True, comment='绑定的ip地址(dumpxml中的devices/interface/ip/@address). 用户填写'),
    Column('model', String(30), nullable=True, comment='网络类型(dumpxml中的devices/interface/model/@type). 用户填写'),
    Column('driver', String(30), nullable=True, comment='驱动(dumpxml中的devices/interface/driverl/@type)(vhost时叫内核加速). vhost 或其他, 读不到就写null'),
    Column('network_strategy', String(256), nullable=True, comment='网络策略名称'),
    Column('vf_name', String(32), nullable=True, comment='VF网卡'),
    Column('vf_address', String(32), nullable=True, comment='设备编号'),
    Column('vlan_id', String(1024), nullable=True, comment='vlan_id'),
    Column('mtu', String(6), nullable=True, comment='mtu')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    pool.create()
    host.create()
    cluster.create()
    domain.create()
    domain_disk.create()
    domain_interface.create()



def downgrade(migrate_engine):
    meta.bind = migrate_engine
    pool.drop()
    host.drop()
    cluster.drop()
    domain.drop()
    domain_disk.drop()
    domain_interface.drop()


