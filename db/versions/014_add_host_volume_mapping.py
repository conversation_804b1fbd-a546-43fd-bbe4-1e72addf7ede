from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, Boolean, Text

meta = MetaData()
storage_volume = Table(
    'storage_volume', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('storage_pool_id', String(36), ForeignKey('storage_pool.id'),  nullable=False, comment='存储池id'),
    Column('name', String(255), nullable=False, comment='存储卷名称(区分大小写)'),
     Column('protocol_type', String(32), nullable=False, comment='FC, iSCSI, NFS等'),
    Column('volume_type', Unicode(16), nullable=True, comment='存储卷格式:存储卷类型:0.其他(libvirt剩余的) 1.qcow2 2.raw 3.iso, 4.vmdk, 5.vdi, 6.vhd, 7.qed, 8.dmg, 9.ova, 10.img'),
    Column('join_type', Unicode(16), nullable=True, comment='加入平台的方式:1.同步加入,2平台添加,3模板磁盘/xml'),
    Column('path', String(512), nullable=True, comment='存储卷路径'),
    Column('encrypt', Integer, nullable=True, comment='是否加密:0否 1 是'),
    Column('status', Integer, nullable=True, comment='状态:1.正常 2.异常'),
    Column('capacity', BigInteger, nullable=True, comment='存储卷总容量'),
    Column('allocation', BigInteger, nullable=True, comment='存储卷已用容量'),
    Column('preallocation', Unicode(16), nullable=True, comment='存储卷置备类型:1.off(精简置备) 2.falloc(厚置备延迟置零) 3.full(厚置备置零)'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(512), comment='简要备注')
)

client_host = Table(
    'client_host', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='主机ID'),
    Column('hostname', String(100), nullable=False, comment='主机名'),
    Column('ip_address', String(50), nullable=True, comment='IP地址'),
    Column('os_type', String(50), nullable=True, comment='操作系统类型'),
    Column('fc_wwn', Text, nullable=True, comment='光纤WWN，可以有多个WWN，以逗号分隔'),
    Column('iscsi_iqn', String(255), nullable=True, comment='iSCSI IQN'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间')
)

host_volume_mapping = Table(
    'host_volume_mapping', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='映射ID'),
    Column('host_id', String(36), ForeignKey('client_host.id'), nullable=False, comment='主机ID'),
    Column('volume_id', String(36), ForeignKey('storage_volume.id'), nullable=False, comment='存储卷ID'),
    Column('lun_id', Integer, nullable=True, comment='LUN ID号'),
    Column('read_only', Boolean, default=False, nullable=True, comment='是否只读'),
    Column('status', String(50), nullable=True, comment='状态'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    client_host.create()
    host_volume_mapping.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    host_volume_mapping.drop()
    client_host.drop()
