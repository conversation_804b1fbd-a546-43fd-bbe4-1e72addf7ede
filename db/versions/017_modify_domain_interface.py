"""add switch_port_id to domain_interface

Revision ID: 017_modify_domain_interface
Revises: 上一个版本号
Create Date: 2025-06-26

"""
from sqlalchemy import *
from migrate import *

meta = MetaData()

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    domain_interface = Table('domain_interface', meta, autoload=True)

    # 新增字段（如果不存在才加）
    if 'switch_port_id' not in domain_interface.c:
        switch_port_id_col = Column('switch_port_id', String(36), nullable=True)
        switch_port_id_col.create(domain_interface)

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    domain_interface = Table('domain_interface', meta, autoload=True)

    # 删除字段（如果存在才删）
    if 'switch_port_id' in domain_interface.c:
        domain_interface.c.switch_port_id.drop()