from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, create_engine 
from sqlalchemy import Table, Column, String, Integer, BigInteger, DateTime, MetaData, ForeignKey, create_engine



meta = MetaData()


storage_device = Table(
    'storage_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('device_type', String(64), nullable=False, comment='存储的类型: 1 FC_SAN， 2 IP_SAN，3 NFS，4 RBD（CEPH） 5.本地目录'),
    Column('device_name', String(100), nullable=False, comment='存储设备名称(区分大小写)'),
    Column('model', String(100), nullable=False, comment='型号'),
    Column('vendor', String(100), nullable=False, comment='厂家'),
    Column('total_capacity', BigInteger, nullable=True, comment='存储总容量,单位:字节(byte)'),
    Column('used_capacity', BigInteger, nullable=True, comment='已使用存储总容量,单位:字节(byte)'),
    Column('ip_mgmt', String(255), nullable=True, comment='管理ip集合(ip间用逗号隔开),如*************,*************'),
    Column('last_scn_time', DateTime(timezone=True), nullable=True, comment='最后扫描的时间'),
    Column('username', String(100), nullable=True, comment='用户名'),
    Column('password', String(255), nullable=True, comment='密码(应加密存储)'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)




storage_pool = Table(
    'storage_pool', meta,
    Column('id',String(36), primary_key=True,  unique=True, nullable=False, comment='主键'),
    Column('name', String(100), nullable=False, comment='存储池名称'),
    Column('storage_device_id', String(36), ForeignKey('storage_device.id'), nullable=False, comment='存储设备的ID'),
    Column('storage_local_dir', String(100), nullable=True, comment='本地存储目录'),
    Column('type_code', String(100), nullable=True, default="local", comment='存储类型代码'),
    Column('type_code_display', String(100), nullable=True, default="本地存储", comment='存储类型显示名称'),
    Column('is_nova_libvirt_docker', String(16), nullable=True, default="no", comment='libvirt是否docker'),
    Column('outside_prefix', String(100), nullable=True, comment='外部前缀'),
    Column('inside_prefix', String(100), nullable=True, comment='内部前缀'),
    Column('use_type', Integer, nullable=True, default=0, comment='使用类型'),
    Column('status', Integer, nullable=True, default=0, comment='状态'),
    Column('capacity', BigInteger, nullable=True, default=0, comment='总容量，单位:字节'),
    Column('available', BigInteger, nullable=True, default=0, comment='可用容量，单位:字节'),
    Column('allocation', BigInteger, nullable=True, default=0, comment='已分配容量，单位:字节'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(512), nullable=True, comment='备注')
)

storage_volume = Table(
    'storage_volume', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('storage_pool_id', String(36), ForeignKey('storage_pool.id'),  nullable=False, comment='存储池id'),
    Column('name', String(255), nullable=False, comment='存储卷名称(区分大小写)'),
    Column('protocol_type', String(32), nullable=False, comment='FC, iSCSI, NFS等'),
    # Column('type_code', String(100), nullable=True, comment='存储卷类型代码'),
    Column('volume_type', Unicode(16), nullable=True, comment='存储卷格式:存储卷类型:0.其他(libvirt剩余的) 1.qcow2 2.raw 3.iso, 4.vmdk, 5.vdi, 6.vhd, 7.qed, 8.dmg, 9.ova, 10.img'),
    Column('join_type', Unicode(16), nullable=True, comment='加入平台的方式:1.同步加入,2平台添加,3模板磁盘/xml'),
    Column('path', String(512), nullable=True, comment='存储卷路径'),
    Column('encrypt', Integer, nullable=True, comment='是否加密:0否 1 是'),
    Column('status', Integer, nullable=True, comment='状态:1.正常 2.异常'),
    Column('capacity', BigInteger, nullable=True, comment='存储卷总容量'),
    Column('allocation', BigInteger, nullable=True, comment='存储卷已用容量'),
    Column('preallocation', Unicode(16), nullable=True, comment='存储卷置备类型:1.off(精简置备) 2.falloc(厚置备延迟置零) 3.full(厚置备置零)'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(512), comment='简要备注')
)





def upgrade(migrate_engine):
    meta.bind = migrate_engine
    storage_device.create()
    storage_pool.create()
    storage_volume.create()



def downgrade(migrate_engine):
    meta.bind = migrate_engine
    storage_volume.drop()
    storage_pool.drop()
    storage_device.drop()



