from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, Boolean, BigInteger, Float, create_engine

meta = MetaData()
storage_device = Table(
    'storage_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('device_type', String(64), nullable=False, comment='存储的类型: 1 FC， 2 ISCSI，3 NFS，4 RBD（CEPH） 5.本地目录'),
    Column('device_name', String(100), nullable=False, comment='存储设备名称(区分大小写)'),
    Column('model', String(100), nullable=False, comment='型号'),
    Column('vendor', String(100), nullable=False, comment='厂家'),
    Column('total_capacity', BigInteger, nullable=True, comment='存储总容量,单位:字节(byte)'),
    Column('used_capacity', BigInteger, nullable=True, comment='已使用存储总容量,单位:字节(byte)'),
    Column('ip_mgmt', String(255), nullable=True, comment='管理ip集合(ip间用逗号隔开),如*************,*************'),
    Column('last_scn_time', DateTime(timezone=True), nullable=True, comment='最后扫描的时间'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)

storage_pool = Table(
    'storage_pool', meta,
    Column('id',String(36), primary_key=True,  unique=True, nullable=False, comment='主键'),
    Column('name', String(100), nullable=False, comment='存储池名称'),
    Column('storage_device_id', String(36), ForeignKey('storage_device.id'), nullable=False, comment='存储设备的ID'),
    Column('storage_local_dir', String(100), nullable=True, comment='本地存储目录'),
    Column('type_code', String(100), nullable=True, default="local", comment='存储类型代码'),
    Column('type_code_display', String(100), nullable=True, default="本地存储", comment='存储类型显示名称'),
    Column('is_nova_libvirt_docker', String(16), nullable=True, default="no", comment='libvirt是否docker'),
    Column('outside_prefix', String(100), nullable=True, comment='外部前缀'),
    Column('inside_prefix', String(100), nullable=True, comment='内部前缀'),
    Column('use_type', Integer, nullable=True, default=0, comment='使用类型'),
    Column('status', Integer, nullable=True, default=0, comment='状态'),
    Column('capacity', BigInteger, nullable=True, default=0, comment='总容量，单位:字节'),
    Column('available', BigInteger, nullable=True, default=0, comment='可用容量，单位:字节'),
    Column('allocation', BigInteger, nullable=True, default=0, comment='已分配容量，单位:字节'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(512), nullable=True, comment='备注')
)



ceph_cluster = Table(
    'ceph_cluster', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='Ceph集群主键'),
    Column('device_id', String(36), ForeignKey('storage_device.id'), nullable=False, unique=True, comment='存储设备ID'),
    Column('cluster_name', String(100), nullable=False, comment='Ceph集群名称'),
    Column('mon_count', Integer, nullable=True, comment='Monitor节点数'),
    Column('osd_count', Integer, nullable=True, comment='OSD节点数'),
    Column('mgr_count', Integer, nullable=True, comment='Manager节点数'),
    Column('crush_rule', String(50), nullable=True, comment='CRUSH规则'),
    Column('pg_count', Integer, nullable=True, comment='Placement Group数量'),
    Column('cluster_version', String(50), nullable=True, comment='Ceph集群版本'),
    Column('health_status', String(50), nullable=True, comment='集群健康状态'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

ceph_pool = Table(
    'ceph_pool', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='Ceph池主键'),
    Column('pool_id', String(36), ForeignKey('storage_pool.id'), nullable=False, unique=True, comment='存储池ID'),
    Column('ceph_id', String(36), ForeignKey('ceph_cluster.id'), nullable=False, comment='Ceph集群ID'),
    Column('pool_type', String(20), nullable=False, comment='池类型: replicated, erasure'),
    Column('replica_count', Integer, default=3, nullable=True, comment='副本数量(对复制池)'),
    Column('ec_profile', String(100), nullable=True, comment='纠删码配置(对EC池)'),
    Column('pg_count', Integer, nullable=True, comment='池的PG数量'),
    Column('application_type', String(50), nullable=True, comment='RBD, CephFS, RadosGW'),
    Column('compression_enabled', Boolean, default=False, nullable=True, comment='是否启用压缩'),
    Column('compression_algorithm', String(50), nullable=True, comment='压缩算法'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

ceph_osd = Table(
    'ceph_osd', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='OSD主键'),
    Column('ceph_id', String(36), ForeignKey('ceph_cluster.id'), nullable=False, comment='Ceph集群ID'),
    Column('osd_number', Integer, nullable=False, comment='OSD编号'),
    Column('host_server', String(100), nullable=True, comment='主机服务器'),
    Column('device_path', String(255), nullable=True, comment='设备路径'),
    Column('weight', Float, nullable=True, comment='权重'),
    Column('status', String(50), nullable=True, comment='状态'),
    Column('class', String(50), nullable=True, comment='ssd, hdd, nvme等'),
    Column('up', Boolean, default=True, nullable=True, comment='是否UP状态'),
    Column('in', Boolean, default=True, nullable=True, comment='是否IN状态'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    ceph_cluster.create()
    ceph_pool.create()
    ceph_osd.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    ceph_osd.drop()
    ceph_pool.drop()
    ceph_cluster.drop()
