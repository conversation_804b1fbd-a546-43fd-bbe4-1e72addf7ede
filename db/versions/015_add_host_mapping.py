from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, create_engine

meta = MetaData()

# 定义 host 表结构
host = Table(
    'host', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键')

)

storage_device = Table(
    'storage_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键')
)




storage_pool = Table(
    'storage_pool', meta,
    Column('id',String(36), primary_key=True,  unique=True, nullable=False, comment='主键')
)

# host和storage_device的多对多映射表
host_storage_device_mapping = Table(
    'host_storage_device_mapping', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('host_id', String(36), ForeignKey('host.id'), nullable=False, comment='主机ID'),
    Column('storage_device_id', String(36), Foreign<PERSON>ey('storage_device.id'), nullable=False, comment='存储设备ID'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)

# host和storage_pool的多对多映射表
host_storage_pool_mapping = Table(
    'host_storage_pool_mapping', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('host_id', String(36), ForeignKey('host.id'), nullable=False, comment='主机ID'),
    Column('storage_pool_id', String(36), ForeignKey('storage_pool.id'), nullable=False, comment='存储池ID'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    host_storage_device_mapping.create()
    host_storage_pool_mapping.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    host_storage_device_mapping.drop()
    host_storage_pool_mapping.drop()
