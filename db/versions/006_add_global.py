from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()



global_config = Table(
    'global_config', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('key', String(64)),
    <PERSON>umn('value', String(255)),
    <PERSON>umn('config_type', String(64)),
    <PERSON>umn('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    global_config.create()

    
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    global_config.drop()

