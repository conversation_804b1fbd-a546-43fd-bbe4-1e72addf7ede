from migrate import *
import datetime
from sqlalchemy import (
    Table, Column, String, Integer, MetaData, DateTime, 
    UniqueConstraint, ForeignKeyConstraint
)

meta = MetaData()

user_host_assignments = Table(
    'user_host_assignments', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('user_id', String(36), nullable=False, comment='用户ID'),
    <PERSON>umn('host_id', String(36), nullable=False, comment='主机ID'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间'),
    UniqueConstraint('user_id', 'host_id', name='uniq_user_host'),
    ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    ForeignKeyConstraint(['host_id'], ['host.id'], ondelete='CASCADE'),
)

user_cluster_assignments = Table(
    'user_cluster_assignments', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('user_id', String(36), nullable=False, comment='用户ID'),
    Column('cluster_id', String(36), nullable=False, comment='集群ID'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间'),
    UniqueConstraint('user_id', 'cluster_id', name='uniq_user_cluster'),
    ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    ForeignKeyConstraint(['cluster_id'], ['cluster.id'], ondelete='CASCADE'),
)

user_vm_assignments = Table(
    'user_vm_assignments', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('user_id', String(36), nullable=False, comment='用户ID'),
    Column('vm_id', String(36), nullable=False, comment='虚拟机ID'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间'),
    UniqueConstraint('user_id', 'vm_id', name='uniq_user_vm'),
    ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    ForeignKeyConstraint(['vm_id'], ['domain.id'], ondelete='CASCADE'),
)

user_storage_pools = Table(
    'user_storage_pools', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('user_id', String(36), nullable=False, comment='用户ID'),
    Column('pool_id', String(36), nullable=False, comment='存储池ID'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间'),
    UniqueConstraint('user_id', 'pool_id', name='uniq_user_pool'),
    ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    ForeignKeyConstraint(['pool_id'], ['storage_pool.id'], ondelete='CASCADE'),
)

user_quotas = Table(
    'user_quotas', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('user_id', String(36), nullable=False, unique=True, comment='用户ID'),
    Column('cpu_limit', Integer, nullable=False, comment='CPU核心数上限'),
    Column('cpu_used', Integer, default=0, comment='已使用的CPU核心数'),
    Column('memory_limit', Integer, nullable=False, comment='内存上限(MB)'),
    Column('memory_used', Integer, default=0, comment='已使用的内存(MB)'),
    Column('storage_limit', Integer, nullable=False, comment='存储上限(GB)'),
    Column('storage_used', Integer, default=0, comment='已使用的存储(GB)'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False),
    ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    Table('users', meta, autoload=True)
    Table('host', meta, autoload=True)
    Table('cluster', meta, autoload=True)
    Table('domain', meta, autoload=True)
    Table('storage_pool', meta, autoload=True)
    user_host_assignments.create()
    user_cluster_assignments.create()
    user_vm_assignments.create()
    user_storage_pools.create()
    user_quotas.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    Table('users', meta, autoload=True)
    Table('host', meta, autoload=True)
    Table('cluster', meta, autoload=True)
    Table('domain', meta, autoload=True)
    Table('storage_pool', meta, autoload=True)
    user_quotas.drop()
    user_storage_pools.drop()
    user_vm_assignments.drop()
    user_cluster_assignments.drop()
    user_host_assignments.drop()
