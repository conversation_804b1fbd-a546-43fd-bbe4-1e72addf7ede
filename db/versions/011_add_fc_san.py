from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, Boolean, create_engine

meta = MetaData()
storage_device = Table(
    'storage_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('device_type', String(64), nullable=False, comment='存储的类型: 1 FC， 2 ISCSI，3 NFS，4 RBD（CEPH） 5.本地目录'),
    <PERSON>umn('device_name', String(100), nullable=False, comment='存储设备名称(区分大小写)'),
    Column('model', String(100), nullable=False, comment='型号'),
    Column('vendor', String(100), nullable=False, comment='厂家'),
    Column('total_capacity', BigInteger, nullable=True, comment='存储总容量,单位:字节(byte)'),
    Column('used_capacity', BigInteger, nullable=True, comment='已使用存储总容量,单位:字节(byte)'),
    Column('ip_mgmt', String(255), nullable=True, comment='管理ip集合(ip间用逗号隔开),如*************,*************'),
    Column('last_scn_time', DateTime(timezone=True), nullable=True, comment='最后扫描的时间'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)



fc_san_device = Table(
    'fc_san_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='FC SAN 设备主键'),
    Column('device_id', String(36), ForeignKey('storage_device.id'), nullable=False, unique=True, comment='存储设备ID'),
    Column('wwn', String(50), nullable=False, comment='全球唯一名称'),
    Column('fc_switch_count', Integer, default=0, nullable=True, comment='FC交换机数量'),
    Column('zone_count', Integer, default=0, nullable=True, comment='分区数量'),
    Column('max_speed', String(20), nullable=True, comment='例如 "16Gbps"'),
    Column('multipathing_type', String(50), nullable=True, comment='多径类型'),
    Column('initiator_count', Integer, default=0, nullable=True, comment='发起端数量'),
    Column('target_count', Integer, default=0, nullable=True, comment='目标端数量'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

fc_san_port = Table(
    'fc_san_port', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='端口主键'),
    Column('fc_device_id', String(36), ForeignKey('fc_san_device.id'), nullable=False, comment='FC SAN 设备ID'),
    Column('port_wwn', String(50), nullable=False, comment='端口WWN'),
    Column('port_type', String(16), nullable=False, comment='端口类型: initiator, target'),
    Column('port_speed', String(20), nullable=True, comment='端口速度'),
    Column('connected_to', String(100), nullable=True, comment='连接的交换机或设备'),
    Column('status', String(16), nullable=False, comment='状态: online, offline, error'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    fc_san_device.create()
    fc_san_port.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    fc_san_port.drop()
    fc_san_device.drop()
