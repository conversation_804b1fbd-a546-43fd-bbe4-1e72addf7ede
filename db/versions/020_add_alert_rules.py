from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, Text

meta = MetaData()


alert_rules = Table(
    'alert_rules', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    <PERSON>umn('name', String(255), nullable=False),
    <PERSON>umn('expr', String(512), nullable=False),
    <PERSON>umn('expr_code', String(32), nullable=False),
    <PERSON>umn('value', Integer, nullable=False),
    <PERSON>umn('unit', String(16)),
    <PERSON>umn('for_interval', String(20), nullable=False, default='1m'),
    <PERSON>umn('report_interval', String(20), nullable=False, default='1m'),
    <PERSON>umn('critical_value', Integer),
    <PERSON>umn('major_value', Integer),
    <PERSON>umn('warning_value', Integer),
    <PERSON>umn('info_value', Integer),
    <PERSON>umn('description', String(255)),
    Column('summary', String(255)),
    Column('job', String(255)),
    <PERSON>umn('alert_type', String(64)),
    <PERSON>umn('created_at', DateTime(timezone=True), server_default=func.now()),
    Column('updated_at', DateTime(timezone=True), server_default=func.now(), onupdate=func.now()),
    Column('is_default', Integer),
    Column('status', String(50)),
    Column('order_id', Integer, nullable=False, default=0),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    alert_rules.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    alert_rules.drop()
