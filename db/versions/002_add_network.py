from sqlalchemy import *
from migrate import *

import uuid
import datetime
from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, create_engine
from sqlalchemy import Table, Column, String, Integer, BigInteger, DateTime, MetaData, ForeignKey, create_engine, text
import enum

meta = MetaData()


# Define enums if not already defined
class DHCPRangeType(enum.Enum):
    RANGE = 'range'
    LIST = 'list'
    EXCLUDE = 'exclude'


# 定义网络
networks = Table(
    'networks', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('name', String(30), nullable=False, unique=True, comment='名称'),
    Column('status', String(36), comment="网络状态"),
    Column('network_type', String(36), comment="网络类型：flat, vlan, vxlan"),
    Column('physical_network', String(36), comment="物理网络"),
    Column('remark', String(512), comment='备注'),
)

# 定义子网络
subnets = Table(
    'subnets', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('network_id', String(36), nullable=False, unique=False, comment='网络ID'),
    Column('cidr', String(64), nullable=False, unique=False, comment='cidr'),
    Column('gateway_ip', String(64), nullable=False, unique=False, comment='网关'),
    Column('enable_dhcp', Boolean, default=True, nullable=False, comment="是否启用dhcp"),
    Column('allocation_pools', String(36), comment="动态IP分配池，DHCP使用"),
    Column('dns_nameservers', String(36), comment="DNS服务器列表"),
    Column('host_routes', String(36), comment="路由表"),
    Column('ipv6_mode', String(36), comment="IPv6配置模式：slaac, dhcp6, none"),
    Column('status', String(36), comment="子网状态"),
    Column('vlan_id', Integer, nullable=True, comment="VLAN ID, 可选字段，仅当network_type是VLAN时使用"),
    Column('remark', String(512), comment='备注'),
)

# 定义端口
ports = Table(
    'ports', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('subnet_id', String(36), nullable=False, unique=False, comment='网络ID'),
    Column('mac_address', String(64), nullable=False, unique=False, comment='Mac地址'),
    Column('ip_address', String(64), nullable=False, unique=False, comment='IP地址'),
    Column('device_id', String(64), nullable=False, unique=False, comment='设备ID'),
    Column('device_type', String(64), nullable=False, unique=False, comment='设备类型'),
    Column('status', String(36), comment="端口状态"),
    Column('role', String(36), nullable=False, comment="端口角色：vm 或 bridge"),
    Column('switch_port_id', String(36), ForeignKey("switchs_ports.id")),
    Column('port_group_id', String(36), ForeignKey('port_groups.id'), comment="与端口关联的安全组"),
    Column('remark', String(512), comment='备注'),
)

# Define the dhcp_ranges table
dhcp_ranges = Table(
    'dhcp_ranges', meta,
    Column('id', String(36), primary_key=True, default=uuid.uuid4, nullable=False, comment='主键'),
    Column('subnet_id', String(36), ForeignKey('subnets.id'), nullable=False),
    Column('type', Enum(DHCPRangeType), nullable=False, comment="DHCP范围类型：range, list, exclude"),
    Column('ranges', String(512), comment="IP地址范围列表"),
    Column('addresses', String(512), comment="离散IP地址列表")
)

# Define the port_pairs table
port_pairs = Table(
    'port_pairs', meta,
    Column('id', String(36), primary_key=True, default=uuid.uuid4, nullable=False, comment='主键'),
    Column('vm_port_id', String(36), primary_key=True, default=uuid.uuid4, nullable=False, comment="虚拟机端口ID"),
    Column('bridge_port_id', String(36), primary_key=True, default=uuid.uuid4, nullable=False, comment="网桥端口ID"),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now),
    Column('updated_at', DateTime(timezone=True), onupdate=datetime.datetime.now)
)

# Define the port_groups table
port_groups = Table(
    'port_groups', meta,
    Column('id', String(36), primary_key=True, default=uuid.uuid4, nullable=False, comment='主键'),
    Column('name', String(36), nullable=False, comment="安全组名称"),
    Column('remark', String(512), comment='备注')
)

# Define the port_group_rules table
port_group_rules = Table(
    'port_group_rules', meta,
    Column('id', String(36), primary_key=True, default=uuid.uuid4, nullable=False, comment='主键'),
    Column('port_group_id', String(36), ForeignKey("port_groups.id")),
    Column('direction', String(36), comment="方向"),
    Column('ethertype', String(36), comment="以太网类型：ipv4/ipv6"),
    Column('protocol', String(36), comment="协议：TCP/UDP/ICMP"),
    Column('port_range_min', String(36), comment="最小端口号"),
    Column('port_range_max', String(36), comment="最大端口号"),
    Column('remote_ip_prefix', String(36), nullable=False, comment="远程IP前缀"),
    Column('remark', String(512), comment='备注')
)

# 定义路由表
router_table = Table(
    'router_table', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('router_id', String(36), nullable=False, unique=True, comment='路由ID'),
    Column('next_hop', String(36), nullable=False, unique=True, comment='下一跳'),
    Column('remark', String(512), comment='备注'),
)

# 定义路由
routers = Table(
    'routers', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('cluster_id', String(36), nullable=False),
    Column('name', String(36), nullable=False, unique=True, comment='名称'),
    Column('status', String(36), comment="路由状态"),
    Column('external_gateway_info', String(36), comment="外部网关信息"),
    Column('remark', String(512), comment='备注'),
)

# 定义路由端口
router_port = Table(
    'router_port', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('router_id', String(36), nullable=False, unique=True, comment='路由ID'),
    Column('port_id', String(36), nullable=False, unique=True, comment='端口ID'),
    Column('subnet_id', String(36), nullable=False, unique=True, comment='子网ID'),
    Column('remark', String(512), comment='备注'),
)

switchs = Table(
    'switchs', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='主键', default=str(uuid.uuid4())),
    Column('name', String(36), nullable=False, comment='名称'),
    Column('vswitch_type', String(36), nullable=False, comment='交换机类型'),
    Column('vlan_ids', String(256), nullable=True, comment='VLAN IDs'),
    Column('status', String(36), nullable=True, comment='状态'),
    Column('host_id', String(100), nullable=True, comment='主机id'),
    Column('cluster_id', String(100), nullable=True, comment='集群id'),
    Column('extra_config', String(36), nullable=True, comment='额外配置'),
    Column('mtu', String(36), nullable=True, comment='mtu设置'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
)

switchs_ports = Table(
    'switchs_ports', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True, comment='主键', default=str(uuid.uuid4())),
    Column('switchs_id', String(36), ForeignKey('switchs.id'), nullable=False, comment='交换机ID'),
    Column('name', String(36), nullable=True, comment='端口名称'),
    Column('gate_way', String(36), nullable=True, comment='网关'),
    Column('next_hop', String(36), nullable=True, comment='下一跳'),
    Column('ip', String(36), nullable=True, comment='IP地址'),
    Column('ip_type', String(36), nullable=True, comment='IP类型'),
    Column('netmask', String(36), nullable=True, comment='子网掩码'),
    Column('mode', String(36), nullable=True, comment='模式'),
    Column('pnics', String(36), nullable=True, comment='物理网卡'),
    Column('ports_num', String(36), nullable=True, comment='端口数量'),
    Column('status', String(36), nullable=True, comment='状态'),
    Column('extra_config', String(36), nullable=True, comment='额外配置'),
    Column('port_type', String(36), nullable=True, comment='端口类型'),
    Column('interface', String(36), nullable=True, comment='接口类型'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('switch_port_group_id', String(36), ForeignKey('switch_port_groups.id'), nullable=False, comment='交换机端口组ID'),
)

switch_port_groups = Table(
    'switch_port_groups', meta,
    Column('id', String(36), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键'),
    Column('name', String(36), nullable=False, comment='交换机端口组名称'),
    Column('vlan_id', String(36), comment='vlan_id'),
    Column('remark', String(512), comment='备注'),
    Column('switchs_id', String(36), ForeignKey('switchs.id'), nullable=False, comment='交换机ID'),
)

switch_port_group_rules = Table(
    'switch_port_group_rules', meta,
    Column('id', String(36), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键'),
    Column('switch_port_group_id',  String(36), ForeignKey("switch_port_groups.id"), nullable=False, comment='关联的端口组ID'),
    Column('direction', String(36), comment='方向'),
    Column('ethertype', String(36), comment='以太网类型：ipv4/ipv6'),
    Column('protocol', String(36), comment='协议：TCP/UDP/ICMP'),
    Column('port_range_min', String(36), comment='最小端口号'),
    Column('port_range_max', String(36), comment='最大端口号'),
    Column('remote_ip_prefix', String(36), nullable=False, comment='远程IP前缀'),
    Column('action', String(36), nullable=False, comment='动作：allow, deny,modify'),
    Column('priority', String(36), nullable=False, comment='规则优先级'),
    Column('remark', String(512), comment='备注'),
)

# 设备 可以是vm 容器 祼机 虚路由器 防火 负载均衡器 等
devices = Table(
    'devices', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('name', String(36), nullable=False, unique=True, comment='名称'),
    Column('device_type', String(36), nullable=False, unique=True, comment='设备类型'),
    Column('remark', String(512), comment='备注'),
)

# 设备端口
device_ports = Table(
    'device_ports', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('device_id', String(36), nullable=False, unique=True, comment='设备ID'),
    Column('port_id', String(36), nullable=False, unique=True, comment='端口ID'),
    Column('remark', String(512), comment='备注'),
)

# 定义外部网络
external_network = Table(
    'external_network', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('name', String(30), nullable=False, unique=True, comment='名称'),
    Column('remark', String(512), comment='备注'),
)

# 定义外部网络
external_network_port = Table(
    'external_network_port', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('pool_id', String(36), ForeignKey('pool.id'), nullable=False, comment='(外键)主机池'),
    Column('name', String(30), nullable=False, unique=True, comment='名称'),
    Column('remark', String(512), comment='备注'),
    Column('ha_level', Integer, nullable=False, default=0, comment='ha等级. 0不启用, 1低, 2中(默认), 3高'),
    Column('enabled_drs', Integer, comment='是否启用 DRS'),
    Column('enable_storage_drs', Integer, comment='1：启用，0不启用'),
    Column('enable_dmp', Integer, comment='1：启用，0不启用'),
    Column('cpu_architecture', String(50), comment='cpu架构'),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    networks.create()
    subnets.create()
    router_table.create()
    routers.create()
    router_port.create()
    switchs.create()
    switch_port_groups.create()
    switchs_ports.create()
    devices.create()
    device_ports.create()
    port_pairs.create()
    port_groups.create()
    port_group_rules.create()
    dhcp_ranges.create()
    switch_port_group_rules.create()
    ports.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    networks.drop()
    subnets.drop()
    router_table.drop()
    routers.drop()
    router_port.drop()
    switchs.drop()
    switchs_ports.drop()
    devices.drop()
    device_ports.drop()
    ports.drop()
    port_pairs.drop()
    port_groups.drop()
    port_group_rules.drop()
    dhcp_ranges.drop()
    switch_port_groups.drop()
    switch_port_group_rules.drop()
