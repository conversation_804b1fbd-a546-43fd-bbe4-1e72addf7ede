from sqlalchemy import *
from migrate import *
from sqlalchemy import (
    Table, Column, String, Integer, MetaData, DateTime, ForeignKey, 
    create_engine, Boolean, UniqueConstraint, inspect
)
import datetime
import uuid

meta = MetaData()

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 首先加载已存在的表
    switchs = Table('switchs', meta, autoload=True)
    
    # 1. 给 switchs 表添加 ovs_name 字段
    inspector = inspect(migrate_engine)
    columns = [c['name'] for c in inspector.get_columns('switchs')]
    if 'ovs_name' not in columns:
        try:
            # 先添加列（不带约束）
            ovs_name = Column('ovs_name', String(36), nullable=True, comment='OVS网桥名称')
            ovs_name.create(switchs)
            print("Added ovs_name column to switchs table")
            
            # 复制 name 字段到 ovs_name
            migrate_engine.execute('''
                UPDATE switchs SET ovs_name = name 
                WHERE vswitch_type = 'ovs'
            ''')
            print("Copied existing OVS bridge names to ovs_name field")
            
            # 检查是否有重复的 ovs_name（在同一主机内）
            duplicate_check = migrate_engine.execute('''
                SELECT host_id, ovs_name, COUNT(*) as count 
                FROM switchs 
                WHERE ovs_name IS NOT NULL 
                GROUP BY host_id, ovs_name 
                HAVING count > 1
            ''').fetchall()
            
            if duplicate_check:
                print("Warning: Found duplicate ovs_names within the same host:")
                for host_id, ovs_name, count in duplicate_check:
                    print(f"host_id: {host_id}, ovs_name: {ovs_name} appears {count} times")
                print("Please resolve duplicate ovs_names before continuing.")
                return False
            
            # 添加联合唯一约束
            migrate_engine.execute('''
                ALTER TABLE switchs 
                ADD CONSTRAINT uq_host_ovs_name UNIQUE (host_id, ovs_name)
            ''')
            print("Added composite unique constraint to host_id and ovs_name")
            
        except Exception as e:
            print(f"Error while adding ovs_name column: {str(e)}")
            raise
    
    # 2. 检查并创建 host_switches_mapping 表
    inspector = inspect(migrate_engine)
    if 'host_switches_mapping' not in inspector.get_table_names():
        host_switches_mapping = Table(
            'host_switches_mapping', meta,
            Column('id', String(36), primary_key=True, default=str(uuid.uuid4()), unique=True, nullable=False, comment='主键'),
            Column('host_id', String(36), nullable=False, comment='主机ID'),
            Column('switch_id', String(36), ForeignKey('switchs.id'), nullable=False, comment='OVS网桥ID'),
            Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
            Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
            Column('remark', String(512), comment='备注'),
            UniqueConstraint('host_id', 'switch_id', name='uix_host_switch')
        )
        host_switches_mapping.create()
        print("Created host_switches_mapping table")
    
    # 2. 给 switchs_ports 表添加 is_physical 字段
    switchs_ports = Table('switchs_ports', meta, autoload=True)
    inspector = inspect(migrate_engine)
    columns = [c['name'] for c in inspector.get_columns('switchs_ports')]
    if 'is_physical' not in columns:
        is_physical = Column('is_physical', Boolean, server_default='0', nullable=False, comment='是否是物理网卡绑定')
        is_physical.create(switchs_ports)
        print("Added is_physical column to switchs_ports table")
    
    # 3. 给 switch_port_groups 的 vlan_id 添加唯一约束
    switch_port_groups = Table('switch_port_groups', meta, autoload=True)
    
    # 检查 vlan_id 是否已经有唯一约束
    unique_constraints = [c['name'] for c in inspector.get_unique_constraints('switch_port_groups')]
    columns = [c['name'] for c in inspector.get_columns('switch_port_groups')]
    
    if not any('vlan_id' in c['column_names'] for c in inspector.get_unique_constraints('switch_port_groups')):
        try:
            # 1. 清理可能存在的临时列
            if 'temp_vlan_id' in columns:
                print("Found existing temporary column, cleaning up...")
                migrate_engine.execute('ALTER TABLE switch_port_groups DROP COLUMN temp_vlan_id')
                print("Cleaned up temporary column")
            
            # 2. 检查是否有重复的 vlan_id
            duplicate_check = migrate_engine.execute('''
                SELECT vlan_id, COUNT(*) as count 
                FROM switch_port_groups 
                WHERE vlan_id IS NOT NULL 
                GROUP BY vlan_id 
                HAVING count > 1
            ''').fetchall()
            
            if duplicate_check:
                print("Warning: Found duplicate vlan_ids:")
                for vlan_id, count in duplicate_check:
                    print(f"vlan_id {vlan_id} appears {count} times")
                print("Please resolve duplicate vlan_ids before continuing.")
                return False
            
            # 3. 创建临时列（不带约束）
            temp_vlan_id = Column('temp_vlan_id', String(36), comment='vlan_id')
            temp_vlan_id.create(switch_port_groups)
            print("Created temporary column for vlan_id")
            
            # 4. 复制数据
            migrate_engine.execute(
                'UPDATE switch_port_groups SET temp_vlan_id = vlan_id'
            )
            print("Copied data to temporary column")
            
            # 5. 删除旧列
            switch_port_groups.c.vlan_id.drop()
            print("Dropped old vlan_id column")
            
            # 6. 重命名临时列
            migrate_engine.execute('''
                ALTER TABLE switch_port_groups 
                CHANGE temp_vlan_id vlan_id VARCHAR(36)
            ''')
            print("Renamed temporary column to vlan_id")
            
            # 7. 添加唯一约束（如果没有重复值）
            migrate_engine.execute('''
                ALTER TABLE switch_port_groups 
                ADD CONSTRAINT uix_vlan_id UNIQUE (vlan_id)
            ''')
            print("Added unique constraint to vlan_id")
            
        except Exception as e:
            print(f"Error while modifying vlan_id: {str(e)}")
            
            # 清理：如果临时列存在，删除它
            try:
                if 'temp_vlan_id' in [c['name'] for c in inspector.get_columns('switch_port_groups')]:
                    migrate_engine.execute('ALTER TABLE switch_port_groups DROP COLUMN temp_vlan_id')
                    print("Cleaned up temporary column after error")
            except Exception as cleanup_error:
                print(f"Error during cleanup: {str(cleanup_error)}")
            raise
            
        except Exception as e:
            print(f"Error while modifying vlan_id: {str(e)}")
            
            # 清理：如果临时列存在，删除它
            try:
                if 'temp_vlan_id' in [c['name'] for c in inspector.get_columns('switch_port_groups')]:
                    migrate_engine.execute('ALTER TABLE switch_port_groups DROP COLUMN temp_vlan_id')
            except Exception as cleanup_error:
                print(f"Error during cleanup: {str(cleanup_error)}")
            raise

def downgrade(migrate_engine):
    try:
        meta.bind = migrate_engine
        inspector = inspect(migrate_engine)
        
        # 1. 删除 host_switches_mapping 表
        if 'host_switches_mapping' in inspector.get_table_names():
            host_switches_mapping = Table('host_switches_mapping', meta, autoload=True)
            host_switches_mapping.drop()
            print("Dropped host_switches_mapping table")
        
        # 2. 删除 switchs 表的 ovs_name 字段
        if 'ovs_name' in [c['name'] for c in inspector.get_columns('switchs')]:
            switchs = Table('switchs', meta, autoload=True)
            switchs.c.ovs_name.drop()
            print("Dropped ovs_name column from switchs table")
        
        # 3. 删除 switchs_ports 表的 is_physical 字段
        if 'is_physical' in [c['name'] for c in inspector.get_columns('switchs_ports')]:
            switchs_ports = Table('switchs_ports', meta, autoload=True)
            switchs_ports.c.is_physical.drop()
            print("Dropped is_physical column from switchs_ports")
        
        # 4. 恢复 switch_port_groups 的 vlan_id（移除唯一约束）
        if any('vlan_id' in c['column_names'] for c in inspector.get_unique_constraints('switch_port_groups')):
            try:
                switch_port_groups = Table('switch_port_groups', meta, autoload=True)
                
                # 创建临时列
                temp_vlan_id = Column('temp_vlan_id', String(36), comment='vlan_id')
                temp_vlan_id.create(switch_port_groups)
                print("Created temporary column")
                
                # 复制数据
                migrate_engine.execute(
                    'UPDATE switch_port_groups SET temp_vlan_id = vlan_id'
                )
                print("Copied data to temporary column")
                
                # 删除带唯一约束的列
                migrate_engine.execute('ALTER TABLE switch_port_groups DROP COLUMN vlan_id')
                print("Dropped column with unique constraint")
                
                # 重命名临时列为 vlan_id
                migrate_engine.execute(
                    'ALTER TABLE switch_port_groups CHANGE temp_vlan_id vlan_id VARCHAR(36)'
                )
                print("Renamed temporary column to vlan_id")
                
            except Exception as e:
                print(f"Error while removing unique constraint from vlan_id: {str(e)}")
                
                # 清理：如果临时列存在，删除它
                try:
                    if 'temp_vlan_id' in [c['name'] for c in inspector.get_columns('switch_port_groups')]:
                        migrate_engine.execute('ALTER TABLE switch_port_groups DROP COLUMN temp_vlan_id')
                except Exception as cleanup_error:
                    print(f"Error during cleanup: {str(cleanup_error)}")
                raise
                
        print("Downgrade completed successfully")
        
    except Exception as e:
        print(f"Error during downgrade: {str(e)}")
        raise
