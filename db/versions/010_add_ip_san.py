from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, String, Integer, MetaData, DateTime, ForeignKey, Boolean, create_engine

meta = MetaData()

storage_device = Table(
    'storage_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('device_type', String(64), nullable=False, comment='存储的类型: 1 FC， 2 ISCSI，3 NFS，4 RBD（CEPH） 5.本地目录'),
    <PERSON>umn('device_name', String(100), nullable=False, comment='存储设备名称(区分大小写)'),
    Column('model', String(100), nullable=False, comment='型号'),
    Column('vendor', String(100), nullable=False, comment='厂家'),
    Column('total_capacity', BigInteger, nullable=True, comment='存储总容量,单位:字节(byte)'),
    Column('used_capacity', BigInteger, nullable=True, comment='已使用存储总容量,单位:字节(byte)'),
    Column('ip_mgmt', String(255), nullable=True, comment='管理ip集合(ip间用逗号隔开),如*************,*************'),
    Column('last_scn_time', DateTime(timezone=True), nullable=True, comment='最后扫描的时间'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),
    Column('remark', String(255), nullable=True, comment='备注')
)



# 注意：将 ENUM 类型改为 String，因为 SQLAlchemy Core 不直接支持 ENUM
ip_san_device = Table(
    'ip_san_device', meta,
    Column('id', String(36), primary_key=True, nullable=False, unique=True,  comment='IP SAN 设备主键'),
    Column('ip_san_target_id', String(36), ForeignKey('ip_san_target.id'), nullable=False, comment='traget ID'),
    Column('iscsi_iqn', String(255), nullable=True, comment='iSCSI Qualified Name'),
    Column('authentication_type', String(16), default='none', nullable=True, comment='认证类型: none, CHAP, mutual CHAP'),
    Column('target_count', Integer, default=0, nullable=True, comment='目标数量'),
    Column('max_sessions', Integer, nullable=True, comment='最大会话数'),
    Column('network_type', String(50), nullable=True, comment='网络类型，例如 10GbE, 25GbE'),
    Column('use_jumbo_frames', Boolean, default=False, nullable=True, comment='是否使用巨型帧'),
    Column('status', String(16), nullable=True, comment='状态'),
    Column('mount_type', String(16), nullable=True, comment='挂载类型'),
    Column('mount_point', String(255), nullable=True, comment='挂载点'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间'),

    # LUN 相关信息
    Column('lun_number', String(16), nullable=True, comment='逻辑单元号 (LUN)'),
    Column('device_path', String(255), nullable=True, comment='设备路径，例如 /dev/sda'),
    Column('scsi_address', String(64), nullable=True, comment='SCSI 地址，例如 2:0:0:0'),
    Column('vendor', String(64), nullable=True, comment='设备厂商，例如 FreeNAS'),
    Column('model', String(64), nullable=True, comment='设备型号，例如 iSCSI Disk'),
    Column('revision', String(16), nullable=True, comment='固件版本，例如 0123'),
    Column('state', String(16), nullable=True, comment='设备状态，例如 running'),
    Column('fs_type', String(32), nullable=True, comment='文件系统类型，例如 ext4, xfs'),
    Column('mounted_nodes', String(255), nullable=True, comment='挂载节点，JSON 格式存储多个节点'),
    Column('host_number', String(16), nullable=True, comment='主机适配器编号'),
    Column('channel', String(16), nullable=True, comment='通道编号'),
    Column('capacity', String(64), nullable=True, comment='容量大小，例如 100GB'),
    Column('lun_status', String(16), nullable=True, comment='LUN 状态，例如 online, offline'),
)

ip_san_target = Table(
    'ip_san_target', meta,
    Column('id', String(36), primary_key=True,  nullable=False,unique=True,comment='目标主键'),
    Column('device_id', String(36), ForeignKey('storage_device.id'), nullable=False, comment='设备ID'),
    Column('target_name', String(255), nullable=False, comment='目标名称'),
    Column('target_alias', String(100), nullable=True, comment='目标别名'),
    Column('ip_address', String(50), nullable=False, comment='IP地址'),
    Column('port', Integer, default=3260, nullable=True, comment='端口号'),
    Column('authentication_enabled', Boolean, default=False, nullable=True, comment='是否启用认证'),
    Column('username', String(100), nullable=True, comment='用户名'),
    Column('password', String(255), nullable=True, comment='密码(应加密存储)'),
    Column('status', String(16), nullable=False, comment='状态: active, inactive, error'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(timezone=True), default=datetime.datetime.now, comment='更新时间')
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    ip_san_target.create()
    ip_san_device.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    ip_san_device.drop()
    ip_san_target.drop()
