from sqlalchemy import *
from migrate import *

meta = MetaData()

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    host = Table('host', meta, autoload=True)

    # 新增字段（如果不存在才加）
    if 'role' not in host.c:
        switch_port_id_col = Column('role', String(36), nullable=True)
        switch_port_id_col.create(host)

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    host = Table('host', meta, autoload=True)

    # 删除字段（如果存在才删）
    if 'role' in host.c:
        host.c.role.drop()