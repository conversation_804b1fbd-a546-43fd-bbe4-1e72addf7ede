from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, String, Integer, BigInteger, DateTime, MetaData, create_engine, text, Boolean, Text
import datetime

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    domain = Table('domain', meta, autoload=True)

    # 新建可空字符串列，无默认值
    exporter = Column('exporter', String(36), nullable=True)
    exporter.create(domain)


def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    domain = Table('domain', meta, autoload=True)
    domain.c.exporter.drop()