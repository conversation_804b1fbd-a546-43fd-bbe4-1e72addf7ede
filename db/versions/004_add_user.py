import datetime
from uuid import uuid4

from sqlalchemy import *
from migrate import *
import uuid

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

account = Table(
    'users', meta,
    Column('id', String(64), primary_key=True, comment='id'),
    <PERSON><PERSON><PERSON>('username', String(32), unique=True),
    <PERSON><PERSON><PERSON>('name', String(32)),
    <PERSON><PERSON><PERSON>('role_name', String(32)),
    <PERSON><PERSON><PERSON>('password', String(128)),
    <PERSON><PERSON><PERSON>('session_id', String(128)),
    <PERSON><PERSON><PERSON>('create_at', DateTime()),
    <PERSON>umn('updated_at', DateTime()),
    <PERSON>umn('status', String(16)),
    <PERSON>umn('expiredday', DateTime()),
)

role = Table(
    'role', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('name', String(32), unique=True),
    <PERSON>umn('display_name', String(32), unique=True),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    account.create()
    role.create()
    # 插入数据到 'role' 表
    migrate_engine.execute(role.insert(), [
        {'id': 1, 'name': 'sysadm', 'description': '系统管理员'},
        {'id': 2, 'name': 'adtadm', 'description': '审计管理员'},
        {'id': 3, 'name': 'secadm', 'description': '安全管理员'},
        {'id': 4, 'name': 'operator', 'description': '操作员'},
        {'id': 5, 'name': 'supadm', 'description': '超级管理员'}
    ])

    # 计算100年后的时间
    # 获取当前时间
    now = datetime.datetime.now()
    expiredday = now + datetime.timedelta(days=1000 * 365)  # 假设非闰年365天
    # 插入数据到 'users' 表
    migrate_engine.execute(account.insert(), [
        {'id': str(uuid4()), 'username': 'sysadm', 'name': 'sysadm',
         'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'sysadm',
         'status': 'on', 'create_at': now, 'updated_at': now, 'expiredday': expiredday},
        {'id': str(uuid4()), 'username': 'adtadm', 'name': 'adtadm',
         'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'adtadm',
         'status': 'on', 'create_at': now, 'updated_at': now, 'expiredday': expiredday},
        {'id': str(uuid4()), 'username': 'secadm', 'name': 'secadm',
         'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'secadm',
         'status': 'on', 'create_at': now, 'updated_at': now, 'expiredday': expiredday},
        {'id': str(uuid4()), 'username': 'supadm', 'name': 'supadm',
         'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'supadm',
         'status': 'on', 'create_at': now, 'updated_at': now, 'expiredday': expiredday}
    ])


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    account.drop()
    role.drop()
    # 删除 'role' 表中指定的记录
    migrate_engine.execute(role.delete().where(role.c.id.in_([1, 2, 3, 4])))

    # 删除 'users' 表中指定的记录
    migrate_engine.execute(account.delete().where(account.c.username.in_(['sysadm', 'adtadm', 'secadm'])))
