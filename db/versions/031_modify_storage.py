"""modify storage add pid field for tree relationship and use_type field

Revision ID: 031_modify_storage
Revises: 030_previous_migration
Create Date: 2024-01-15 10:00:00.000000

"""
from migrate import *
import sqlalchemy as sa
from migrate.changeset import schema


# revision identifiers, used by migrate.
revision = '031_modify_storage'
down_revision = '030_previous_migration'  # 请根据实际情况修改为上一个迁移的 ID
branch_labels = None
depends_on = None


def upgrade(migrate_engine):
    """添加 pid 字段用于实现存储卷的树形关系，添加 use_type 字段表示卷的用途"""

    # 获取元数据
    meta = sa.MetaData(bind=migrate_engine)

    # 获取 storage_volume 表
    storage_volume = sa.Table('storage_volume', meta, autoload=True)

    # 为 storage_volume 表添加 pid 字段
    pid_column = sa.Column('pid', sa.String(36), nullable=True, comment='父存储卷ID，用于树形关系')
    pid_column.create(storage_volume)

    # 为 storage_volume 表添加 use_type 字段
    use_type_column = sa.Column('use_type', sa.String(32), nullable=True, default='data', comment='存储卷用途：data, backup, snapshot, export等')
    use_type_column.create(storage_volume)

    # 添加索引以提高查询性能
    pid_idx = sa.Index('idx_storage_volume_pid', storage_volume.c.pid)
    pid_idx.create(migrate_engine)
    
    use_type_idx = sa.Index('idx_storage_volume_use_type', storage_volume.c.use_type)
    use_type_idx.create(migrate_engine)

    # 注意：外键约束和检查约束在某些情况下可能有问题，先只添加字段和索引
    print("Successfully added pid and use_type columns with indexes to storage_volume table")


def downgrade(migrate_engine):
    """回滚操作：删除 pid 和 use_type 字段及相关约束"""

    # 获取元数据
    meta = sa.MetaData(bind=migrate_engine)

    # 获取 storage_volume 表
    storage_volume = sa.Table('storage_volume', meta, autoload=True)

    # 删除索引
    try:
        pid_idx = sa.Index('idx_storage_volume_pid', storage_volume.c.pid)
        pid_idx.drop(migrate_engine)
    except Exception as e:
        print(f"Warning: Could not drop pid index: {e}")
    
    try:
        use_type_idx = sa.Index('idx_storage_volume_use_type', storage_volume.c.use_type)
        use_type_idx.drop(migrate_engine)
    except Exception as e:
        print(f"Warning: Could not drop use_type index: {e}")

    # 删除 use_type 字段
    try:
        storage_volume.c.use_type.drop()
        print("Successfully removed use_type column from storage_volume table")
    except Exception as e:
        print(f"Error: Could not drop use_type column: {e}")

    # 删除 pid 字段
    try:
        storage_volume.c.pid.drop()
        print("Successfully removed pid column from storage_volume table")
    except Exception as e:
        print(f"Error: Could not drop pid column: {e}")