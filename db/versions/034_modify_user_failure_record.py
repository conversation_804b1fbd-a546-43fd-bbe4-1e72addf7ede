from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, String, Integer, BigInteger, DateTime, MetaData, create_engine, text, Boolean, Text
import datetime

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    login_failure_records = Table('login_failure_records', meta, autoload=True)


def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    login_failure_records = Table('login_failure_records', meta, autoload=True)
    login_failure_records.c.exporter.drop()